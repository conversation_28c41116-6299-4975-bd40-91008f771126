{"name": "aktualnitesty", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "build:css-watch": "npx tailwindcss -i ./src/app/globals.css -o ./public/output.css --watch", "build:css": "npx tailwindcss -i ./src/app/globals.css -o ./public/output.css", "graphql:generate-types": "npm run apollo:download && npm run apollo:generate", "apollo:download": "rover graph introspect https://local-admin.aktualnitesty.cz/graphql/ --insecure-accept-invalid-certs > src/graphql/schema.graphql", "apollo:generate": "rm -rf src/graphql/types && apollo codegen:generate --localSchemaFile=src/graphql/schema.graphql --target=typescript --tagName=gql --outputFlat --excludes=src/graphql/* src/graphql/types", "pretty": "prettier --write './**/*.{js,ts,tsx}'", "pretty:check": "prettier --check './**/*.{js,ts,tsx}'"}, "dependencies": {"@apollo/client": "^3.13.5", "@react-oauth/google": "^0.12.1", "chart.js": "^4.4.8", "dotenv": "^16.4.5", "graphql": "^16.9.0", "lucide-react": "^0.483.0", "next": "15.1.7", "next-auth": "^4.24.11", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-toastify": "^11.0.2", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.20", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8.4.49", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.16", "typescript": "^5"}}