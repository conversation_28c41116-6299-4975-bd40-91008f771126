build_string := DOCKER_DEFAULT_PLATFORM=linux/arm64 docker compose -f docker-compose.yml -f compose.override.yaml
run_string:= docker exec docker-ci-web

build:
	$(run_string) npm install

dev-start:
	$(build_string) up -d

dev-stop:
	$(build_string) stop

dev-clean:
	$(build_string) down

# npm run build - nelze spoustet pres container, protoze uz je zbuildovany pro dev.
prod-build:
	docker compose -f docker-compose.yml build
dev-build:
	docker compose -f docker-compose.yml -f compose.override.yaml build

dev-restart: dev-stop dev-start

dev-rebuild:
	make dev-clean \
	&& docker rmi webzkousky-docker-ci-web \
	&& make dev-build \
	&& make dev-start

generate-graphql-types:
	$(run_string) npm run graphql:generate-types

dbg-console:
	$(build_string) exec docker-ci-web sh -c "bash"

cs-fix:
	$(run_string) npm run pretty

copy-node-modules-to-local:
	docker cp seduo-company_nodejs:/usr/app/node_modules/ .

