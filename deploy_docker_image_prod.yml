.docker_template: &template
  tags:
    - aws-ec2-arm.t4g.medium

deploy_docker_image:
  <<: *template
  stage: deploy
  resource_group: production
  before_script:
    - cat $next_ec2_key > key.pem
    - chmod 600 key.pem
    - ssh -i key.pem -o StrictHostKeyChecking=no ec2-user@************ "sudo docker rm -v -f docker-ci-web || true"
    - ssh -i key.pem -o StrictHostKeyChecking=no ec2-user@************ "sudo docker volume ls -qf dangling=true | xargs -r sudo docker volume rm"
    - ssh -i key.pem -o StrictHostKeyChecking=no ec2-user@************ "sudo docker rm $(sudo docker ps -a -f status=exited -q) || true"
    - ssh -i key.pem -o StrictHostKeyChecking=no ec2-user@************ "sudo docker volume rm $(sudo docker volume ls -q -f name=runner-) || true"
  script:
    - echo "Deploying the project..."
    - ssh -i key.pem -o StrictHostKeyChecking=no ec2-user@************ "sudo docker login registry.gitlab.com -u hrdlickama -p $NEXT_TOKEN"
    - ssh -i key.pem -o StrictHostKeyChecking=no ec2-user@************ "sudo docker run -d --name=docker-ci-web --network docker-ci-network -p 3000:3000 registry.gitlab.com/hrdlicka/webzkousky:next-$CI_PIPELINE_IID"
    # Lze i rucne spustit -  docker images | grep next- | awk 'NF {print $3, $2}' | sort -t '-' -k2 -nr | tail -n +3 | awk '{print $1}' | xargs -r docker rmi"
    - ssh -i key.pem -o StrictHostKeyChecking=no ec2-user@************ "sudo docker images | grep next- | awk 'NF {print \$3, \$2}' | sort -t '-' -k2 -nr | tail -n +3 | awk '{print \$1}' | xargs -r sudo docker rmi"

  environment:
    name: dev
    url: http://************
