// Minimal Service Worker for PWA installation support
const CACHE_NAME = "aktualni-testy-pwa-v6";
const STATIC_ASSETS = [
  "/manifest.json",
  "/icons/favicon.ico",
  "/icons/icon-72x72.png",
  "/icons/icon-96x96.png",
  "/icons/icon-128x128.png",
  "/icons/icon-144x144.png",
  "/icons/icon-152x152.png",
  "/icons/icon-192x192.png",
  "/icons/icon-192x192-maskable.png",
  "/icons/icon-384x384.png",
  "/icons/icon-512x512.png",
  "/icons/icon-512x512-maskable.png",
  "/icons/apple-icon-120x120.png",
  "/icons/apple-icon-152x152.png",
  "/icons/apple-icon-180x180.png",
];

// Install handler - cache minimum required assets for PWA
self.addEventListener("install", (event) => {
  self.skipWaiting();

  event.waitUntil(
    caches
      .open(CACHE_NAME)
      .then((cache) => {
        return cache.addAll(STATIC_ASSETS);
      })
      .catch((error) => {
        console.error("Service worker cache installation failed:", error);
      }),
  );
});

// Activate handler - clean up old caches
self.addEventListener("activate", (event) => {
  event.waitUntil(clients.claim());

  // Remove old cache versions
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames
          .filter((cacheName) => cacheName !== CACHE_NAME)
          .map((cacheName) => caches.delete(cacheName)),
      );
    }),
  );
});

// Fetch handler with extremely minimal caching
self.addEventListener("fetch", (event) => {
  // Skip if not a GET request
  if (event.request.method !== "GET") {
    return;
  }

  try {
    const url = new URL(event.request.url);

    // Skip if not the same origin or chrome-extension protocol
    if (
      url.origin !== location.origin ||
      url.protocol === "chrome-extension:"
    ) {
      return;
    }

    // Only handle image/CSS/icon assets - NEVER handle HTML pages or data
    const isAsset =
      url.pathname.match(/\.(css|png|jpg|jpeg|gif|svg|ico)$/) ||
      url.pathname === "/manifest.json";

    if (!isAsset) {
      // Let browser handle normally for all non-assets
      return;
    }

    // Only for essential PWA assets - cache-first strategy
    event.respondWith(
      caches.match(event.request).then((cachedResponse) => {
        if (cachedResponse) {
          return cachedResponse;
        }

        return fetch(event.request)
          .then((response) => {
            if (!response || response.status !== 200) {
              return response;
            }

            // Cache only a limited set of file types
            const clonedResponse = response.clone();

            // The browser will ensure we don't cache chrome-extension URLs
            caches
              .open(CACHE_NAME)
              .then((cache) => {
                try {
                  // Double-check it's a valid scheme before caching
                  const responseUrl = new URL(response.url);
                  if (
                    responseUrl.protocol === "http:" ||
                    responseUrl.protocol === "https:"
                  ) {
                    cache.put(event.request, clonedResponse);
                  }
                } catch (error) {
                  console.error("Caching failed:", error);
                }
              })
              .catch((error) => {
                console.error("Cache open failed:", error);
              });

            return response;
          })
          .catch((error) => {
            console.error("Fetch failed:", error);
            throw error;
          });
      }),
    );
  } catch (error) {
    console.error("Service worker fetch handler error:", error);
    return;
  }
});
