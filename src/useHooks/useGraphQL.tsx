import { ApolloClient, from, HttpLink, InMemoryCache } from "@apollo/client";

export const useGraphQL = () => {
  process.env["NODE_TLS_REJECT_UNAUTHORIZED"] =
    process.env.NODE_ENV === "development" ? "0" : "";
  /* Na localu si toto lze odkomentovat a vypnout tim overeni na self-assigned certifikat
    process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = "0";
  */
  process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = "0";

  const httpLink = new HttpLink({
    uri: process.env.NEXT_PUBLIC_GRAPHQL_HOST,
    headers: {
      authorization: `Basic ${btoa("root:password123")}`,
    },
  });

  return new ApolloClient({
    ssrMode: typeof window === "undefined",
    link: from([httpLink]),
    cache: new InMemoryCache(),
  });
};
