import { User as Next<PERSON><PERSON><PERSON>ser, Session as NextAuthSession } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import { gql } from "@apollo/client";
import { getClient } from "@/lib/client";
import type { NextAuthOptions } from "next-auth";

interface CustomUser extends NextAuthUser {
  token: string;
}

interface CustomSession extends NextAuthSession {
  user: {
    name?: string | null;
    email?: string | null;
    image?: string | null;
    token: string;
  };
}
const LOGIN_USER = gql`
  mutation Login($email: String!, $password: String!) {
    login(username: $email, password: $password)
  }
`;

const LOGIN_BY_HASH = gql`
  mutation LoginByHash($loginHash: String!) {
    loginGoogleEmail(loginHash: $loginHash)
  }
`;

const client = getClient();

export const authConfig = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "text" },
        password: { label: "Password", type: "password" },
      },
      authorize: async (
        credentials: Record<"email" | "password", string> | undefined,
      ) => {
        const { data } = await client.mutate({
          mutation: LOGIN_USER,
          variables: {
            email: credentials?.email,
            password: credentials?.password,
          },
        });

        if (data.login) {
          return {
            id: "random-id",
            email: credentials?.email,
            name: "custom username",
            token: data.login,
          };
        } else {
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, account }) {
      if (user) {
        token.token = (user as CustomUser).token;
      }

      if (account?.provider === "google") {
        const { data } = await client.mutate({
          mutation: LOGIN_BY_HASH,
          variables: {
            loginHash: btoa(user?.email || ""),
          },
        });

        token.token = data.loginGoogleEmail;
      }

      return token;
    },
    async session({ session, token }) {
      (session as CustomSession).user.token = token.token as string;

      //session.user = { ...session.user, id: token.id, email: token.email, name: token.name };
      return session;
    },
    // TADY lze overit, napr. jestli je email_verified
    //async signIn({ account, profile }) {
    //  if (account?.provider === "google") {
    //    return profile.email_verified
    //  }
    //  return true // Do different verification for other providers that don't have `email_verified`
    //},
  },
  pages: {
    signIn: "/prihlaseni",
  },
  session: {
    strategy: "jwt",
  },
  secret: process.env.NEXTAUTH_SECRET,
} satisfies NextAuthOptions;
