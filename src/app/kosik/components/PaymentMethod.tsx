import React, { ChangeEvent } from "react";
import { PaymentMethodTypes_paymentMethodTypes_paymentMethodType } from "@/graphql/types/PaymentMethodTypes";
import Image from "next/image";
import { useCart } from "@/context/CartContext";

type PaymentMethodProps = {
  state: {
    success: boolean;
    message: string;
    inputs?: {
      paymentMethod: number;
    };
    errors?: {
      paymentMethod?: string[];
    };
  };
  handleChange: (e: ChangeEvent<HTMLInputElement>) => void;
  paymentMethods: PaymentMethodTypes_paymentMethodTypes_paymentMethodType[];
};

export const PaymentMethod: React.FC<PaymentMethodProps> = ({
  state,
  handleChange,
  paymentMethods,
}) => {
  const { paymentMethod } = useCart();

  return (
    <div className="bg-white rounded-lg shadow-md p-6 border-t-2">
      <h2 className="text-xl font-semibold mb-4">Způsob platby</h2>
      <div className="space-y-4">
        {paymentMethods.map((paymentMethodType) => (
          <div key={paymentMethodType.id} className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <h3 className="font-medium">{paymentMethodType.name}</h3>
              <p className="text-sm text-gray-400">
                {paymentMethodType.description}
              </p>
            </div>
            <div className="space-y-2">
              {paymentMethodType.paymentMethods.map((method) => (
                <div
                  key={method.id}
                  className="flex items-center pl-4 py-2 hover:bg-gray-50 rounded-md border border-gray-100 mb-2"
                >
                  <input
                    type="radio"
                    id={`payment_${method.id}`}
                    name="paymentMethod"
                    value={method.id}
                    checked={paymentMethod.id === method.id}
                    onChange={handleChange}
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 cursor-pointer"
                  />
                  <label
                    htmlFor={`payment_${method.id}`}
                    className="flex items-center cursor-pointer w-full"
                  >
                    {method.imageUrl && (
                      <Image
                        src={`/img/payments/${method.imageUrl}`}
                        width={82}
                        height={40}
                        priority={true}
                        alt={method.name}
                        className="h-8 mr-3 object-contain"
                        style={{ width: "auto", height: "auto" }}
                      />
                    )}
                    <span className="text-gray-700">{method.name}</span>
                  </label>
                </div>
              ))}
            </div>
          </div>
        ))}
        {state?.errors?.paymentMethod && (
          <div
            className="h-8 flex items-center justify-center bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
            role="alert"
            aria-live="polite"
            aria-atomic="true"
          >
            <p className="text-sm">{state.errors.paymentMethod[0]}</p>
          </div>
        )}
      </div>
    </div>
  );
};
