import Link from "next/link";
import React from "react";
import { ShoppingCart, ArrowLeft } from "lucide-react";

export const CartEmpty = () => (
  <div className="bg-white rounded-lg shadow-md p-8 md:p-12 min-h-[60vh] flex flex-col items-center justify-center text-center">
    <div className="mb-6 bg-gray-100 p-6 rounded-full">
      <ShoppingCart className="w-16 h-16 text-gray-400" />
    </div>

    <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-3">
      V<PERSON><PERSON> ko<PERSON> je prázdný
    </h2>
    <p className="text-gray-600 mb-8 max-w-md">
      Vypadá to, že jste si zatím nepřidali žádné produkty do košíku.
      Prozkoumejte naši nabídku a najděte testy, kter<PERSON> vám pomohou s přípravou.
    </p>

    <Link
      href="/"
      className="flex items-center justify-center gap-2 px-6 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors shadow-md hover:shadow-lg"
    >
      <ArrowLeft className="w-5 h-5" />
      <span>Pokračovat v nákupu</span>
    </Link>

    <div className="mt-12 pt-8 border-t border-gray-200 w-full max-w-md">
      <p className="text-sm text-gray-500">
        Potřebujete pomoc s výběrem? Kontaktujte nás na{" "}
        <a
          href="mailto:<EMAIL>"
          className="text-emerald-600 hover:text-emerald-700"
        >
          <EMAIL>
        </a>
      </p>
    </div>
  </div>
);
