import React from "react";
import { CartItem } from "@/context/CartContext";

type CartTableProps = {
  items: CartItem[];
  removeItem: (id: string) => void;
  totalPrice: number;
};

export const CartTable: React.FC<CartTableProps> = ({
  items,
  removeItem,
  totalPrice,
}) => (
  <div className="bg-white rounded-lg shadow-md overflow-hidden">
    <table className="w-full">
      <thead className="bg-gray-100">
        <tr>
          <th className="px-6 py-3 text-left">Název</th>
          <th className="px-6 py-3 text-right">
            Cena <span className="text-xs text-gray-900">vč. DPH</span>
          </th>
          <th className="px-6 py-3 text-center"></th>
        </tr>
      </thead>
      <tbody>
        {items.map((item) => (
          <tr key={item.id} className="border-t">
            <td className="px-6 py-4">{item.name}</td>
            <td className="px-6 py-4 text-right">{item.price} Kč</td>
            <td className="px-6 py-4 text-center">
              <button
                type="button"
                onClick={() => removeItem(item.id)}
                className="text-red-500 hover:text-red-700"
              >
                Odstranit
              </button>
            </td>
          </tr>
        ))}
      </tbody>
      <tfoot className="bg-gray-50">
        <tr>
          <td className="px-6 py-4 font-bold">Celkem</td>
          <td className="px-6 py-4 text-right font-bold">{totalPrice} Kč</td>
          <td></td>
        </tr>
      </tfoot>
    </table>
  </div>
);
