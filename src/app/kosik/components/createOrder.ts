"use server";

import { ActionResponse, CartFormData } from "@/types/cart";
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth.config";
import { ExtendedDefaultSession } from "@/types/extendedDefaultSession";
import { ApolloError, gql } from "@apollo/client";
import { mappedValidationError } from "@/app/kosik/components/validationApiErrorMapper";
import { CartItem } from "@/context/CartContext";
import { getClient } from "@/lib/client";
import { CreateOrder, CreateOrderVariables } from "@/graphql/types/CreateOrder";

const CREATE_ORDER_MUTATION = gql`
  mutation CreateOrder($paymentData: PaymentDataInput!) {
    createOrder(paymentData: $paymentData) {
      gatewayUrl
      embedJs
      status
    }
  }
`;

const mapItemsToIds = (items: CartItem[]): string[] =>
  items.map((item) => item.id);

export const createOrder = async (
  items: CartItem[],
  paymentMethod: number,
  rawData: CartFormData,
): Promise<ActionResponse> => {
  try {
    const session = (await getServerSession(
      authConfig,
    )) as ExtendedDefaultSession;

    const itemIds = mapItemsToIds(items);

    const client = getClient();
    const { data } = await client.mutate<CreateOrder, CreateOrderVariables>({
      mutation: CREATE_ORDER_MUTATION,
      variables: {
        paymentData: {
          items: itemIds,
          paymentMethodId: paymentMethod,
        },
      },
      context: {
        headers: {
          Authorization: `Bearer ${session?.user?.token}`,
        },
      },
    });

    return {
      success: true,
      message: "Objednávka úspěšně vytvořena",
      createdOrder: data?.createOrder,
    };
  } catch (e: unknown) {
    if (e instanceof ApolloError) {
      return mappedValidationError(e.message, rawData);
    }
  }

  return {
    success: true,
    message: "User validated successfully!",
  };
};
