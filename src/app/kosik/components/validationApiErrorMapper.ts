"use server";

import { FORM_GLOBAL_ERROR_MESSAGE } from "@/app/registrace/constants/translations";
import { CartFormData } from "@/types/cart";

export const mappedValidationError = async (
  message: string,
  rawData: CartFormData,
) => {
  const validationMap: { [key: string]: string } = {
    "Payment method validation": "paymentMethod",
  };

  for (const [key, value] of Object.entries(validationMap)) {
    if (message.startsWith(key)) {
      return {
        success: false,
        message: FORM_GLOBAL_ERROR_MESSAGE,
        errors: { [value]: [message.replace(`${key}: `, "")] },
        inputs: rawData,
      };
    }
  }

  return {
    success: false,
    message,
    inputs: rawData,
  };
};
