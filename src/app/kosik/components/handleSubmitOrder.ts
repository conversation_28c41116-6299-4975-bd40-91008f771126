"use client";

import { ActionResponse, CartFormData, CreatedOrder } from "@/types/cart";
import { CartItem } from "@/context/CartContext";
import { validateOrder } from "@/app/kosik/components/validateOrder";
import { createOrder } from "@/app/kosik/components/createOrder";

const handleGopayRedirect = (gopayData: CreatedOrder) => {
  if (gopayData.status === "success" && gopayData.gatewayUrl) {
    // Před přesměrováním vyčistíme košík
    localStorage.removeItem("cart");
    // Přesměrování na GoPay bránu
    window.location.href = gopayData.gatewayUrl;
  }
};

export const handleSubmitOrder = async (
  prevState: ActionResponse | null,
  formData: FormData,
): Promise<ActionResponse> => {
  const rawData: CartFormData = {
    paymentMethod: Number(formData.get("paymentMethod")) || 0,
  };

  const validatedData = await validateOrder(rawData);

  if (!validatedData.success) {
    return validatedData;
  }

  const cartItems: CartItem[] = JSON.parse(
    localStorage.getItem("cart") || "[]",
  );

  const order = await createOrder(
    cartItems,
    validatedData.inputs?.paymentMethod as number,
    validatedData.inputs as CartFormData,
  );

  if (order?.success) {
    if (!order.createdOrder) {
      return {
        success: false,
        message: "Objednávka nebyla vytvořena.",
      };
    }

    handleGopayRedirect(order.createdOrder);
  }

  return order;
};
