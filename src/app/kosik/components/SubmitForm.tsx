import React from "react";
import Link from "next/link";
import { CreditCard, Loader2 } from "lucide-react";

type CustomerFormProps = {
  state: {
    success: boolean;
    message: string;
    inputs?: {
      paymentMethod: number;
    };
    errors?: {
      paymentMethod?: string[];
    };
  };
  isPending: boolean;
};

export const SubmitForm: React.FC<CustomerFormProps> = ({
  state,
  isPending,
}) => (
  <div className="bg-white rounded-lg shadow-md p-6 border-t-2">
    <div className="text-sm text-gray-600 mb-4 p-3 bg-gray-50 rounded-md border border-gray-200">
      <p>
        Odesláním objednávky souhlasíte s{" "}
        <Link
          href="/vop"
          className="text-blue-600 hover:text-blue-800 font-medium underline"
        >
          obchodn<PERSON><PERSON> podmínkami
        </Link>{" "}
        a zavazujete se k úhradě celkové č<PERSON>.
      </p>
    </div>
    {!state?.success && state?.message && (
      <div
        className="rounded-lg bg-red-50 p-3 mb-4"
        role="alert"
        aria-live="polite"
      >
        <p className="text-sm text-red-600">{state.message}</p>
      </div>
    )}
    <button
      type="submit"
      disabled={isPending}
      className="group/button flex w-full items-center justify-center gap-2 px-6 py-2.5 bg-gradient-to-br from-blue-500 to-blue-600 text-white font-medium rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-70 disabled:cursor-not-allowed"
    >
      {isPending ? (
        <>
          <Loader2 className="w-5 h-5 animate-spin" />
          <span>Odesílání...</span>
        </>
      ) : (
        <>
          <CreditCard className="w-5 h-5 text-blue-200" />
          <span className="group-hover/button:translate-x-0.5 transition-transform duration-150">
            Přejít k platbě
          </span>
        </>
      )}
    </button>
  </div>
);
