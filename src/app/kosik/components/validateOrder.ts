"use server";

import { z } from "zod";
import { ActionResponse, CartFormData } from "@/types/cart";
import { FORM_GLOBAL_ERROR_MESSAGE } from "@/app/registrace/constants/translations";

const orderSchema = z.object({
  paymentMethod: z.number().min(1, "<PERSON>p<PERSON>so<PERSON> platby je povinný"),
});

export const validateOrder = async (
  rawData: CartFormData,
): Promise<ActionResponse> => {
  const validatedData = orderSchema.safeParse(rawData);

  if (!validatedData.success) {
    return {
      success: false,
      message: FORM_GLOBAL_ERROR_MESSAGE,
      errors: validatedData.error.flatten().fieldErrors,
      inputs: rawData,
    };
  }

  return {
    success: true,
    message: "Objednávka validována úspěšně",
    inputs: validatedData.data,
  };
};
