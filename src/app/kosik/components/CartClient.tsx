"use client";

import React, { ChangeEvent, useActionState, useEffect, useState } from "react";
import { useCart } from "@/context/CartContext";
import { handleSubmitOrder } from "@/app/kosik/components/handleSubmitOrder";
import { handleFormStateChange } from "@/utils/handleFormStateChange";
import { CartEmpty } from "@/app/kosik/components/CartEmpty";
import { CartLoading } from "@/app/kosik/components/CartLoading";
import { CartTable } from "@/app/kosik/components/CartTable";
import { PaymentMethod } from "@/app/kosik/components/PaymentMethod";
import { SubmitForm } from "@/app/kosik/components/SubmitForm";
import { PaymentMethodTypes_paymentMethodTypes_paymentMethodType } from "@/graphql/types/PaymentMethodTypes";
import { Applications_applications_application } from "@/graphql/types/Applications";

type ActionResponse = {
  success: boolean;
  message: string;
  inputs?: {
    paymentMethod: number;
  };
  errors?: {
    paymentMethod?: string[];
  };
};

const initialState: ActionResponse = {
  success: false,
  message: "",
  inputs: {
    paymentMethod: 1,
  },
  errors: {},
};

type CartClientProps = {
  paymentMethods: PaymentMethodTypes_paymentMethodTypes_paymentMethodType[];
  applications: Applications_applications_application[];
};

const CartClient: React.FC<CartClientProps> = ({
  paymentMethods,
  applications,
}) => {
  const {
    items,
    removeItem,
    totalPrice,
    updatePaymentMethod,
    updateApplications,
  } = useCart();
  const [state, action, isPending] = useActionState<ActionResponse, FormData>(
    handleSubmitOrder,
    initialState,
  );
  const [isCartLoading, setIsCartLoading] = useState(true);
  const [, forceUpdate] = useState(0);

  useEffect(() => {
    updateApplications(applications);

    const timer = setTimeout(() => {
      setIsCartLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, [applications, updateApplications]);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (name === "paymentMethod") {
      updatePaymentMethod(Number(value));
    }

    handleFormStateChange(name, state);
    forceUpdate((prev) => prev + 1);
  };

  if (isCartLoading) {
    return <CartLoading />;
  }

  if (items.length === 0) {
    return <CartEmpty />;
  }

  return (
    <form
      action={action}
      className="flex flex-col space-y-6 w-full max-w-3xl mx-auto"
    >
      <CartTable
        items={items}
        removeItem={removeItem}
        totalPrice={totalPrice}
      />
      <PaymentMethod
        state={state}
        handleChange={handleChange}
        paymentMethods={paymentMethods}
      />
      <SubmitForm state={state} isPending={isPending} />
    </form>
  );
};

export default CartClient;
