"use server";

import React from "react";
import Menu from "@/components/Menu/Menu";
import CartClient from "./components/CartClient";
import { getPaymentMethodTypes } from "@/lib/services/payments";
import { getApplications } from "@/lib/services/applications";

export default async function CartPage() {
  const paymentMethods = await getPaymentMethodTypes();
  const applications = await getApplications();

  return (
    <>
      <Menu application={undefined} />
      <div className="app-layout min-h-screen">
        <div className="container mx-auto pt-20 lg:pt-32 pb-20 max-w-3xl">
          <div className="flex flex-col items-center mb-8">
            <h1 className="text-3xl font-bold text-center">
              Dokončen<PERSON> obje<PERSON>ky
            </h1>
            <div className="w-16 h-1 bg-blue-500 mt-2"></div>
          </div>
          <CartClient
            paymentMethods={paymentMethods}
            applications={applications}
          />
        </div>
      </div>
    </>
  );
}
