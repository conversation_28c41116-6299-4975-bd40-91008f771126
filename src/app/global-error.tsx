"use client";

import React from "react";
import Link from "next/link";
import { Home, RefreshCw } from "lucide-react";
import "./globals.css";

interface ErrorProps {
  reset: () => void;
}

export default function GlobalError({ reset }: ErrorProps) {
  return (
    <html>
      <body>
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
          {/* Animated background elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute w-full h-full bg-grid opacity-10"></div>
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-red-100 rounded-full mix-blend-multiply filter blur-xl opacity-70"></div>
            <div className="absolute top-60 -left-20 w-80 h-80 bg-orange-50 rounded-full mix-blend-multiply filter blur-xl opacity-70"></div>
          </div>

          <div className="relative z-10 min-h-screen flex items-center justify-center px-6">
            <div className="text-center max-w-2xl mx-auto">
              {/* 500 text */}
              <div className="relative">
                <h1 className="text-[100px] sm:text-[150px] font-bold text-gray-900">
                  500
                </h1>
              </div>

              {/* Obsah */}
              <div className="mt-8 space-y-6">
                <h2 className="text-3xl font-semibold text-gray-700">
                  Kritická chyba aplikace
                </h2>
                <p className="text-gray-600 max-w-md mx-auto">
                  Omlouváme se, ale došlo ke kritické chybě aplikace. Zkuste
                  obnovit stránku nebo se vraťte na hlavní stránku.
                </p>

                {/* Tlačítka */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
                  <button
                    onClick={reset}
                    className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200 shadow-sm hover:shadow"
                  >
                    <RefreshCw className="mr-2 h-5 w-5" />
                    Obnovit aplikaci
                  </button>

                  <Link
                    href="/"
                    className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 transition-all duration-200 shadow-md hover:shadow-lg"
                  >
                    <Home className="mr-2 h-5 w-5" />
                    Zpět na hlavní stránku
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
