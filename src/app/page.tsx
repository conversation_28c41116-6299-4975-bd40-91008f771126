import React from "react";
import { Metadata } from "next";
import CategoriesList from "@/components/CategoriesList";
import Menu from "@/components/Menu";
import { getApplications } from "@/lib/services/applications";
import UserAccessComparisonTable from "@/components/UserAccessComparisonTable";
import TestModeExplanation from "@/components/TestModeExplanation/TestModeExplanation";
import { ArrowRight } from "lucide-react";
import dynamic from "next/dynamic";

const AnimatedSection = dynamic(
  () => import("@/components/AnimatedSection/AnimatedSection"),
);

const InteractiveTest = dynamic(
  () => import("@/components/InteractiveTest/InteractiveTest"),
);

export const metadata: Metadata = {
  title: "Autoškola testy online - procvičování otázek zdarma",
  description: "Procvičte si aktuální otázky pro autoškolu online a zdarma",
};

export default async function Home() {
  const applications = await getApplications();

  return (
    <div className="relative min-h-screen w-full bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Background grid pattern */}
      <div className="absolute inset-0 w-full h-full bg-grid opacity-10 pointer-events-none"></div>

      {/* Menu */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200/20">
        <Menu application={undefined} />
      </div>

      {/* Content wrapper */}
      <div className="relative w-full">
        {/* Hero Section */}
        <div className="relative min-h-screen flex items-center">
          {/* Animated background elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className="absolute -top-40 -left-40 w-80 h-80 bg-emerald-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
            <div className="absolute -bottom-40 left-20 w-80 h-80 bg-emerald-50 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
          </div>

          {/* Main content */}
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-32">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
              {/* Left column - Text content */}
              <div className="text-center lg:text-left bg-white/50 backdrop-blur-sm rounded-2xl p-6">
                <AnimatedSection
                  staticText="Připravte se na testy"
                  words={[
                    "jednoduše online",
                    "Autoškola",
                    "Zbrojní průkaz",
                    "Potápěč",
                    "Kapitán lodi",
                    "Strážník",
                  ]}
                  typingSpeed={100}
                  deletingSpeed={50}
                  delayBetweenWords={2000}
                  description="Procvičujte aktuální testové otázky kdykoliv a kdekoliv. Připravili jsme pro vás přehledné prostředí s okamžitou zpětnou vazbou."
                />

                {/* CTA Buttons */}
                <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                  <a
                    href="#vyber-testu"
                    className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-xl
                      text-white bg-emerald-600 hover:bg-emerald-700
                      transition-all duration-300
                      shadow-xl hover:shadow-2xl"
                  >
                    Začít zdarma
                    <ArrowRight className="ml-2 -mr-1 w-5 h-5" />
                  </a>

                  <a
                    href="#rezimy-testovani"
                    className="inline-flex items-center px-8 py-3 border bg-white border-emerald-200 text-base font-medium rounded-xl
                      text-emerald-700 hover:bg-emerald-50
                      transition-all duration-300
                      shadow-xl hover:shadow-2xl"
                  >
                    Jak to funguje ?
                  </a>
                </div>
              </div>

              {/* Right column - Interactive Features */}
              <div className="relative hidden md:block">
                <InteractiveTest />
              </div>
            </div>
          </div>

          {/* Scroll indicator */}
          <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 flex flex-col items-center animate-fade-in-up z-20 hidden sm:flex">
            <span className="text-lg font-semibold mb-4 bg-gradient-to-r from-emerald-500 to-blue-500 bg-clip-text text-transparent">
              Scrollujte dolů
            </span>
            <div className="w-10 h-16 border-3 border-emerald-400/80 rounded-full flex justify-center p-2 relative group transition-all duration-300 hover:border-emerald-500">
              <div className="w-2.5 h-2.5 bg-emerald-400 rounded-full animate-scroll-bounce group-hover:bg-emerald-500"></div>
              <div className="absolute -inset-0.5 bg-emerald-400/10 rounded-full blur-md group-hover:bg-emerald-400/20 transition-all duration-300"></div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="relative">
          {/* Dekorativní prvek */}
          <div className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80">
            <div className="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-blue-500 to-teal-500 opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"></div>
          </div>

          {/* Section with CategoriesList */}
          <div
            id="vyber-testu"
            className="container mx-auto px-3 sm:px-6 py-12 sm:py-16"
          >
            <div className="mb-8 sm:mb-16">
              <h2 className="text-2xl sm:text-3xl font-bold text-center text-gray-900 sm:text-4xl">
                Vyberte si oblast testů
              </h2>
              <p className="mt-3 sm:mt-4 text-center text-base sm:text-lg text-gray-600">
                Nabízíme testy z různých oblastí. Vyberte si tu svou a začněte s
                přípravou ještě dnes.
              </p>
            </div>

            {/* Highlighted CategoriesList section */}
            <div className="relative mb-16 sm:mb-24">
              {/* Decorative elements */}
              <div className="absolute -inset-4 bg-gradient-to-r from-emerald-50 via-blue-50 to-emerald-50 rounded-3xl -z-10"></div>
              <div className="absolute -inset-0.5 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-3xl opacity-10 blur-lg -z-10"></div>

              {/* Main content with subtle border */}
              <div className="relative rounded-2xl p-6 sm:p-8 border-2 border-emerald-100 bg-white shadow-sm">
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-emerald-600 text-white px-6 py-1 rounded-full text-sm font-medium whitespace-nowrap shadow-sm">
                  Vyberte si z našich testů
                </div>

                <CategoriesList applications={applications} />
              </div>
            </div>

            {/* Porovnání funkcí */}
            <div className="mb-16 sm:mb-24">
              <h2 className="text-2xl sm:text-3xl font-bold text-center text-gray-900 mb-8 sm:mb-12">
                Porovnání dostupných funkcí
              </h2>
              <UserAccessComparisonTable />
            </div>

            {/* Režimy testování */}
            <div id="rezimy-testovani" className="relative mb-16 sm:mb-24">
              <div className="absolute -inset-4 bg-gradient-to-r from-blue-50 via-emerald-50 to-blue-50 rounded-3xl -z-10"></div>
              <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-3xl opacity-10 blur-lg -z-10"></div>

              <div className="relative rounded-2xl py-6 px-2 sm:p-8 border-2 border-blue-100 bg-white shadow-sm">
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-blue-600 text-white px-6 py-1 rounded-full text-sm font-medium whitespace-nowrap shadow-sm">
                  Jak fungují naše testy
                </div>

                <TestModeExplanation />
              </div>
            </div>

            {/* CTA Section */}
            <div className="mb-16 sm:mb-24 relative isolate overflow-hidden  rounded-2xl sm:rounded-3xl w-full mx-0">
              <div className="absolute rounded-2xl sm:rounded-3xl" />
              <div className="relative mx-auto max-w-7xl px-4 sm:px-6 py-16 sm:py-24">
                <div className="mx-auto max-w-2xl text-center">
                  <h2 className="relative inline-block">
                    <span className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-extrabold tracking-tight text-gray-900">
                      Začněte s přípravou
                    </span>
                    <div className="absolute -bottom-2 left-0 right-0 h-1 bg-emerald-500 transform origin-left"></div>
                  </h2>
                  <span className="block text-xl sm:text-2xl md:text-3xl font-semibold text-emerald-600 mt-4 sm:mt-6">
                    ještě dnes
                  </span>
                  <p className="mx-auto mt-4 sm:mt-6 text-base sm:text-lg leading-relaxed text-gray-600">
                    Registrujte se zdarma a získejte přístup ke všem testům.
                    Vaše úspěšná zkouška začíná zde.
                  </p>
                  <div className="mt-8 sm:mt-10 flex items-center justify-center gap-x-4 sm:gap-x-6">
                    <a
                      href="#vyber-testu"
                      className="inline-flex items-center px-6 sm:px-8 py-3 sm:py-4 rounded-xl
                        bg-emerald-600
                        text-white font-semibold
                        shadow-lg shadow-emerald-200/50
                        transform transition-all duration-200 hover:-translate-y-1 hover:bg-emerald-500
                        focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2"
                    >
                      Začít zdarma
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
