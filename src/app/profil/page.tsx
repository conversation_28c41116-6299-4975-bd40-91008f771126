import Menu from "@/components/Menu";
import React from "react";
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth.config";
import { ExtendedDefaultSession } from "@/types/extendedDefaultSession";
import { redirect } from "next/navigation";
import { getUserOrders } from "@/lib/services/userOrders";
import UserOrdersServer from "./components/UserOrdersServer";

export default async function ProfilePage() {
  const session = (await getServerSession(
    authConfig,
  )) as ExtendedDefaultSession;

  if (!session) {
    redirect("/prihlaseni");
  }

  // Fetch orders data server-side
  const ordersData = await getUserOrders();

  return (
    <div className="relative min-h-screen w-full bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Background grid pattern */}
      <div className="absolute inset-0 w-full h-full bg-grid opacity-10 pointer-events-none"></div>

      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -left-40 w-80 h-80 bg-emerald-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute -bottom-40 left-20 w-80 h-80 bg-emerald-50 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      <Menu application={undefined} />
      <div className="app-layout relative">
        <div className="container mx-auto pt-5 lg:pt-10 pb-20 max-w-3xl">
          <div className="relative bg-white/80 backdrop-blur-sm shadow-md rounded-lg p-6 mb-8 border border-emerald-100 hover:shadow-lg transition-shadow duration-300">
            <div className="absolute -inset-0.5 rounded-lg opacity-10 blur-lg -z-10"></div>
            <div className="flex items-center mb-6">
              <div className="bg-emerald-100 p-3 rounded-full mr-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 text-emerald-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-800">
                  Informace o účtu
                </h2>
                <p className="text-gray-600">{session.user?.email}</p>
              </div>
            </div>
          </div>

          <div className="relative bg-white/80 backdrop-blur-sm shadow-md rounded-lg p-6 border border-emerald-100 hover:shadow-lg transition-shadow duration-300">
            <div className="absolute -inset-0.5 rounded-lg opacity-10 blur-lg -z-10"></div>
            {ordersData ? (
              <UserOrdersServer orders={ordersData.userOrders.orders} />
            ) : (
              <div className="bg-white/80 backdrop-blur-sm border border-red-200 rounded-lg p-6 my-4 text-center shadow-sm">
                <div className="flex flex-col items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-12 w-12 text-red-400 mb-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <p className="text-red-600 font-medium">
                    Nepodařilo se načíst objednávky
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
