import React from "react";
import { formatDate } from "@/lib/utils/formatDate";
import { UserOrders_userOrders_orders } from "@/graphql/types/UserOrders";

interface UserOrdersServerProps {
  orders: UserOrders_userOrders_orders[];
}

const formatPrice = (price: number): string => {
  return new Intl.NumberFormat("cs-CZ", {
    style: "currency",
    currency: "CZK",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price / 100);
};

const getStatusLabel = (state: string): { text: string; color: string } => {
  switch (state) {
    case "PAID":
      return {
        text: "Zaplaceno",
        color: "bg-emerald-100 text-emerald-800 border border-emerald-200",
      };
    case "CREATED":
      return {
        text: "Čeká na platbu",
        color: "bg-yellow-100 text-yellow-800 border border-yellow-200",
      };
    case "CANCELED":
    case "TIMEOUTED":
      return {
        text: "<PERSON><PERSON><PERSON><PERSON>",
        color: "bg-red-100 text-red-800 border border-red-200",
      };
    case "REFUNDED":
      return {
        text: "Vr<PERSON><PERSON>no",
        color: "bg-blue-100 text-blue-800 border border-blue-200",
      };
    default:
      return {
        text: state,
        color: "bg-gray-100 text-gray-800 border border-gray-200",
      };
  }
};

export default function UserOrdersServer({ orders }: UserOrdersServerProps) {
  if (orders.length === 0) {
    return (
      <div className="bg-white/80 backdrop-blur-sm border border-emerald-100 rounded-lg p-6 my-4 text-center shadow-sm">
        <div className="flex flex-col items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 text-emerald-300 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
            />
          </svg>
          <p className="text-gray-600 font-medium">
            Zatím nemáte žádné objednávky.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold bg-clip-text">Moje objednávky</h2>

      <div className="grid grid-cols-1 gap-4">
        {orders.map((order) => {
          const status = getStatusLabel(order.state);
          return (
            <div
              key={order.id}
              className="relative bg-white/90 backdrop-blur-sm rounded-lg shadow-sm border border-emerald-100 overflow-hidden hover:shadow-md transition-all duration-300 hover:-translate-y-1"
            >
              <div className="absolute -inset-0.5 rounded-lg opacity-5 blur-sm -z-10"></div>
              <div className="p-4 border-b border-gray-100 flex justify-between items-center flex-wrap gap-2">
                <div>
                  <span className="text-sm text-gray-500">
                    Objednávka: #{order.id}
                  </span>
                  <div className="text-sm text-gray-500">
                    {formatDate(order.createdAt)}
                  </div>
                </div>
                <span
                  className={`px-3 py-1 text-xs font-semibold rounded-full shadow-sm ${status.color}`}
                >
                  {status.text}
                </span>
              </div>

              <div className="p-4">
                <div className="mb-3">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">
                    Položky:
                  </h3>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {order.items.map((item) => (
                      <li key={item.id} className="flex justify-between">
                        <span className="mr-2">{item.name}</span>
                        <span className="text-gray-500">
                          {item.price + " Kč"}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="flex justify-between items-center pt-3 border-t border-gray-100">
                  <span className="text-sm font-medium text-gray-700">
                    Celková cena:
                  </span>
                  <span className="text-sm font-bold text-gray-900">
                    {formatPrice(order.price)}
                  </span>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
