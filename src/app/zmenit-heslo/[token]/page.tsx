import { getForgottenPassword } from "@/app/zmenit-heslo/[token]/getForgottenPassword";
import Link from "next/link";
import { ChangeForgottenPasswordForm } from "@/components/ChangeForgottenPasswordForm";
import StaticMenu from "@/components/Menu/StaticMenu";

type Params = Promise<{ token: string }>;

export default async function ForgottenPasswordPage({
  params,
}: {
  params: Params;
}) {
  const { token: forgottenPasswordToken } = await params;
  const forgottenPasswordResponse = await getForgottenPassword(
    forgottenPasswordToken,
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Menu */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200">
        <StaticMenu application={undefined} />
      </div>

      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute w-full h-full bg-grid opacity-10"></div>
        <div className="absolute -top-40 -left-40 w-80 h-80 bg-emerald-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
      </div>

      <div className="relative flex min-h-full flex-1 flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <h2 className="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900">
            Obnovení hesla
          </h2>
          {!forgottenPasswordResponse.success && (
            <p className="mt-2 text-center text-sm text-gray-600">
              Odkaz pro obnovení hesla již není platný
            </p>
          )}
          {forgottenPasswordResponse.success && (
            <p className="mt-2 text-center text-sm text-gray-600">
              Zadejte nové heslo pro účet:{" "}
              <span className="font-semibold text-lg text-emerald-600">
                {forgottenPasswordResponse.data?.user.email}
              </span>
            </p>
          )}
        </div>

        <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
          <div className="bg-white/80 backdrop-blur-md px-6 py-12 shadow-lg sm:rounded-lg sm:px-12">
            {!forgottenPasswordResponse.success &&
              forgottenPasswordResponse?.message && (
                <>
                  <div
                    className="rounded-md bg-red-50 p-4 mb-6"
                    role="alert"
                    aria-live="polite"
                  >
                    <div className="flex">
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">
                          {forgottenPasswordResponse.message}
                        </h3>
                      </div>
                    </div>
                  </div>

                  <div className="text-center">
                    <p className="text-sm text-gray-600 mb-4">
                      Potřebujete obnovit heslo?
                    </p>
                    <Link
                      href="/zapomenute-heslo"
                      className="inline-flex justify-center rounded-md bg-emerald-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-emerald-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-emerald-600"
                    >
                      Zapomenuté heslo
                    </Link>
                  </div>
                </>
              )}

            {forgottenPasswordResponse.success && (
              <ChangeForgottenPasswordForm
                forgottenPasswordToken={forgottenPasswordToken}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
