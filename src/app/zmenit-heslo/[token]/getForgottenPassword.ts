"use server";

import { getClient } from "@/lib/client";
import { GET_FORGOTTEN_PASSWORD } from "@/app/zmenit-heslo/[token]/constants/getForgottenPasswordQuery";
import { ActiveForgottenPassword } from "@/graphql/types/ActiveForgottenPassword";
import { ApolloError } from "@apollo/client";
import { ActionActiveForgottenPasswordResponse } from "@/types/changeForgottenPassword";
import { mappedValidationError } from "@/app/zmenit-heslo/[token]/validationApiErrorMapper";

export async function getForgottenPassword(
  forgottenPasswordToken: string,
): Promise<ActionActiveForgottenPasswordResponse> {
  try {
    const client = getClient();

    const { data } = await client.query<ActiveForgottenPassword>({
      query: GET_FORGOTTEN_PASSWORD,
      variables: {
        forgottenPasswordToken: forgottenPasswordToken,
      },
    });

    return {
      success: true,
      message: "Token is valid",
      data: data.activeForgottenPassword,
    };
  } catch (e: unknown) {
    if (e instanceof ApolloError) {
      return mappedValidationError(e.message);
    }
  }

  return {
    success: true,
    message: "Token is valid",
  };
}
