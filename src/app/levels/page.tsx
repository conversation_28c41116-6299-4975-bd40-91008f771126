import React from "react";
import { gql } from "@apollo/client";
import { ExperienceLevels } from "@/graphql/types/ExperienceLevels";
import LevelList from "@/components/level/components/LevelList";
import { getClient } from "@/lib/client";

export const revalidate = 3600; // invalidate every hour
export const dynamic = "force-static";

const GET_LEVELS = gql`
  query LevelsExperienceLevels {
    experienceLevels(applicationId: "1") {
      experienceLevel {
        level
      }
    }
  }
`;
async function getLevels() {
  const client = getClient();

  const { data } = await client.query<ExperienceLevels>({ query: GET_LEVELS });
  return data.experienceLevels.experienceLevel.map((level) => level.level);
}

export default async function LevelPage() {
  const levels = await getLevels();

  return (
    <div>
      <p>NEXT_PUBLIC_GRAPHQL_HOST: {process.env.NEXT_PUBLIC_GRAPHQL_HOST}</p>
      <p>GraphQL: {process.env.REACT_APP_GRAPHQL_URI} </p>
      <LevelList levels={levels} />
    </div>
  );
}
