"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useSession } from "next-auth/react";
import { RegisterPromptModal } from "@/components/RegisterPromptModal/RegisterPromptModal";

interface Props {
  applicationSlug: string;
}

export const FavouriteQuestionsLink: React.FC<Props> = ({
  applicationSlug,
}) => {
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);
  const { status } = useSession();

  const handleClick = (e: React.MouseEvent) => {
    if (status !== "authenticated") {
      e.preventDefault();
      setIsRegisterModalOpen(true);
      return;
    }
  };

  return (
    <>
      <Link
        href={`/${applicationSlug}/oblibene-otazky`}
        onClick={handleClick}
        className="inline-flex items-center justify-center w-full px-4 py-3.5 bg-white border-2 border-gray-200 text-gray-700 font-medium rounded-xl hover:bg-gray-50 transition-all duration-200 group/button"
      >
        <span className="group-hover/button:translate-x-0.5 transition-transform duration-150">
          Moje oblíbené otázky
        </span>
        <svg
          className="ml-2 w-5 h-5 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M9 5l7 7-7 7"
          />
        </svg>
      </Link>

      <RegisterPromptModal
        isOpen={isRegisterModalOpen}
        onClose={() => setIsRegisterModalOpen(false)}
      />
    </>
  );
};

export default FavouriteQuestionsLink;
