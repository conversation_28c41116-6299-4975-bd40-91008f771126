import React from "react";
import Menu from "@/components/Menu/Menu";
import { Sections_sections_section } from "@/graphql/types/Sections";
import SectionItem from "./components/SectionItem";
import { getApplicationBySlug } from "@/lib/services/applications";
import { getSectionsByApplication } from "@/lib/services/sections";
import { getCategoryBySlug } from "@/lib/services/categories";
import NotFound from "@/components/NotFound/NotFound";
import StudyModesExplainer from "@/components/StudyModesExplainer/StudyModesExplainer";
import StudyModeCards from "@/components/StudyModeCards/StudyModeCards";

type Params = Promise<{ application: string; category: string }>;

export default async function CategoryPage({ params }: { params: Params }) {
  const { application, category } = await params;
  const applicationData = await getApplicationBySlug(application);

  if (!applicationData) {
    return <NotFound message="Aplikace nenalezena" />;
  }

  const categoryData = await getCategoryBySlug(category, applicationData);

  if (!categoryData) {
    return (
      <NotFound application={applicationData} message="Kategorie nenalezena" />
    );
  }

  const sections = await getSectionsByApplication(
    applicationData,
    categoryData,
  );

  return (
    <>
      {/* Menu */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200">
        <Menu application={applicationData} />
      </div>
      <div className="relative min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
        {/* Dekorativní prvek - horní část */}
        <div className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80">
          <div className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-emerald-300 to-blue-300 opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"></div>
        </div>

        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute w-full h-full bg-grid opacity-10"></div>
          <div className="absolute -top-40 -left-40 w-80 h-80 bg-emerald-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
          <div className="absolute top-0 -right-40 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
          <div className="absolute bottom-40 left-20 w-80 h-80 bg-emerald-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
        </div>

        {/* Main content */}
        <div className="relative container mx-auto pt-28 lg:pt-32 pb-20 px-4">
          <div className="max-w-7xl mx-auto">
            {" "}
            <StudyModesExplainer title={categoryData.title} />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 relative mb-24">
              {/* Dekorativní prvek - pozadí pro sekce */}
              <div className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80">
                <div className="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-blue-300 to-emerald-300 opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"></div>
              </div>

              {sections.map((section: Sections_sections_section) => (
                <div key={section.id} className="transition-all duration-300">
                  <SectionItem
                    section={section}
                    application={applicationData}
                  />
                </div>
              ))}
            </div>
            {/* Nová sekce - Další možnosti přípravy */}
            <div className="relative mt-24">
              <div className="relative max-w-7xl mx-auto">
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
                    Další možnosti přípravy
                  </h2>
                  <p className="mt-3 max-w-2xl mx-auto text-lg text-gray-600">
                    Prozkoumejte všechny dostupné testové otázky a vytvořte si
                    vlastní plán studia
                  </p>
                </div>

                <StudyModeCards
                  application={applicationData}
                  category={categoryData}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Dekorativní prvek - spodní část */}
        <div className="absolute inset-x-0 bottom-0 -z-10 transform-gpu overflow-hidden blur-3xl">
          <div className="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-blue-300 to-emerald-300 opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"></div>
        </div>
      </div>
    </>
  );
}
