"use server";

import React from "react";
import Menu from "@/components/Menu/Menu";
import { getApplicationBySlug } from "@/lib/services/applications";
import { getCategoryBySlug } from "@/lib/services/categories";
import { getQuestions } from "@/lib/services/getQuestions";
import NotFound from "@/components/NotFound/NotFound";
import { getSectionBySectionId } from "@/lib/services/section";
import { QuestionsWrapper } from "@/components/QuestionsWrapper/QuestionsWrapper";
import { PAGE_LIMIT } from "@/constants/pagination";
import { getQuestionInflection } from "@/utils/inflection";
import { QuestionsList } from "@/components/QuestionsList";

type Params = Promise<{
  application: string;
  category: string;
  section: string;
}>;

export default async function AllQuestionsPage({ params }: { params: Params }) {
  const { application, category, section } = await params;

  try {
    const applicationData = await getApplicationBySlug(application);

    if (!applicationData) {
      return <NotFound message="Aplikace nenalezena" />;
    }

    const categoryData = await getCategoryBySlug(category, applicationData);

    if (!categoryData) {
      return (
        <NotFound
          application={applicationData}
          message="Kategorie nenalezena"
        />
      );
    }

    const sectionData = await getSectionBySectionId(
      applicationData,
      categoryData,
      section,
    );

    if (!sectionData) {
      return (
        <NotFound application={applicationData} message="Section nenalezen" />
      );
    }

    const initialData = await getQuestions({
      applicationData,
      categoryData,
      sectionId: String(section),
      limit: PAGE_LIMIT,
      offset: 0,
    });

    if (initialData.allQuestionsCollection.totalCount === 0) {
      return (
        <NotFound
          application={applicationData}
          message="Pro tuto sekci nejsou k dispozici žádné otázky"
        />
      );
    }

    return (
      <div className="min-h-screen bg-gray-100">
        <div className="fixed top-0 left-0 right-0 z-50 bg-white shadow-sm">
          <Menu application={applicationData} />
        </div>

        <QuestionsWrapper
          title={
            <div className="flex items-center gap-3">
              <span>{sectionData.questionGroup.title} - Všechny otázky</span>
              <span className="text-base font-normal text-gray-500">
                ({initialData.allQuestionsCollection.totalCount}{" "}
                {getQuestionInflection(
                  initialData.allQuestionsCollection.totalCount,
                )}
                )
              </span>
            </div>
          }
          description={`Kompletní seznam všech dostupných testových otázek v sekci ${sectionData.questionGroup.title}`}
        >
          <QuestionsList
            applicationData={applicationData}
            categoryData={categoryData}
            sectionData={sectionData}
            initialData={initialData}
          />
        </QuestionsWrapper>
      </div>
    );
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    return <NotFound message="Došlo k chybě při načítání dat" />;
  }
}
