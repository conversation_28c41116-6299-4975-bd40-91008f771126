.main {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(to bottom right, #f9fafb, #ffffff, #f9fafb);
}

.scrollView {
  display: flex;
  flex-grow: 1;
}

.backgroundImage {
  display: flex;
  width: 100%;
  resize-mode: cover;
  flex-grow: 1;
}

.contentContainer {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
  max-width: 64rem;
  margin: 0 auto;
  padding: 1.25rem;
}

.row {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1rem;
}

.space {
  margin-top: 15px;
}

.leftSide {
  width: 90px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.rightSide {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 0.5rem;
}

.imageLetter {
  font-size: 1.125rem;
  color: #4b5563;
}

.imageLetterTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.imageLeft {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 0.75rem;
}

.newExamButton {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: #4b9346;
  border-radius: 15px;
  border-bottom-width: 2px;
  border-color: #999;
  padding: 10px;
  margin-right: 15px;
}

.newExamText {
  text-align: center;
  color: white;
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}
