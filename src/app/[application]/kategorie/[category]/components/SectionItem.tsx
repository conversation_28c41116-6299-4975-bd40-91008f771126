"use client";

import React from "react";
import Link from "next/link";
import { Sections_sections_section } from "@/graphql/types/Sections";
import { Application_application } from "@/graphql/types/Application";
import { NotebookPen, HelpCircle } from "lucide-react";

type SectionItemProps = {
  section: Sections_sections_section;
  application: Application_application;
};

export default function SectionItem({
  section,
  application,
}: SectionItemProps) {
  return (
    <div
      className="h-full bg-white rounded-2xl shadow-lg hover:shadow-2xl
      transition-all duration-300 border border-gray-100 overflow-hidden group"
    >
      <div className="relative h-40 bg-gradient-to-br from-emerald-500/10 to-blue-500/10">
        <div className="absolute inset-0 flex items-center justify-center">
          <svg
            className="w-16 h-16 sm:w-20 sm:h-20 text-emerald-600/80 transition-transform duration-300 group-hover:scale-110"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth="1.5"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25"
            />
          </svg>
        </div>
      </div>

      <div className="p-4 sm:p-6">
        <div className="space-y-4">
          <div>
            <h3 className="text-lg sm:text-xl font-semibold text-gray-900 line-clamp-2 min-h-[48px] sm:min-h-[56px]">
              {section.questionGroup.title}
            </h3>
            <p className="mt-2 text-sm font-medium text-gray-600">
              U závěrečné zkoušky
            </p>
          </div>

          <div className="flex items-center justify-between text-sm text-gray-600 border-t border-gray-100 pt-4">
            <div className="flex items-center space-x-1.5">
              <svg
                className="w-4 h-4 sm:w-5 sm:h-5 text-emerald-500 flex-shrink-0"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span>otázek: {section.questionCount}</span>
            </div>
            <div className="flex items-center space-x-1.5">
              <svg
                className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500 flex-shrink-0"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
              <span>body: {section.pointsPerQuestion} / otázka</span>
            </div>
          </div>

          <div className="flex flex-col gap-3 pt-2">
            <div className="grid grid-cols-2 gap-3">
              <Link
                href={`/${application.slug}/kategorie/${section.category.slug}/vsechny-otazky/${section.id}`}
                className="group/button flex items-center justify-center px-4 py-2.5 bg-gradient-to-br from-slate-600 to-slate-700 text-white font-medium rounded-xl hover:from-slate-500 hover:to-slate-600 transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <HelpCircle className="w-5 h-5 mr-1.5 sm:mr-2 text-blue-200 flex-shrink-0" />
                <span className="group-hover/button:translate-x-0.5 transition-transform duration-150 font-medium text-sm sm:text-base">
                  <span>Přehled</span>
                </span>
              </Link>
              <Link
                href={`/${application.slug}/kategorie/${section.category.slug}/cviceni/${section.id}`}
                className="flex items-center justify-center px-3 sm:px-4 py-2.5 bg-gradient-to-br from-emerald-600 to-emerald-700 text-white font-medium rounded-xl hover:from-emerald-500 hover:to-emerald-600 transition-all duration-200 shadow-sm hover:shadow-md group/button"
              >
                <NotebookPen className="w-5 h-5 mr-1.5 sm:mr-2 text-emerald-200 flex-shrink-0" />
                <span className="group-hover/button:translate-x-0.5 transition-transform duration-150 font-medium text-sm sm:text-base">
                  Cvičení
                </span>
              </Link>
            </div>
            <Link
              href={`/${application.slug}/kategorie/${section.category.slug}/vsechny-otazky/zkouska/${section.id}`}
              className="flex items-center justify-center px-3 sm:px-4 py-2.5 bg-gradient-to-br from-blue-600 to-blue-700 text-white font-medium rounded-xl hover:from-blue-500 hover:to-blue-600 transition-all duration-200 shadow-sm hover:shadow-md group/button min-w-0"
            >
              <HelpCircle className="w-5 h-5 mr-1.5 sm:mr-2 text-blue-200 flex-shrink-0" />
              <span className="group-hover/button:translate-x-0.5 transition-transform duration-150 font-medium text-sm sm:text-base">
                <span>Zkouška</span>
              </span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
