"use client";

import React, { useState, useCallback } from "react";
import { useCart } from "@/context/CartContext";
import { Application_application } from "@/graphql/types/Application";
import { ShoppingCart, LogIn, UserPlus, X, Check } from "lucide-react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import GoogleButton from "@/components/LoginForm/GoogleButton";
import { useRouter } from "next/navigation";
import { CartSuccessModal } from "@/components/CartSuccessModal/CartSuccessModal";

type AddToCartButtonProps = {
  application: Application_application;
};

export default function AddToCartButton({ application }: AddToCartButtonProps) {
  const { addItem } = useCart();
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
  const [isAdded, setIsAdded] = useState(false);
  const { status } = useSession();
  const router = useRouter();

  const handleAddToCart = useCallback(() => {
    if (status !== "authenticated") {
      setIsLoginModalOpen(true);
      return;
    }

    addItem({
      id: application.token,
      name: application.name,
      price: application.price,
    });

    setIsAdded(true);
    setIsSuccessModalOpen(true);
  }, [application, addItem, status]);

  const handleGoToCart = () => {
    router.push("/kosik");
  };

  // Pokud je aplikace již zakoupena, zobrazíme jiné tlačítko
  if (application.isPaid) {
    return (
      <button
        disabled
        className="flex items-center justify-center gap-2 px-8 py-4 rounded-xl
          bg-gray-50 text-gray-500 border-gray-200 cursor-not-allowed
          shadow-sm border"
      >
        <Check className="w-5 h-5" />
        <span className="font-semibold">Již zakoupeno</span>
      </button>
    );
  }

  return (
    <>
      <button
        onClick={handleAddToCart}
        disabled={isAdded}
        className={`
          flex items-center justify-center gap-2 px-8 py-4 rounded-xl
          transition-all duration-300 transform
          shadow-lg hover:shadow-xl border
          ${
            isAdded
              ? "bg-emerald-50 text-emerald-600 border-emerald-200 cursor-default"
              : "bg-white text-emerald-600 border-emerald-200 hover:bg-emerald-50 hover:-translate-y-1"
          }
        `}
      >
        {isAdded ? (
          <>
            <Check className="w-5 h-5" />
            <span className="font-semibold">Přidáno do košíku</span>
          </>
        ) : (
          <>
            <ShoppingCart className="w-5 h-5" />
            <span className="font-semibold">Přidat do košíku</span>
          </>
        )}
      </button>

      {/* Login Modal */}
      {isLoginModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center px-6">
          <div
            className="fixed inset-0 bg-black/50"
            onClick={() => setIsLoginModalOpen(false)}
          />
          <div className="relative bg-white shadow-xl rounded-2xl p-8 max-w-md w-full">
            <button
              onClick={() => setIsLoginModalOpen(false)}
              className="absolute right-4 top-4 text-gray-400 hover:text-gray-600"
            >
              <X className="w-5 h-5" />
            </button>

            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900">
                Pro přidání do košíku se přihlaste
              </h3>
              <p className="mt-2 text-sm text-gray-600">
                Pro nákup našich produktů je nutné být přihlášený
              </p>
            </div>

            <div className="space-y-6">
              <GoogleButton />

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-4 py-1.5 bg-white text-gray-500 rounded-full">
                    Nebo pokračujte s emailem
                  </span>
                </div>
              </div>

              <div className="space-y-3">
                <Link
                  href="/prihlaseni"
                  className="flex items-center justify-center w-full gap-2 px-4 py-2.5 text-white bg-emerald-600 rounded-lg hover:bg-emerald-700 transition-colors"
                >
                  <LogIn className="w-5 h-5" />
                  <span>Přihlásit se</span>
                </Link>

                <Link
                  href="/registrace"
                  className="flex items-center justify-center w-full gap-2 px-4 py-2.5 text-emerald-600 bg-white border border-emerald-600 rounded-lg hover:bg-emerald-50 transition-colors"
                >
                  <UserPlus className="w-5 h-5" />
                  <span>Vytvořit účet zdarma</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}

      <CartSuccessModal
        isOpen={isSuccessModalOpen}
        onClose={() => setIsSuccessModalOpen(false)}
        onGoToCart={handleGoToCart}
        productName={application.name}
      />
    </>
  );
}
