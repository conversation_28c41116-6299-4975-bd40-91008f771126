"use server";

import React from "react";
import Menu from "@/components/Menu/Menu";
import { Questions } from "@/app/[application]/kategorie/zkouska/[category]/Questions/Questions";
import { getApplicationBySlug } from "@/lib/services/applications";
import { getCategoryBySlug } from "@/lib/services/categories";
import { getQuestionsByQuestionGroup } from "@/lib/services/sectionQuestions";
import NotFound from "@/components/NotFound/NotFound";
import { getSectionBySectionId } from "@/lib/services/section";
import { getFavouriteQuestions } from "@/app/[application]/kategorie/zkouska/[category]/services/getFavouriteQuestions";
import { QuestionsWrapper } from "@/components/QuestionsWrapper/QuestionsWrapper";

type Params = Promise<{
  application: string;
  category: string;
  section: string;
}>;

export default async function SectionPracticePage({
  params,
}: {
  params: Params;
}) {
  const { application, category, section } = await params;

  try {
    const applicationData = await getApplicationBySlug(application);

    if (!applicationData) {
      return <NotFound message="Aplikace nenalezena" />;
    }

    const categoryData = await getCategoryBySlug(category, applicationData);

    if (!categoryData) {
      return (
        <NotFound
          application={applicationData}
          message="Kategorie nenalezena"
        />
      );
    }

    const sectionData = await getSectionBySectionId(
      applicationData,
      categoryData,
      section,
    );

    if (!sectionData) {
      return (
        <NotFound application={applicationData} message="Section nenalezen" />
      );
    }

    const [questions, favouriteQuestionIds] = await Promise.all([
      getQuestionsByQuestionGroup(applicationData, categoryData, sectionData),
      getFavouriteQuestions(applicationData.id),
    ]);

    if (questions.length === 0) {
      return (
        <NotFound
          application={applicationData}
          message="Pro tuto sekci nejsou k dispozici žádné otázky"
        />
      );
    }

    return (
      <div className="min-h-screen bg-gray-100">
        <div className="fixed top-0 left-0 right-0 z-50 bg-white shadow-sm">
          <Menu application={applicationData} />
        </div>

        <QuestionsWrapper
          title={`${categoryData.title} - Procvičování otázek`}
          description={`Sekce: ${sectionData.questionGroup.title}`}
        >
          <Questions
            questions={questions}
            showCorrectAnswer={true}
            categoryName={category}
            application={applicationData}
            favouriteQuestionIds={favouriteQuestionIds}
            section={sectionData}
            typeExamFinished={"sectionExam"}
          />
        </QuestionsWrapper>
      </div>
    );
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    return <NotFound message="Došlo k chybě při načítání dat" />;
  }
}
