.categoryHS {
  flex: 1;
  align-items: center;
  flex-direction: column;
  max-width: 1000px;
  margin: auto;
  padding-left: 10px;
  padding-right: 10px;
}
/* Container with no padding */
.container {
  padding: 0;
}

/* Category container */
.category {
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: space-between;
  margin-top: 12px;
}

/* Category item */
.item,
.itemSelected {
  width: 34px;
  height: 34px;
  border-width: 1px;
  border-color: black;
}

/* Selected category item */
.itemSelected {
  background-color: green;
}

/* Default item */
.item {
  background-color: yellow;
}

/* Text container with centering */
.text {
  justify-content: center;
  align-items: center;
  flex: 1;
}

/* Description text */
.description {
  font-size: 15px;
  color: #888;
  margin-top: 5px;
  margin-bottom: 5px;
}

/* Title button styles */
.titleButton {
  font-size: 18px;
  color: #888;
  display: inline;
}

.titleButtonMute {
  font-size: 18px;
  color: #b1b0b0ff;
}

/* Title text */
.title {
  font-size: 20px;
  color: #000;
  font-weight: bold;
  text-align: center;
}

/* Subtitle text */
.subtitle {
  font-size: 15px;
  color: #000;
}

/* Text color */
.nameText {
  color: #888;
}

/* Main start example style */
.mainStartExample {
  align-items: center;
  padding: 20px;
  background-color: white;
}

/* Image touchable style */
.imageTouchable {
  padding: 3px;
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
}

.imageTouchable:hover {
  opacity: 1;
}

/* Active and inactive image styles */
.imageActive {
  opacity: 1;
}

.imageNotActive {
  opacity: 0.4;
}
.imageNotActive:hover {
  opacity: 0.6;
}
/* Active and inactive image letter container styles */
.imageLetterContainerActive {
  opacity: 1;
}

.imageLetterContainerNotActive {
  opacity: 0.5;
}

/* Image for categories */
.imageCategories {
  width: 80px;
  height: auto;
  cursor: pointer;
}

/* Image letter container style */
.imageLetterContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: -10px;
  border-radius: 200px;
  height: 30px;
  width: 30px;
  background-color: #4b9346;
  border: 1px solid white;
  cursor: pointer;
}

.buttonContainer {
  width: 100%;
}

/* Image letter text style */
.imageLetter {
  color: white;
  font-size: 19px;
  cursor: pointer;
}

/* Space style for margin */
.space {
  margin-top: 20px;
}

/* All questions container style */
.allQuestionsContainer {
  flex-direction: row;
}

/* All questions description container style */
.allQuestionsDescContainer {
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  flex: 1;
}

/* Lock icon image style */
.imageLockFullVersion {
  width: 15px;
  height: 15px;
  opacity: 0.5;
  display: inline;
}


