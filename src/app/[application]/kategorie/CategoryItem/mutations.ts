import { gql } from "@apollo/client";

export const ADD_FAVOURITE_CATEGORY = gql`
  mutation AddFavouriteCategory($applicationId: ID!, $categoryId: ID!) {
    addFavouriteCategory(applicationId: $applicationId, categoryId: $categoryId)
  }
`;

export const REMOVE_FAVOURITE_CATEGORY = gql`
  mutation RemoveFavouriteCategory($applicationId: ID!, $categoryId: ID!) {
    removeFavouriteCategory(
      applicationId: $applicationId
      categoryId: $categoryId
    )
  }
`;
