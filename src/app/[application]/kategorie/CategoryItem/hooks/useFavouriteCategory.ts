"use client";

import { useMutation } from "@apollo/client";
import { useSession } from "next-auth/react";
import { useState, useCallback } from "react";
import { ExtendedDefaultSession } from "@/types/extendedDefaultSession";
import { showToast } from "@/components/Toast/config";
import {
  ADD_FAVOURITE_CATEGORY,
  REMOVE_FAVOURITE_CATEGORY,
} from "../mutations";

interface AddFavouriteCategoryVariables {
  applicationId: string;
  categoryId: string;
}

interface AddFavouriteCategory {
  addFavouriteCategory: boolean;
}

interface RemoveFavouriteCategory {
  removeFavouriteCategory: boolean;
}

export const useFavouriteCategory = (
  applicationId: number,
  categoryId: number,
  isFavouriteCategoryDefault: boolean,
) => {
  const [isFavourite, setIsFavourite] = useState(isFavouriteCategoryDefault);
  const { data: sessionData } = useSession();
  const session = sessionData as ExtendedDefaultSession;

  const authHeaders = {
    headers: {
      Authorization: `Bearer ${session?.user?.token}`,
    },
  };

  const [addFavouriteCategory] = useMutation<
    AddFavouriteCategory,
    AddFavouriteCategoryVariables
  >(ADD_FAVOURITE_CATEGORY, {
    context: authHeaders,
  });

  const [removeFavouriteCategory] = useMutation<
    RemoveFavouriteCategory,
    AddFavouriteCategoryVariables
  >(REMOVE_FAVOURITE_CATEGORY, {
    context: authHeaders,
  });

  const toggleFavourite = useCallback(async () => {
    if (!session?.user?.token) return;

    try {
      const isCurrentlyFavourite = isFavourite;
      const mutation = isCurrentlyFavourite
        ? removeFavouriteCategory
        : addFavouriteCategory;

      await mutation({
        variables: {
          applicationId: applicationId.toString(),
          categoryId: categoryId.toString(),
        },
      });

      setIsFavourite((prev) => !prev);

      if (isCurrentlyFavourite) {
        showToast("Kategorie byla odebrána z oblíbených", "info");
      } else {
        showToast("Kategorie byla přidána mezi oblíbené", "success");
      }
    } catch (error) {
      console.error("Failed to toggle favourite category:", error);
      showToast("Nepodařilo se změnit stav oblíbené kategorie", "error");
    }
  }, [
    isFavourite,
    applicationId,
    categoryId,
    addFavouriteCategory,
    removeFavouriteCategory,
    session?.user?.token,
  ]);

  return {
    isFavourite,
    toggleFavourite,
  };
};
