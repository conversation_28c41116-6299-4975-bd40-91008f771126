.container {
  padding: 0;
}
.category {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  flex-direction: row;
  justify-content: center;
  margin-top: 5px;
}
.item {
  width: 34px;
  height: 34px;
  border-width: 1px;
  border-color: black;
  background-color: yellow;
}
.itemSelected {
  width: 34px;
  height: 34px;
  border-width: 1px;
  border-color: black;
  background-color: green;
}
.text {
  justify-content: center;
  align-items: center;
  flex: 1;
}
.description {
  font-size: 15px;
  color: #888;
  margin-top: 5px;
  margin-bottom: 5px;
}
.titleButton {
  font-size: 18px;
  color: #888;
  max-width: 200px;
}
.titleButtonMute {
  font-size: 18px;
  color: #b1b0b0ff;
}
.title {
  font-size: 20px;
  color: #000000;
}
.subtitle {
  font-size: 15px;
  color: #000000;
}
.nameText {
  color: #888;
}
.mainStartExample {
  align-items: center;
  padding: 20px;
  background-color: white;
}
.imageTouchable {
  display: flex;
  align-items: center;
  flex: 1;
}
.imageActive {
  opacity: 1;
}
.imageNotActive {
  opacity: 0.4;
}
.imageLetterContainerActive {
  opacity: 1;
}
.imageLetterContainerNotActive {
  opacity: 0.5;
}
.imageCategories {
  width: 65px;
  height: 72px;
  resize-mode: contain;
}
.imageLetterContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}
.imageLetterTitle {
  font-size: 17px;
  color: black;
  font-weight: bold;
}
.imageLetter {
  font-size: 17px;
  color: black;
}
.space {
  margin-top: 20px;
}
.allQuestionsContainer {
  flex-direction: row;
}
.allQuestionsDescContainer {
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  flex: 1;
}
.imageLockFullVersion {
  width: 15px;
  height: 15px;
  opacity: 0.5;
}
.startButton {
  height: 60px;
}
.row {
  flex-direction: row;
  flex: 1;
  display: flex;
}
.leftSide {
  display: flex;
  align-items: flex-start;
  width: 90px;
}
.rightSide {
  display: flex;
  flex-direction: column;
  flex: 1;
  align-items: flex-start;
  justify-content: flex-start;
}
.rightSideImage {
  width: 10px;
  justify-content: center;
  align-items: flex-end;
  top: 0;
  margin-right: -15px;
}
.newExamButton {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: #4b9346;
  border-radius: 15px;
  border-bottom-width: 2px;
  border-color: #999;
  margin-right: 8px;
  cursor: pointer;
  margin-top: 10px;
}
.newExamText {
  text-align: center;
  color: white;
}
.arrowButton {
  width: 15px;
  top: 5px;
  padding-right: 20px;
}
.arrow {
  width: 30px;
  height: 30px;
}