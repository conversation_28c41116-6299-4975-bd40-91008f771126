"use client";

import { Star } from "lucide-react";
import { useState } from "react";
import { useFavouriteCategory } from "./hooks/useFavouriteCategory";
import { RegisterPromptModal } from "@/components/RegisterPromptModal/RegisterPromptModal";
import { useSession } from "next-auth/react";

interface Props {
  categoryId: number;
  applicationId: number;
  isFavouriteCategoryDefault: boolean;
}

export default function FavouriteButton({
  applicationId,
  categoryId,
  isFavouriteCategoryDefault,
}: Props) {
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);
  const { status } = useSession();
  const { isFavourite, toggleFavourite } = useFavouriteCategory(
    applicationId,
    categoryId,
    isFavouriteCategoryDefault,
  );

  const handleClick = () => {
    if (status !== "authenticated") {
      setIsRegisterModalOpen(true);
      return;
    }

    toggleFavourite();
  };

  return (
    <>
      <button
        onClick={handleClick}
        className={`
          absolute top-3 right-3 z-10
          p-2.5
          rounded-full
          transition-all duration-200
          focus:outline-none
          ${
            isFavourite
              ? "bg-amber-100 text-amber-500"
              : "bg-white/80 text-gray-400 hover:bg-gray-100"
          }
        `}
        aria-label={
          isFavourite ? "Odebrat z oblíbených" : "Přidat do oblíbených"
        }
        title={isFavourite ? "Odebrat z oblíbených" : "Přidat do oblíbených"}
      >
        <Star
          className="w-5 h-5 transition-transform hover:scale-110"
          fill={isFavourite ? "currentColor" : "none"}
          strokeWidth={2}
        />
      </button>

      <RegisterPromptModal
        isOpen={isRegisterModalOpen}
        onClose={() => setIsRegisterModalOpen(false)}
        title="Oblíbené kategorie"
        features={[
          "Ukládat si kategorie mezi oblíbené",
          "Efektivnější organizace vašeho studia",
        ]}
      />
    </>
  );
}
