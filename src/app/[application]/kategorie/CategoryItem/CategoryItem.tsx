"use client";

import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { Categories_categories_category } from "@/graphql/types/Categories";
import {
  BookOpen,
  NotebookPen,
  Timer,
  HelpCircle,
  Clock,
  LockKeyhole,
} from "lucide-react";
import dynamic from "next/dynamic";
import { Application_application } from "@/graphql/types/Application";
import PaymentRequiredModal from "@/components/PaymentRequiredModal/PaymentRequiredModal";

interface Props {
  category: Categories_categories_category;
  application: Application_application;
}

const FavouriteButton = dynamic(() => import("./FavouriteButton"));

export const CategoryItem = (props: Props) => {
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);

  // Check if this is one of the special combinations that need items-end alignment
  const isSpecialCombination =
    (props.application.slug === "kapitanske-zkousky" &&
      props.category.slug === "s-a-s20") ||
    (props.application.slug === "zbrojni-prukaz" &&
      props.category.slug === "d") ||
    (props.application.slug === "straznik" &&
      props.category.slug === "straznik");
  return (
    <div
      className="group h-full bg-white rounded-2xl shadow-lg hover:shadow-2xl
      transition-all duration-300 border border-gray-100 relative"
    >
      <FavouriteButton
        categoryId={props.category.id}
        applicationId={props.application.id}
        isFavouriteCategoryDefault={props.category.isFavourite}
      />

      <div className="relative h-40 bg-gradient-to-br from-emerald-500/10 to-blue-500/10 rounded-t-2xl">
        <div
          className={`absolute inset-0 flex ${isSpecialCombination ? "items-end" : "items-center"} justify-center`}
        >
          {props.category.image ? (
            <Image
              src={props.category.image.url}
              alt={props.category.title}
              width={isSpecialCombination ? 180 : 200}
              height={isSpecialCombination ? 180 : 200}
              priority={true}
              className="transition-transform duration-300 group-hover:scale-110"
            />
          ) : (
            <svg
              className="w-16 h-16 text-emerald-600/80 transition-transform duration-300 group-hover:scale-110"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth="1.5"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25"
              />
            </svg>
          )}
        </div>
      </div>

      <div className="p-6 relative">
        <div className="space-y-4">
          <div>
            <h3 className="text-xl font-semibold text-gray-900 line-clamp-2 min-h-[56px]">
              {props.category.title}
            </h3>
            <p className="mt-2 text-sm font-medium text-gray-600">
              Skupina {props.category.name}
            </p>
          </div>

          <div className="flex items-center justify-between text-sm text-gray-600 border-t border-gray-100 pt-4">
            <div className="flex items-center space-x-1">
              <HelpCircle className="w-5 h-5 text-emerald-500" />
              <span>{props.category.questionsCount} otázek</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="w-5 h-5 text-blue-500" />
              <span>{props.category.timeLimitInMinutes} minut</span>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-3 pt-4">
            {props.application.isPaid ? (
              <Link
                href={`/${props.application.slug}/kategorie/${props.category.slug}`}
                className="group/button flex items-center justify-center px-4 py-2.5 bg-gradient-to-br from-slate-600 to-slate-700 text-white font-medium rounded-xl hover:from-slate-500 hover:to-slate-600 transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <BookOpen className="w-5 h-5 mr-1.5 sm:mr-2 text-slate-200 flex-shrink-0" />
                <span className="group-hover/button:translate-x-0.5 transition-transform duration-150 font-medium text-sm sm:text-base">
                  Výuka
                </span>
              </Link>
            ) : (
              <button
                onClick={() => setIsPaymentModalOpen(true)}
                className="group/button flex items-center justify-center px-4 py-2.5 bg-gradient-to-br from-slate-600 to-slate-700 text-white font-medium rounded-xl hover:from-slate-500 hover:to-slate-600 transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <LockKeyhole className="w-5 h-5 mr-1.5 sm:mr-2 text-amber-400 flex-shrink-0" />
                <span className="group-hover/button:translate-x-0.5 transition-transform duration-150 font-medium text-sm sm:text-base">
                  Výuka
                </span>
              </button>
            )}
            <Link
              href={`/${props.application.slug}/kategorie/cviceni/${props.category.slug}`}
              className="group/button flex items-center justify-center px-4 py-2.5 bg-gradient-to-br from-emerald-600 to-emerald-700 text-white font-medium rounded-xl hover:from-emerald-500 hover:to-emerald-600 transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <NotebookPen className="w-5 h-5 mr-1.5 sm:mr-2 text-emerald-200 flex-shrink-0" />
              <span className="group-hover/button:translate-x-0.5 transition-transform duration-150 font-medium text-sm sm:text-base">
                Cvičení
              </span>
            </Link>
            <Link
              href={`/${props.application.slug}/kategorie/zkouska/${props.category.slug}`}
              className="group/button col-span-2 flex items-center justify-center px-4 py-2.5 bg-gradient-to-br from-blue-600 to-blue-700 text-white font-medium rounded-xl hover:from-blue-500 hover:to-blue-600 transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <Timer className="w-5 h-5 mr-1.5 sm:mr-2 text-blue-200 flex-shrink-0" />
              <span className="group-hover/button:translate-x-0.5 transition-transform duration-150 font-medium text-sm sm:text-base">
                Zkouška
              </span>
            </Link>
          </div>
        </div>
      </div>

      {/* Modální okno pro nezakoupenou aplikaci */}
      <PaymentRequiredModal
        isOpen={isPaymentModalOpen}
        onCloseAction={() => setIsPaymentModalOpen(false)}
        application={props.application}
      />
    </div>
  );
};
