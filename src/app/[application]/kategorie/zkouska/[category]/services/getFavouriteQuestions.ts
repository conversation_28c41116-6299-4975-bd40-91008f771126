import { getClient } from "@/lib/client";
import { GET_FAVOURITE_QUESTIONS } from "../Questions/Question/query";
import {
  FavouriteQuestions,
  FavouriteQuestionsVariables,
} from "@/graphql/types/FavouriteQuestions";
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth.config";
import { ExtendedDefaultSession } from "@/types/extendedDefaultSession";

export const getFavouriteQuestions = async (applicationId: number) => {
  const session = (await getServerSession(
    authConfig,
  )) as ExtendedDefaultSession;

  if (!session?.user?.token) {
    return [];
  }

  const client = getClient();

  const { data } = await client.query<
    FavouriteQuestions,
    FavouriteQuestionsVariables
  >({
    query: GET_FAVOURITE_QUESTIONS,
    variables: {
      applicationId: applicationId.toString(),
    },
    context: {
      headers: {
        Authorization: `Bearer ${session.user.token}`,
      },
    },
  });

  return (
    data?.favouriteQuestions?.questions.map((question) => question.id) || []
  );
};
