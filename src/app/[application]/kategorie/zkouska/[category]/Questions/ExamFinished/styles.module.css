/* Overall container */
.main {
    display: block;
    flex-shrink: 1;
    height: 100%;
}
.container {
    max-width: 1200px;
}

/* Scrollable content area */
.scrollView {
    padding: 20px;
    display: block;
    flex-grow: 1;
}

/* Title text with positive result (green) */
.titleTextPositive {
    font-size: 25px;
    color: #28a745; /* Assuming Colors.BrandDefault is green */
    text-align: center;
    padding-bottom: 10px;
    margin-top: -10px;
}

/* Title text with negative result (red) */
.titleTextNegative {
    font-size: 25px;
    color: #e74c3c; /* Assuming Colors.ContentDefaultPrimary is red */
    text-align: center;
    padding-bottom: 10px;
    margin-top: -10px;
}

/* Container for elements arranged in a row */
.flexRowContainer {
    flex-direction: row;
}

/* Individual item within the row */
.flexItem {
    flex: 1;
    align-items: center;
    text-align: center;
    padding-bottom: 25px;
}

/* Text for displaying points */
.textPoints {
    font-size: 20px;
    color: #080;
    margin-top: 40px;
}

/* Text for displaying questions */
.textQuestions {
    font-size: 20px;
    color: #333; /* Assuming Colors.ContentDefaultPrimary is a dark color */
    margin-bottom: 10px;
}

/* Container for displaying time information (centered) */
.textTimeContainer {
    flex: 1;
    align-items: center;
    margin-top: 40px;
}

/* Text for displaying time */
.textTime {
    font-size: 20px;
    color: red;
    margin-bottom: 10px;
}

/* Text for points label */
.pointsText {
    font-size: 20px;
    color: #333; /* Assuming Colors.ContentDefaultPrimary is a dark color */
}

/* Text inside a circle (green) */
.circularText {
    color: green;
    font-size: 24px;
}

/* Main container (centered) */
.container {
    flex: 1;
    align-items: center;
    width: 100%;
}

/* Text for incorrect answer (red) */
.wrongText {
    color: red;
    font-size: 20px;
}

/* Text for correct answer (green) */
.correctText {
    color: green;
    font-size: 20px;
}

/* Container for image (centered) */
.imageContainer {
    margin-top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}

/* Image styles */
.image {
    height: 100px;
    width: 100px;
}

/* Text for progress description */
.textProgress {
    font-size: 16px;
    color: #333; /* Assuming Colors.ContentDefaultPrimary is a dark color */
    margin-top: 10px;
    margin-bottom: 2px;
    padding-left: 10px;
    text-align: center;
}
.sectionContainer {
    margin-bottom: 10px;
}
.sectionTitle {
    font-size: 19px;
    font-weight: bold;
}
.questionText {
    font-size: 18px;
}
.answerText {
    font-size: 16px;
}
.questionContainer {
    margin-bottom: 30px;
    margin-top: 15px;
}