"use client";
import { Exam_exam_questions } from "@/graphql/types/Exam";
import { UserAnswer } from "@/app/[application]/kategorie/zkouska/[category]/Questions/Questions";
import {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { useMutation } from "@apollo/client";
import { MUTATION_EVALUATE_EXAM } from "@/app/[application]/kategorie/zkouska/[category]/Questions/ExamFinished/mutation";
import { ExamStatistic } from "@/app/[application]/kategorie/zkouska/[category]/Questions/ExamFinished/ExamStatistic/ExamStatistic";
import {
  EvaluateExam,
  EvaluateExamVariables,
} from "@/graphql/types/EvaluateExam";
import { useSession } from "next-auth/react";
import { ExtendedDefaultSession } from "@/types/extendedDefaultSession";
import Link from "next/link";
import { Application_application } from "@/graphql/types/Application";
import PaymentRequiredModal from "@/components/PaymentRequiredModal/PaymentRequiredModal";

export interface Props {
  questions: Exam_exam_questions[];
  userAnswers: UserAnswer[];
  application: Application_application;
  categoryName: string;
  setIndexQuestion: Dispatch<SetStateAction<number>>;
  showCorrectAnswerAndUserAnswerByFinished: Dispatch<SetStateAction<boolean>>;
  existingResult: EvaluateExam | null;
  onResultReceived: (result: EvaluateExam) => void;
}

export const ExamFinished = (props: Props) => {
  const { data: sessionData } = useSession();
  const session = sessionData as ExtendedDefaultSession | null;
  const isSubmitting = useRef(false);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);

  // Destructure props outside useCallback
  const {
    userAnswers,
    application,
    categoryName,
    existingResult,
    onResultReceived,
  } = props;

  const [mutate, { data, loading }] = useMutation<
    EvaluateExam,
    EvaluateExamVariables
  >(MUTATION_EVALUATE_EXAM, {
    context: {
      headers: {
        ...(session?.user?.token
          ? { authorization: `Bearer ${session.user.token}` }
          : {}),
      },
    },
  });

  const handleSubmitFinishExam = useCallback(async () => {
    if (isSubmitting.current || existingResult) return;
    isSubmitting.current = true;

    try {
      const result = await mutate({
        variables: {
          answers: userAnswers,
          applicationId: application.id.toString(),
          categorySlug: categoryName.toLowerCase(),
        },
      });
      if (result.data) {
        onResultReceived(result.data);
      }
    } finally {
      isSubmitting.current = false;
    }
  }, [
    mutate,
    userAnswers,
    application,
    categoryName,
    existingResult,
    onResultReceived,
  ]);

  useEffect(() => {
    handleSubmitFinishExam().then();
  }, [handleSubmitFinishExam]);

  const handleGenerateNewTest = () => {
    window.location.reload();
  };

  const handleCorrectedExam = () => {
    // Kontrola, zda je aplikace zakoupena
    if (application.isPaid) {
      props.setIndexQuestion(0);
      props.showCorrectAnswerAndUserAnswerByFinished(true);
    } else {
      // Pokud aplikace není zakoupena, zobrazíme modální okno
      setIsPaymentModalOpen(true);
    }
  };

  const handleShowIncorrectQuestions = () => {
    // Kontrola, zda je aplikace zakoupena
    if (application.isPaid) {
      // Pokud je aplikace zakoupena, zobrazíme chybné otázky
      document.querySelectorAll("details").forEach((detail) => {
        detail.open = true;
      });
    } else {
      // Pokud aplikace není zakoupena, zobrazíme modální okno
      setIsPaymentModalOpen(true);
    }
  };

  if (loading || (!data && !props.existingResult)) {
    return <></>;
  }

  const resultData = props.existingResult || data;
  if (!resultData) {
    return <></>;
  }

  return (
    <div className="sm:max-w-4xl sm:mx-auto sm:px-4 py-4 sm:py-8">
      <div className="bg-white sm:rounded-2xl shadow-lg sm:p-6 p-3">
        <div className="space-y-4 sm:space-y-6">
          {/* Statistiky */}
          <ExamStatistic data={resultData} />

          {/* Odkaz na statistiky - pouze pro přihlášené uživatele */}
          {session && (
            <div className="mt-4 text-center">
              <Link
                href={`/${application.slug}/statistiky`}
                className="inline-flex items-center text-gray-600 hover:text-gray-800 font-medium transition-colors underline hover:no-underline"
              >
                <svg
                  className="w-4 h-4 mr-1.5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
                <span>Podrobné statistiky</span>
              </Link>
            </div>
          )}

          {/* Akční tlačítka */}
          <div className="mt-8 max-w-4xl mx-auto">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <button
                onClick={handleCorrectedExam}
                className="group relative overflow-hidden rounded-xl bg-white px-6 py-4 shadow-md transition-all duration-300 hover:shadow-lg border border-gray-200"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative flex items-center justify-center gap-3">
                  <div className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-600 group-hover:bg-blue-200 transition-colors">
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <span className="text-gray-800 font-medium group-hover:text-blue-700 transition-colors">
                    {application.isPaid ? (
                      "Projít opravený test"
                    ) : (
                      <span className="flex items-center">
                        Projít opravený test
                        <span className="ml-2 px-2 py-0.5 text-xs bg-amber-100 text-amber-700 rounded-full">
                          Premium
                        </span>
                      </span>
                    )}
                  </span>
                </div>
              </button>

              <button
                onClick={handleGenerateNewTest}
                className="group relative overflow-hidden rounded-xl bg-white px-6 py-4 shadow-md transition-all duration-300 hover:shadow-lg border border-gray-200"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-emerald-50 to-emerald-100 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative flex items-center justify-center gap-3">
                  <div className="flex items-center justify-center w-10 h-10 rounded-full bg-emerald-100 text-emerald-600 group-hover:bg-emerald-200 transition-colors">
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      />
                    </svg>
                  </div>
                  <span className="text-gray-800 font-medium group-hover:text-emerald-700 transition-colors">
                    Spustit nový test
                  </span>
                </div>
              </button>
            </div>
          </div>

          {/* Sekce s chybnými odpověďmi */}
          <div className="mt-8 sm:mt-12">
            <button
              onClick={handleShowIncorrectQuestions}
              className="w-full group relative overflow-hidden rounded-xl bg-white px-6 py-4 shadow-md transition-all duration-300 hover:shadow-lg border border-gray-200 mb-4 sm:mb-6"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-red-50 to-red-100 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative flex items-center justify-center gap-3">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-red-100 text-red-600 group-hover:bg-red-200 transition-colors">
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
                <span className="text-gray-800 font-medium group-hover:text-red-700 transition-colors">
                  {application.isPaid ? (
                    "Rozbalit chybné otázky"
                  ) : (
                    <span className="flex items-center">
                      Rozbalit chybné otázky
                      <span className="ml-2 px-2 py-0.5 text-xs bg-amber-100 text-amber-700 rounded-full">
                        Premium
                      </span>
                    </span>
                  )}
                </span>
              </div>
            </button>

            {/* Seznam sekcí */}
            <div className="space-y-4 sm:space-y-6">
              {resultData.evaluateExam.sections.map((section) => (
                <div
                  key={section.sectionId}
                  className="bg-gray-50 rounded-lg sm:rounded-xl p-3 sm:p-6 border border-gray-100"
                >
                  <div className="flex items-center justify-between mb-3 sm:mb-4">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-800">
                      {section.title}
                    </h3>
                    <span className="text-xs sm:text-sm font-medium text-gray-500">
                      {section.totalPoints}/{section.maximumPoints} bodů
                    </span>
                  </div>

                  {/* Progress bar */}
                  <div className="w-full bg-gray-200 rounded-full h-2 sm:h-2.5">
                    <div
                      className="bg-gradient-to-r from-green-500 to-green-600 h-2 sm:h-2.5 rounded-full transition-all duration-500"
                      style={{
                        width: `${(section.totalPoints / section.maximumPoints) * 100}%`,
                      }}
                    />
                  </div>

                  {/* Chybné otázky */}
                  {section.totalPoints !== section.maximumPoints && (
                    <details className="mt-3 sm:mt-4 group">
                      <summary className="cursor-pointer text-xs sm:text-sm font-medium text-blue-600 hover:text-blue-700 flex items-center">
                        <svg
                          className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2 transform transition-transform group-open:rotate-180"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                        {application.isPaid ? (
                          "Zobrazit chybné otázky"
                        ) : (
                          <span className="flex items-center">
                            Zobrazit chybné otázky
                            <span className="ml-2 px-2 py-0.5 text-xs bg-amber-100 text-amber-700 rounded-full">
                              Premium
                            </span>
                          </span>
                        )}
                      </summary>

                      <div className="mt-3 sm:mt-4 space-y-3 sm:space-y-4">
                        {application.isPaid ? (
                          // Obsah pro uživatele s placenou verzí
                          props.questions
                            .filter(
                              (question) =>
                                !question.questionGroup ||
                                (question.questionGroup &&
                                  question.questionGroup.title ===
                                    section.title),
                            )
                            .map((question) => {
                              const userAnswer = props.userAnswers.find(
                                (answerUser) =>
                                  answerUser.questionId === question.id,
                              );
                              const correctAnswer = question.answers.find(
                                (answer) => answer.correct,
                              );

                              if (userAnswer?.answerId !== correctAnswer?.id) {
                                return (
                                  <div
                                    key={question.id}
                                    className="bg-white rounded-lg p-3 sm:p-4 border border-gray-200"
                                  >
                                    <p className="text-sm sm:text-base text-gray-800 font-medium mb-2 sm:mb-3">
                                      {question.text}
                                    </p>
                                    <div className="space-y-1.5 sm:space-y-2">
                                      <div className="flex items-center text-green-600">
                                        <svg
                                          className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2"
                                          fill="none"
                                          stroke="currentColor"
                                          viewBox="0 0 24 24"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth="2"
                                            d="M5 13l4 4L19 7"
                                          />
                                        </svg>
                                        <span className="text-xs sm:text-sm">
                                          Správná odpověď: {correctAnswer?.text}
                                        </span>
                                      </div>
                                      <div className="flex items-center text-red-600">
                                        <svg
                                          className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2"
                                          fill="none"
                                          stroke="currentColor"
                                          viewBox="0 0 24 24"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth="2"
                                            d="M6 18L18 6M6 6l12 12"
                                          />
                                        </svg>
                                        <span className="text-xs sm:text-sm">
                                          Vaše odpověď:{" "}
                                          {
                                            question.answers.find(
                                              (answer) =>
                                                answer.id ===
                                                userAnswer?.answerId,
                                            )?.text
                                          }
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                );
                              }
                              return null;
                            })
                        ) : (
                          // Obsah pro uživatele bez placené verze
                          <div className="bg-amber-50 rounded-lg p-4 border border-amber-200">
                            <div className="flex items-center gap-3 mb-2">
                              <svg
                                className="w-5 h-5 text-amber-500"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                              </svg>
                              <span className="font-medium text-amber-800">
                                Prémiová funkce
                              </span>
                            </div>
                            <p className="text-sm text-amber-700 mb-3">
                              Zobrazení chybných otázek je dostupné pouze pro
                              uživatele s placenou verzí.
                            </p>
                            <button
                              onClick={() => setIsPaymentModalOpen(true)}
                              className="w-full py-2 px-4 bg-amber-500 hover:bg-amber-600 text-white rounded-lg transition-colors text-sm font-medium"
                            >
                              Zobrazit možnosti zakoupení
                            </button>
                          </div>
                        )}
                      </div>
                    </details>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Modální okno pro nezakoupenou aplikaci */}
      <PaymentRequiredModal
        isOpen={isPaymentModalOpen}
        onCloseAction={() => setIsPaymentModalOpen(false)}
        application={application}
      />
    </div>
  );
};
