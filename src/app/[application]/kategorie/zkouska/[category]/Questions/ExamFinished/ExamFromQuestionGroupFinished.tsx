import { ExamByQuestionGroup_examByQuestionGroup_questions } from "@/graphql/types/ExamByQuestionGroup";
import { UserAnswer } from "@/app/[application]/kategorie/zkouska/[category]/Questions/Questions";
import {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useRef,
} from "react";
import Styles from "./styles.module.css";
import { useMutation } from "@apollo/client";
import { MUTATION_EVALUATE_QUESTION_GROUP_EXAM } from "@/app/[application]/kategorie/zkouska/[category]/Questions/ExamFinished/mutation";
import {
  EvaluateQuestionGroupExam,
  EvaluateQuestionGroupExamVariables,
} from "@/graphql/types/EvaluateQuestionGroupExam";
import { ExamFromQuestionGroupStatistic } from "@/app/[application]/kategorie/zkouska/[category]/Questions/ExamFinished/ExamStatistic/ExamFromQuestionGroupStatistic";
import { Section_section } from "@/graphql/types/Section";
import { Application_application } from "@/graphql/types/Application";

interface Props {
  questions: ExamByQuestionGroup_examByQuestionGroup_questions[];
  userAnswers: UserAnswer[];
  application: Application_application;
  categoryName: string;
  setIndexQuestion: Dispatch<SetStateAction<number>>;
  showCorrectAnswerAndUserAnswerByFinished: Dispatch<SetStateAction<boolean>>;
  typeExamFinished: "allQuestions" | "sectionExam";
  section: Section_section;
  existingResult: EvaluateQuestionGroupExam | null;
  onResultReceived: (result: EvaluateQuestionGroupExam) => void;
}

export const ExamFromQuestionGroupFinished = (props: Props) => {
  const isSubmitting = useRef(false);

  // Destructure props outside useCallback
  const {
    userAnswers,
    application,
    categoryName,
    existingResult,
    onResultReceived,
    section,
    typeExamFinished,
  } = props;

  const [mutate, { data, loading }] = useMutation<
    EvaluateQuestionGroupExam,
    EvaluateQuestionGroupExamVariables
  >(MUTATION_EVALUATE_QUESTION_GROUP_EXAM);

  const handleSubmitFinishExam = useCallback(async () => {
    if (isSubmitting.current || existingResult) return;
    isSubmitting.current = true;

    try {
      const result = await mutate({
        variables: {
          answers: userAnswers,
          applicationId: application.id.toString(),
          categorySlug: categoryName.toLowerCase(),
          questionGroupId: section.questionGroup.id,
          evaluateAllQuestions: typeExamFinished === "allQuestions",
        },
      });
      if (result.data) {
        onResultReceived(result.data);
      }
    } finally {
      isSubmitting.current = false;
    }
  }, [
    mutate,
    userAnswers,
    application,
    categoryName,
    section.questionGroup.id,
    typeExamFinished,
    existingResult,
    onResultReceived,
  ]);

  useEffect(() => {
    handleSubmitFinishExam().then();
  }, [handleSubmitFinishExam]);

  const handleGenerateNewTest = () => {
    window.location.reload();
  };

  const handleCorrectedExam = () => {
    props.setIndexQuestion(0);
    props.showCorrectAnswerAndUserAnswerByFinished(true);
  };

  const handleShowIncorrectQuestions = () => {
    document.querySelectorAll("details").forEach((detail) => {
      detail.open = true;
    });
  };

  if (loading || (!data && !props.existingResult)) {
    return <></>;
  }

  const resultData = props.existingResult || data;
  if (!resultData) {
    return <></>;
  }

  if (props.typeExamFinished === "allQuestions") {
    return (
      <div className={`${Styles.container}`}>
        <div className={Styles.main}>
          <div className={Styles.scrollView}>
            <div className="text-center">
              <p className="text-xl md:text-2xl font-medium mb-8 text-gray-600">
                Prošel si všechny otázky. Gratulujeme!
              </p>
            </div>
            <ExamFromQuestionGroupStatistic
              data={resultData.evaluateQuestionGroupExam}
            />

            <div className="mt-8 max-w-md mx-auto">
              <button
                onClick={handleGenerateNewTest}
                className="w-full group relative overflow-hidden rounded-xl bg-white px-6 py-4 shadow-md transition-all duration-300 hover:shadow-lg border border-gray-200"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-emerald-50 to-emerald-100 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative flex items-center justify-center gap-3">
                  <div className="flex items-center justify-center w-10 h-10 rounded-full bg-emerald-100 text-emerald-600 group-hover:bg-emerald-200 transition-colors">
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                  </div>
                  <span className="text-gray-800 font-medium group-hover:text-emerald-700 transition-colors">
                    Spustit nový test
                  </span>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`${Styles.container}`}>
      <div className={Styles.main}>
        <div className={Styles.scrollView}>
          <div>
            <ExamFromQuestionGroupStatistic
              data={resultData.evaluateQuestionGroupExam}
            />

            <div className={Styles.imageContainer}>
              {/*<img className={Styles.image} src={randomImage()}/>*/}
            </div>

            <div className="mt-8 max-w-4xl mx-auto">
              {/* První řada - 2 tlačítka vedle sebe */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                <button
                  onClick={handleCorrectedExam}
                  className="group relative overflow-hidden rounded-xl bg-white px-6 py-4 shadow-md transition-all duration-300 hover:shadow-lg border border-gray-200"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex items-center justify-center gap-3">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-600 group-hover:bg-blue-200 transition-colors">
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <span className="text-gray-800 font-medium group-hover:text-blue-700 transition-colors">
                      Projít opravený test
                    </span>
                  </div>
                </button>

                <button
                  onClick={handleGenerateNewTest}
                  className="group relative overflow-hidden rounded-xl bg-white px-6 py-4 shadow-md transition-all duration-300 hover:shadow-lg border border-gray-200"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-emerald-50 to-emerald-100 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex items-center justify-center gap-3">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-emerald-100 text-emerald-600 group-hover:bg-emerald-200 transition-colors">
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                        />
                      </svg>
                    </div>
                    <span className="text-gray-800 font-medium group-hover:text-emerald-700 transition-colors">
                      Spustit nový test
                    </span>
                  </div>
                </button>
              </div>

              {/* Tlačítko pro rozbalení chybných otázek */}
              {props.typeExamFinished !== "sectionExam" && (
                <button
                  onClick={handleShowIncorrectQuestions}
                  className="w-full group relative overflow-hidden rounded-xl bg-white px-6 py-4 shadow-md transition-all duration-300 hover:shadow-lg border border-gray-200 mb-4"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-red-50 to-red-100 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex items-center justify-center gap-3">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-red-100 text-red-600 group-hover:bg-red-200 transition-colors">
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </div>
                    <span className="text-gray-800 font-medium group-hover:text-red-700 transition-colors">
                      Rozbalit chybné otázky
                    </span>
                  </div>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
