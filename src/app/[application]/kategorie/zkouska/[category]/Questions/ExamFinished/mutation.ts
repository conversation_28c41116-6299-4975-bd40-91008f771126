import { gql } from "@apollo/client";

export const MUTATION_EVALUATE_EXAM = gql`
  mutation EvaluateExam(
    $applicationId: ID!
    $categorySlug: String!
    $answers: [ExamAnswerInput!]!
  ) {
    evaluateExam(
      applicationId: $applicationId
      categorySlug: $categorySlug
      answers: $answers
    ) {
      isSuccessful
      maximumPoints
      percentageSuccess
      pointsToPass
      totalPoints
      sections {
        sectionId
        title
        totalPoints
        maximumPoints
      }
    }
  }
`;

export const MUTATION_EVALUATE_QUESTION_GROUP_EXAM = gql`
  mutation EvaluateQuestionGroupExam(
    $applicationId: ID!
    $categorySlug: String!
    $answers: [ExamAnswerInput!]!
    $questionGroupId: Int!
    $evaluateAllQuestions: Boolean!
  ) {
    evaluateQuestionGroupExam(
      applicationId: $applicationId
      categorySlug: $categorySlug
      answers: $answers
      questionGroupId: $questionGroupId
      evaluateAllQuestions: $evaluateAllQuestions
    ) {
      maximumPoints
      totalPoints
      percentageSuccess
    }
  }
`;
