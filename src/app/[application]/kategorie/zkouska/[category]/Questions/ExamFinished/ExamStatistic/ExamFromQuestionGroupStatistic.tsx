import { EvaluateQuestionGroupExam_evaluateQuestionGroupExam } from "@/graphql/types/EvaluateQuestionGroupExam";
import { ScoreCircle } from "@/components/ScoreCircle/ScoreCircle";

interface Props {
  data: EvaluateQuestionGroupExam_evaluateQuestionGroupExam;
}

const getColorClasses = (percentage: number) => {
  if (percentage >= 90)
    return { bg: "bg-emerald-200", text: "text-emerald-900" };
  if (percentage >= 80) return { bg: "bg-green-200", text: "text-green-900" };
  if (percentage >= 70) return { bg: "bg-lime-200", text: "text-lime-900" };
  if (percentage >= 60) return { bg: "bg-yellow-200", text: "text-yellow-900" };
  if (percentage >= 50) return { bg: "bg-orange-200", text: "text-orange-900" };
  return { bg: "bg-red-200", text: "text-red-900" };
};

const getMotivationalText = (percentage: number) => {
  if (percentage >= 90) return "Skvělý výsledek! <PERSON> z<PERSON>ušky to jistě zvládneš!";
  if (percentage >= 80) return "Velmi dobrý výsledek!";
  if (percentage >= 70) return "Začíná ti to jít! Nepolevuj!";
  if (percentage >= 60) return "Zvládáš to! Pokračuj v tréninku!";
  if (percentage >= 50) return "Trénink dělá mistra! Příště to bude lepší!";
  return "Zkus to znovu, příště to určitě zvládneš!";
};

export const ExamFromQuestionGroupStatistic = ({ data }: Props) => {
  const colorClasses = getColorClasses(data.percentageSuccess);
  const motivationalText = getMotivationalText(data.percentageSuccess);

  return (
    <div className="bg-white shadow-lg rounded-xl p-6 my-8">
      <div className="space-y-6">
        {/* Result Message */}
        <div className="text-center mb-4">
          <p className={`text-xl md:text-2xl font-medium ${colorClasses.text}`}>
            {motivationalText}
          </p>
        </div>

        <ScoreCircle
          percentage={data.percentageSuccess}
          colorClasses={colorClasses}
          size="lg"
        />

        {/* Points Info */}
        <div className="flex justify-center">
          <div className="bg-gray-50 rounded-full px-6 py-3 inline-flex items-center">
            <svg
              className="w-5 h-5 text-gray-400 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"
              />
            </svg>
            <span className="text-lg font-medium text-gray-700">
              {data.totalPoints} z {data.maximumPoints} bodů
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
