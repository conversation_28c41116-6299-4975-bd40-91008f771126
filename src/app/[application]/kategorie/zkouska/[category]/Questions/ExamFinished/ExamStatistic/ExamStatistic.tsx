import { EvaluateExam } from "@/graphql/types/EvaluateExam";
import { ScoreCircle } from "@/components/ScoreCircle/ScoreCircle";

interface Props {
  data: EvaluateExam;
}

export const ExamStatistic = ({ data }: Props) => {
  const isSuccessful = data.evaluateExam.isSuccessful;
  const percentage = data.evaluateExam.percentageSuccess;
  const colorClasses = {
    bg: isSuccessful ? "bg-green-100" : "bg-red-100",
    text: isSuccessful ? "text-green-600" : "text-red-600",
  };

  return (
    <div className="text-center space-y-4">
      {/* Status Badge */}
      <div
        className={`inline-flex items-center px-3 py-1.5 rounded-full text-sm
        ${
          isSuccessful
            ? "bg-green-50 text-green-700 border border-green-200"
            : "bg-red-50 text-red-700 border border-red-200"
        }`}
      >
        <svg
          className={`w-4 h-4 mr-1.5 ${isSuccessful ? "text-green-500" : "text-red-500"}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          {isSuccessful ? (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          ) : (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          )}
        </svg>
        <span className="font-medium">
          {isSuccessful
            ? "Zkouška úspěšně dokončena"
            : "Zkouška neúspěšně dokončena"}
        </span>
      </div>

      <ScoreCircle
        percentage={percentage}
        colorClasses={colorClasses}
        size="sm"
      />

      {/* Points Info */}
      <div className="bg-gray-50 rounded-lg p-3 inline-flex items-center">
        <div className="flex items-center">
          <svg
            className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400 mr-1.5 sm:mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"
            />
          </svg>
          <span className="text-base sm:text-lg font-medium text-gray-700">
            {data.evaluateExam.totalPoints} z {data.evaluateExam.maximumPoints}{" "}
            bodů
          </span>
        </div>
      </div>
    </div>
  );
};
