import { Exam_exam_questions_answers } from "@/graphql/types/Exam";
import { UserAnswer } from "@/app/[application]/kategorie/zkouska/[category]/Questions/Questions";
import { useState, useEffect } from "react";

interface Props {
  questionId: number;
  answers: Exam_exam_questions_answers[];
  setIndexQuestion: (index: number) => void;
  indexQuestion: number;
  setUserAnswers: (userAnswers: UserAnswer[]) => void;
  userAnswers: UserAnswer[];
  showCorrectAnswer: boolean;
  showCorrectAnswerAndUserAnswerByFinished: boolean;
}

export const Answers: React.FC<Props> = (props) => {
  const [showedCorrectAnswer, setShowedCorrectAnswer] =
    useState<boolean>(false);
  const [selectedAnswerId, setSelectedAnswerId] = useState<number | null>(null);

  // Resetujeme vybranou odpověď při změně ot<PERSON>zky
  useEffect(() => {
    setSelectedAnswerId(null);
  }, [props.questionId]);

  const alphabet: { [key: number]: string } = {
    0: "A",
    1: "B",
    2: "C",
    3: "D",
    4: "E",
    5: "F",
  };

  const handleAnswer = (index: number) => {
    const answerId = props.answers[index].id;

    // Vždy nastavíme vybranou odpověď pro vizualizaci
    setSelectedAnswerId(answerId);

    // Kontrola, zda již existuje odpověď pro tuto otázku
    const hasExistingAnswer = props.userAnswers.some(
      (userAnswer) => userAnswer.questionId === props.questionId,
    );

    // Pokud se zobrazují správné odpovědi nebo je test dokončen, přejdeme na další otázku
    if (
      props.showCorrectAnswerAndUserAnswerByFinished ||
      (props.showCorrectAnswer && showedCorrectAnswer) ||
      hasExistingAnswer
    ) {
      props.setIndexQuestion(props.indexQuestion + 1);

      return;
    }

    // Přidáme odpověď do userAnswers v každém případě
    const newUserAnswer = {
      questionId: props.questionId,
      answerId: answerId,
    };

    // Nejprve přidáme odpověď do userAnswers
    props.setUserAnswers([...props.userAnswers, newUserAnswer]);

    if (props.showCorrectAnswer && !showedCorrectAnswer) {
      // Pokud máme zobrazit správnou odpověď, nastavíme příslušný stav
      setShowedCorrectAnswer(true);
    } else {
      // Přejdeme na další otázku po krátkém zpoždění
      setTimeout(() => {
        props.setIndexQuestion(props.indexQuestion + 1);
      }, 500); // 500ms zpoždění
    }
  };

  const getAnswerStyles = (answer: Exam_exam_questions_answers) => {
    // Základní styly pro nepříznakovou odpověď
    let styles = {
      letter:
        "bg-gray-100 text-gray-700 border-gray-200 group-hover:bg-gray-200",
      text: "text-gray-700",
      container: "border-gray-200 bg-white",
    };

    // Kontrola, zda je odpověď aktuálně vybraná uživatelem
    const isSelectedFromUserAnswers = props.userAnswers.some(
      (userAnswer) =>
        userAnswer.questionId === props.questionId &&
        userAnswer.answerId === answer.id,
    );

    // Kontrola, zda je odpověď aktuálně vybraná lokálně
    const isSelectedLocally = selectedAnswerId === answer.id;

    // Kontrola, zda je odpověď vybrána (buď z userAnswers nebo lokálně)
    const isSelected = isSelectedFromUserAnswers || isSelectedLocally;

    // Zobrazování správných a chybných odpovědí po dokončení testu
    if (props.showCorrectAnswerAndUserAnswerByFinished) {
      const isUserAnswer = props.userAnswers.some(
        (userAnswer) =>
          userAnswer.questionId === props.questionId &&
          userAnswer.answerId === answer.id,
      );

      if (answer.correct) {
        // Správná odpověď - zelená
        styles = {
          letter: "bg-green-600 text-white border-green-600",
          text: "text-green-700",
          container: "border-green-200 bg-green-50",
        };
      } else if (isUserAnswer) {
        // Chybná uživatelova odpověď - červená
        styles = {
          letter: "bg-red-600 text-white border-red-600",
          text: "text-red-700",
          container: "border-red-200 bg-red-50",
        };
      }
    }
    // Zobrazování správných a chybných odpovědí po zobrazení správných odpovědí
    else if (props.showCorrectAnswer && showedCorrectAnswer) {
      if (answer.correct) {
        styles = {
          letter: "bg-green-600 text-white border-green-600",
          text: "text-green-700",
          container: "border-green-200 bg-green-50",
        };
      } else {
        styles = {
          letter: "bg-red-600 text-white border-red-600",
          text: "text-red-700",
          container: "border-red-200 bg-red-50",
        };
      }
    }

    // Přidání modrého okraje pro vybrané odpovědi vždy
    if (isSelected) {
      styles.container += " ring-2 ring-blue-500";
    }

    return styles;
  };

  return (
    <div className="space-y-3 mt-4">
      {props.answers.map((answer, index) => {
        const styles = getAnswerStyles(answer);

        return (
          <button
            key={`answer_${answer.id}`}
            onClick={() => handleAnswer(index)}
            className="w-full group focus:outline-none cursor-pointer"
          >
            <div
              className={`
              flex items-stretch overflow-hidden
              border rounded-xl
              transition-all duration-200
              ${styles.container}
              group-hover:border-gray-300 group-active:scale-[0.99]
              group-focus:ring-2 group-focus:ring-blue-500 group-focus:ring-offset-2
            `}
            >
              <div
                className={`
                flex items-center justify-center
                w-10 sm:w-14
                border-r
                transition-colors duration-200
                ${styles.letter}
              `}
              >
                <span className="font-semibold text-lg sm:text-xl">
                  {alphabet[index]}
                </span>
              </div>

              <div className="flex-1 p-3.5 sm:p-4 text-left">
                <span
                  className={`
                  block text-[15px] sm:text-base
                  leading-relaxed
                  transition-colors duration-200
                  ${styles.text}
                `}
                >
                  {answer.text}
                </span>
              </div>
            </div>
          </button>
        );
      })}
    </div>
  );
};
