import { gql } from "@apollo/client";

export const ADD_FAVOURITE_QUESTION = gql`
  mutation AddFavouriteQuestion($applicationId: ID!, $questionId: ID!) {
    addFavouriteQuestion(applicationId: $applicationId, questionId: $questionId)
  }
`;

export const REMOVE_FAVOURITE_QUESTION = gql`
  mutation RemoveFavouriteQuestion($applicationId: ID!, $questionId: ID!) {
    removeFavouriteQuestion(
      applicationId: $applicationId
      questionId: $questionId
    )
  }
`;
