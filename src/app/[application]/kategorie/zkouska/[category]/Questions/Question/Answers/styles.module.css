/* Container for item layout */
.itemContainer {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    width: 100%;
}

@media (max-width: 639px) {
    .itemContainer {
        padding: 12px;
    }
}

@media (min-width: 640px) {
    .itemContainer {
        padding: 16px;
    }
}

/* Styling for the next question button */
.nextQuestionButton {
    margin-top: 50px;
}

/* Styling for the next question text */
.nextQuestionsText {
    color: black;
    font-size: 20px;
}
