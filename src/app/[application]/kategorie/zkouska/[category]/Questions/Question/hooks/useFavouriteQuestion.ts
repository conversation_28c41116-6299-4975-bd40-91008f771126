import { useMutation } from "@apollo/client";
import { useSession } from "next-auth/react";
import { ExtendedDefaultSession } from "@/types/extendedDefaultSession";
import { ADD_FAVOURITE_QUESTION, REMOVE_FAVOURITE_QUESTION } from "../mutation";
import { useState, useCallback } from "react";
import {
  AddFavouriteQuestion,
  AddFavouriteQuestionVariables,
} from "@/graphql/types/AddFavouriteQuestion";
import { RemoveFavouriteQuestion } from "@/graphql/types/RemoveFavouriteQuestion";

export const useFavouriteButton = (
  applicationId: number,
  questionId: number,
  isFavouriteQuestionDefault: boolean,
) => {
  const [isFavourite, setIsFavourite] = useState(isFavouriteQuestionDefault);
  const { status, data: sessionData } = useSession();
  const session = sessionData as ExtendedDefaultSession;

  const authHeaders = {
    headers: {
      Authorization: `Bearer ${session?.user?.token}`,
    },
  };

  const [addFavouriteQuestion] = useMutation<
    AddFavouriteQuestion,
    AddFavouriteQuestionVariables
  >(ADD_FAVOURITE_QUESTION, {
    context: authHeaders,
  });

  const [removeFavouriteQuestion] = useMutation<
    RemoveFavouriteQuestion,
    AddFavouriteQuestionVariables
  >(REMOVE_FAVOURITE_QUESTION, {
    context: authHeaders,
  });

  const toggleFavourite = useCallback(async () => {
    if (status !== "authenticated") return;

    try {
      const mutation = isFavourite
        ? removeFavouriteQuestion
        : addFavouriteQuestion;
      await mutation({
        variables: {
          applicationId: applicationId.toString(),
          questionId: questionId.toString(),
        },
      });
      setIsFavourite((prev) => !prev);
    } catch (error) {
      console.error("Failed to toggle favourite:", error);
    }
  }, [
    isFavourite,
    applicationId,
    questionId,
    addFavouriteQuestion,
    removeFavouriteQuestion,
    status,
  ]);

  return {
    status,
    isFavourite,
    toggleFavourite,
  };
};
