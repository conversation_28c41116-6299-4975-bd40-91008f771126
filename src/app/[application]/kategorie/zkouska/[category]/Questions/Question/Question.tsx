"use client";

import React from "react";
import Image from "next/image";
import { Answers } from "@/app/[application]/kategorie/zkouska/[category]/Questions/Question/Answers/Answers";
import Styles from "./styles.module.css";
import {
  UniversalQuestion,
  UserAnswer,
} from "@/app/[application]/kategorie/zkouska/[category]/Questions/Questions";
import { FavouriteButton } from "@/app/[application]/kategorie/zkouska/[category]/Questions/Question/FavouriteButton";
import { VideoPlayer } from "./VideoPlayer/VideoPlayer";

interface Props {
  question: UniversalQuestion;
  setIndexQuestion: (index: number) => void;
  indexQuestion: number;
  setUserAnswers: (userAnswers: UserAnswer[]) => void;
  userAnswers: UserAnswer[];
  showCorrectAnswer: boolean;
  applicationId: number;
  isFavouriteQuestionDefault: boolean;
  showCorrectAnswerAndUserAnswerByFinished: boolean;
  favouriteButtonPosition?: "top" | "bottom";
  questionNumber?: number;
  autoplay?: boolean; // Nová propsa
}

export const Question: React.FC<Props> = ({
  question,
  setIndexQuestion,
  indexQuestion,
  setUserAnswers,
  userAnswers,
  showCorrectAnswer,
  applicationId,
  isFavouriteQuestionDefault,
  showCorrectAnswerAndUserAnswerByFinished,
  favouriteButtonPosition = "top",
  questionNumber,
  autoplay = true, // Výchozí hodnota true
}) => {
  const favouriteButton = (
    <FavouriteButton
      applicationId={applicationId}
      questionId={question.id}
      isFavouriteQuestionDefault={isFavouriteQuestionDefault}
    />
  );

  return (
    <>
      {/* Render FavouriteButton na horní pozici */}
      {favouriteButtonPosition === "top" && (
        <div className="flex justify-end mb-2 sm:mb-4">{favouriteButton}</div>
      )}

      <div className="bg-white rounded-lg shadow-sm p-3 sm:p-6">
        {/* Kontejner pro média (obrázek nebo video) */}
        {(question.image !== null ||
          (question.video !== null && question.video)) && (
          <div className={Styles.containerImage}>
            {/* Obrázek otázky */}
            {question.image !== null ? (
              <Image
                width={200}
                height={200}
                className={Styles.imageExamList}
                src={question.image?.url}
                alt={question.image?.fileName || "image"}
                priority={true}
                style={{
                  width: "auto",
                  height: "auto",
                  maxWidth: "100%",
                  maxHeight: "200px",
                }}
              />
            ) : question.video !== null && question.video ? (
              <VideoPlayer video={question.video} autoplay={autoplay} />
            ) : null}
          </div>
        )}

        <div className={Styles.questionTextContainer}>
          <p className="text-sm sm:text-base">
            {questionNumber && <span>{questionNumber}. </span>}
            {question.text}
          </p>
        </div>

        <Answers
          answers={question.answers}
          questionId={question.id}
          setIndexQuestion={setIndexQuestion}
          indexQuestion={indexQuestion}
          setUserAnswers={setUserAnswers}
          userAnswers={userAnswers}
          showCorrectAnswer={showCorrectAnswer}
          showCorrectAnswerAndUserAnswerByFinished={
            showCorrectAnswerAndUserAnswerByFinished
          }
        />

        {/* Render FavouriteButton na spodní pozici s oddělovací čárou */}
        {favouriteButtonPosition === "bottom" && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            {favouriteButton}
          </div>
        )}
      </div>
    </>
  );
};
