"use client";

import React, { useState } from "react";
import Image from "next/image";
import { Play } from "lucide-react";

interface VideoPlayerProps {
  video: {
    url: string;
    thumbnailImage?: {
      url: string;
      fileName: string;
    };
  };
  autoplay?: boolean;
}

export const VideoPlayer: React.FC<VideoPlayerProps> = ({
  video,
  autoplay = true,
}) => {
  const [isPlaying, setIsPlaying] = useState(autoplay);
  const [isLoading, setIsLoading] = useState(false);
  const [videoError, setVideoError] = useState(false);

  const handleVideoLoadStart = () => {
    setIsLoading(true);
    setVideoError(false);
  };

  const handleVideoCanPlay = () => {
    setIsLoading(false);
  };

  const handleVideoErrored = () => {
    setIsLoading(false);
    setVideoError(true);
  };

  const containerStyle = {
    position: "relative",
    width: "100%",
    maxWidth: "420px",
    aspectRatio: "16/9",
    height: "200px", // Fixní výška odpovídající min-height v CSS
  } as const;

  const mediaStyle = {
    position: "absolute",
    top: "0",
    left: "0",
    width: "100%",
    height: "100%",
    objectFit: "contain",
    backgroundColor: "#000",
  } as const;

  if (!isPlaying && video.thumbnailImage) {
    return (
      <div className="flex justify-center items-center" style={containerStyle}>
        <div className="relative w-full h-full">
          <Image
            fill
            src={video.thumbnailImage.url}
            alt={video.thumbnailImage.fileName || "Video thumbnail"}
            priority={true}
            className="rounded-lg"
            style={mediaStyle}
            sizes="(max-width: 420px) 100vw, 420px"
          />
          <div
            className="absolute inset-0 flex items-center justify-center cursor-pointer"
            onClick={() => setIsPlaying(true)}
          >
            <div
              className="bg-black bg-opacity-50 rounded-full p-4 transition-transform hover:scale-110"
              aria-label="Play video"
            >
              <Play className="w-8 h-8 text-white" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center" style={containerStyle}>
      <video
        style={mediaStyle}
        controls
        autoPlay={isPlaying}
        playsInline={true}
        className="rounded-lg"
        onLoadStart={handleVideoLoadStart}
        onCanPlay={handleVideoCanPlay}
        onError={handleVideoErrored}
      >
        <source src={video.url} type="video/mp4" />
        Váš prohlížeč nepodporuje přehrávání videa.
      </video>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          Loading...
        </div>
      )}
      {videoError && (
        <div className="absolute inset-0 flex items-center justify-center text-red-500">
          Error loading video.
        </div>
      )}
    </div>
  );
};
