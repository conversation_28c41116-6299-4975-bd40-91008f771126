"use client";

import { Heart } from "lucide-react";
import { useFavouriteButton } from "./hooks/useFavouriteQuestion";
import Skeleton from "@/components/Skeleton";

interface Props {
  questionId: number;
  applicationId: number;
  isFavouriteQuestionDefault: boolean;
}

export const FavouriteButton = ({
  applicationId,
  questionId,
  isFavouriteQuestionDefault,
}: Props) => {
  const { status, isFavourite, toggleFavourite } = useFavouriteButton(
    applicationId,
    questionId,
    isFavouriteQuestionDefault,
  );

  if (status === "loading") {
    return <Skeleton height="40px" />;
  }

  if (status !== "authenticated") {
    return null;
  }

  return (
    <button
      onClick={toggleFavourite}
      className={`
        inline-flex items-center gap-2
        px-4 py-2.5
        rounded-xl
        border-2
        font-medium
        transition-all duration-200
        focus:outline-none
        shadow-sm
        ${
          isFavourite
            ? "bg-white text-gray-400 border-gray-100 hover:bg-gray-50"
            : "bg-white text-emerald-600 border-emerald-100 hover:border-emerald-200 hover:bg-emerald-50"
        }
        active:scale-[0.98]
      `}
      aria-label={isFavourite ? "Odebrat z oblíbených" : "Přidat do oblíbených"}
    >
      <Heart
        className={`w-5 h-5 transition-transform ${!isFavourite && "hover:scale-110"}`}
        fill={isFavourite ? "currentColor" : "none"}
        strokeWidth={2}
      />
      <span className="text-sm sm:text-base">
        {isFavourite ? "Odebrat z oblíbených" : "Přidat do oblíbených"}
      </span>
    </button>
  );
};
