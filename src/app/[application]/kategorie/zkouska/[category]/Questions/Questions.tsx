"use client";

import { Question } from "@/app/[application]/kategorie/zkouska/[category]/Questions/Question/Question";
import { useState, useEffect, useRef } from "react";
import { ExamByQuestionGroup_examByQuestionGroup_questions } from "@/graphql/types/ExamByQuestionGroup";
import { Exam_exam_questions } from "@/graphql/types/Exam";
import { ExamFinished } from "@/app/[application]/kategorie/zkouska/[category]/Questions/ExamFinished/ExamFinished";
import { ExamFromQuestionGroupFinished } from "@/app/[application]/kategorie/zkouska/[category]/Questions/ExamFinished/ExamFromQuestionGroupFinished";
import { Section_section } from "@/graphql/types/Section";
import { EvaluateExam } from "@/graphql/types/EvaluateExam";
import { EvaluateQuestionGroupExam } from "@/graphql/types/EvaluateQuestionGroupExam";
import { Application_application } from "@/graphql/types/Application";

export type UniversalQuestion =
  | ExamByQuestionGroup_examByQuestionGroup_questions
  | Exam_exam_questions;

function isExamByQuestionGroup(
  questions: UniversalQuestion[],
): questions is ExamByQuestionGroup_examByQuestionGroup_questions[] {
  return (
    questions[0].__typename === "QuestionPayload" &&
    !("questionGroup" in questions[0])
  );
}

function isExam(
  questions: UniversalQuestion[],
): questions is Exam_exam_questions[] {
  return (
    questions[0].__typename === "QuestionPayload" &&
    "questionGroup" in questions[0]
  );
}

interface Props {
  questions: UniversalQuestion[];
  showCorrectAnswer: boolean;
  application: Application_application;
  categoryName: string;
  favouriteQuestionIds?: number[];
  typeExamFinished?: "allQuestions" | "sectionExam";
  section?: Section_section;
}

export interface UserAnswer {
  questionId: number;
  answerId: number;
}

export const Questions = ({
  questions,
  showCorrectAnswer,
  application,
  categoryName,
  favouriteQuestionIds = [],
  typeExamFinished,
  section,
}: Props) => {
  const [indexQuestion, setIndexQuestion] = useState<number>(0);
  const [userAnswers, setUserAnswers] = useState<UserAnswer[]>([]);
  const [
    showCorrectAnswerAndUserAnswerByFinished,
    setShowCorrectAnswerAndUserAnswerByFinished,
  ] = useState<boolean>(false);
  const [examResult, setExamResult] = useState<EvaluateExam | null>(null);
  const [questionGroupExamResult, setQuestionGroupExamResult] =
    useState<EvaluateQuestionGroupExam | null>(null);

  // Reference na kontejner otázky pro scrollování
  const questionContainerRef = useRef<HTMLDivElement>(null);

  // Sledujeme změny v userAnswers a kontrolujeme, zda máme odpověď na poslední otázku
  useEffect(() => {
    // Pokud jsme na poslední otázce a máme odpověď na tuto otázku
    if (indexQuestion === questions.length - 1) {
      const hasAnswerForLastQuestion = userAnswers.some(
        (answer) => answer.questionId === questions[indexQuestion].id,
      );

      if (hasAnswerForLastQuestion) {
        // Počkáme 1 sekundu a pak přejdeme na další otázku (což zobrazí výsledky testu)
        const timer = setTimeout(() => {
          setIndexQuestion(indexQuestion + 1);
        }, 1000);

        return () => clearTimeout(timer);
      }
    }
  }, [indexQuestion, questions, userAnswers, setIndexQuestion]);

  // Scrollování na začátek otázky při změně indexQuestion, pouze pokud není viditelná
  useEffect(() => {
    // Použijeme setTimeout, aby se scrollování provedlo až po vykreslení nové otázky
    const scrollTimer = setTimeout(() => {
      if (questionContainerRef.current) {
        // Získáme pozici a rozměry kontejneru otázky
        const rect = questionContainerRef.current.getBoundingClientRect();

        // Zjistíme, zda je horní část kontejneru mimo viditelnou oblast
        // Scrollujeme pouze pokud začátek otázky není vidět (top < 0)
        const isTopHidden = rect.top < 0;

        if (isTopHidden) {
          questionContainerRef.current.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }
    }, 100); // Krátké zpoždění pro zajištění správného vykreslení

    return () => clearTimeout(scrollTimer);
  }, [indexQuestion]);

  // Funkce pro přechod na další chybnou otázku
  const handleNextIncorrectQuestion = () => {
    const nextIncorrectIndex = questions.findIndex(
      (q, i) =>
        i > indexQuestion &&
        !userAnswers.some(
          (ua) =>
            ua.questionId === q.id &&
            ua.answerId === q.answers.find((a) => a.correct)?.id,
        ),
    );
    if (nextIncorrectIndex !== -1) {
      setIndexQuestion(nextIncorrectIndex);
    } else {
      setIndexQuestion(indexQuestion + 1);
    }
  };

  if (!questions) {
    return null;
  }

  // Kontrola, zda jsme na konci testu
  if (questions.length === indexQuestion) {
    // Pokud zobrazujeme výsledky testu
    if (isExamByQuestionGroup(questions)) {
      if (!section) {
        return <div>Chyba: Sekce není definována</div>;
      }

      if (!typeExamFinished) {
        return <div>Chyba: Neznámí typ testu</div>;
      }

      return (
        <ExamFromQuestionGroupFinished
          questions={
            questions as ExamByQuestionGroup_examByQuestionGroup_questions[]
          }
          userAnswers={userAnswers}
          application={application}
          categoryName={categoryName}
          setIndexQuestion={setIndexQuestion}
          showCorrectAnswerAndUserAnswerByFinished={
            setShowCorrectAnswerAndUserAnswerByFinished
          }
          typeExamFinished={typeExamFinished}
          section={section}
          existingResult={questionGroupExamResult}
          onResultReceived={setQuestionGroupExamResult}
        />
      );
    } else if (isExam(questions)) {
      return (
        <ExamFinished
          questions={questions as Exam_exam_questions[]}
          userAnswers={userAnswers}
          application={application}
          categoryName={categoryName}
          setIndexQuestion={setIndexQuestion}
          showCorrectAnswerAndUserAnswerByFinished={
            setShowCorrectAnswerAndUserAnswerByFinished
          }
          existingResult={examResult}
          onResultReceived={setExamResult}
        />
      );
    }
  }

  const progressPercentage = ((indexQuestion + 1) / questions.length) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 py-4 sm:py-8 px-2 sm:px-4 rounded-lg">
      <div className="max-w-4xl mx-auto" ref={questionContainerRef}>
        {/* Progress bar */}
        <div className="mb-4 sm:mb-8">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Průběh testu</span>
            <span>
              {indexQuestion + 1} z {questions.length} otázek
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full overflow-hidden">
            <div
              className="bg-teal-500 h-2 transition-all duration-500 ease-in-out"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>

        {/* Question component */}
        <Question
          key={questions[indexQuestion].id}
          question={questions[indexQuestion]}
          setIndexQuestion={setIndexQuestion}
          indexQuestion={indexQuestion}
          setUserAnswers={setUserAnswers}
          userAnswers={userAnswers}
          showCorrectAnswer={showCorrectAnswer}
          applicationId={application.id}
          isFavouriteQuestionDefault={favouriteQuestionIds.includes(
            questions[indexQuestion].id,
          )}
          showCorrectAnswerAndUserAnswerByFinished={
            showCorrectAnswerAndUserAnswerByFinished
          }
        />

        {/* Navigation buttons */}
        {showCorrectAnswerAndUserAnswerByFinished && (
          <div className="mt-6 sm:mt-10 flex flex-col sm:flex-row gap-4 justify-center">
            <button
              className="order-2 sm:order-1 inline-flex items-center px-8 py-3 border bg-white border-emerald-200
                text-emerald-600 hover:text-emerald-700 hover:border-emerald-300
                rounded-xl font-medium shadow-lg hover:shadow-xl
                transition-all duration-300"
              onClick={() => setIndexQuestion(indexQuestion + 1)}
            >
              <span>Další otázka</span>
              <svg
                className="ml-2 -mr-1 w-5 h-5"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
            <button
              className="order-1 sm:order-2 inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-xl
                text-white bg-emerald-600 hover:bg-emerald-700
                transition-all duration-300
                shadow-xl hover:shadow-2xl"
              onClick={handleNextIncorrectQuestion}
            >
              <span>Další chybná otázka</span>
              <svg
                className="ml-2 -mr-1 w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
