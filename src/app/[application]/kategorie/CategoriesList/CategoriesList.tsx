"use server";

import React from "react";
import { CategoryItem } from "@/app/[application]/kategorie/CategoryItem/CategoryItem";
import { Categories_categories_category } from "@/graphql/types/Categories";
import { Application_application } from "@/graphql/types/Application";
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth.config";
import { ExtendedDefaultSession } from "@/types/extendedDefaultSession";

interface CategoriesListProps {
  categories: Categories_categories_category[] | null | undefined;
  application: Application_application;
}

export const CategoriesList = async ({
  categories,
  application,
}: CategoriesListProps) => {
  const session = (await getServerSession(
    authConfig,
  )) as ExtendedDefaultSession;
  const isAuthenticated = !!session?.user;

  if (!categories || categories.length === 0) {
    return (
      <div className="text-center py-8">Žádné kategorie nebyly nalezeny</div>
    );
  }

  const sortedCategories = [...categories].sort((a, b) => {
    if (isAuthenticated) {
      if (a.isFavourite && !b.isFavourite) return -1;
      if (!a.isFavourite && b.isFavourite) return 1;
    }

    return a.priority - b.priority;
  });

  const favouriteCategories = sortedCategories.filter((cat) => cat.isFavourite);
  const regularCategories = sortedCategories.filter((cat) => !cat.isFavourite);

  const showFavouritesSection =
    isAuthenticated && favouriteCategories.length > 0;

  return (
    <div className="space-y-10">
      {showFavouritesSection && (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-gray-900 flex items-center">
            <span className="mr-2 text-amber-500">★</span>
            Oblíbené kategorie
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {favouriteCategories.map((category) => (
              <div key={category.name} className="transition-all duration-300">
                <CategoryItem category={category} application={application} />
              </div>
            ))}
          </div>
        </div>
      )}

      <div className={showFavouritesSection ? "space-y-4" : ""}>
        {showFavouritesSection && (
          <h3 className="text-xl font-semibold text-gray-900">Kategorie</h3>
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {(showFavouritesSection ? regularCategories : sortedCategories).map(
            (category) => (
              <div key={category.name} className="transition-all duration-300">
                <CategoryItem category={category} application={application} />
              </div>
            ),
          )}
        </div>
      </div>
    </div>
  );
};
