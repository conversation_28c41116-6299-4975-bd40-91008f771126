"use server";

import React from "react";
import Menu from "@/components/Menu/Menu";
import { Questions } from "@/app/[application]/kategorie/zkouska/[category]/Questions/Questions";
import { getApplicationBySlug } from "@/lib/services/applications";
import { getCategoryBySlug } from "@/lib/services/categories";
import { getQuestionsByCategory } from "@/lib/services/questions";
import NotFound from "@/components/NotFound/NotFound";
import { getFavouriteQuestions } from "@/app/[application]/kategorie/zkouska/[category]/services/getFavouriteQuestions";
import { QuestionsWrapper } from "@/components/QuestionsWrapper/QuestionsWrapper";

type Params = Promise<{ application: string; category: string }>;

export default async function PracticePage({ params }: { params: Params }) {
  const { application, category } = await params;
  const applicationData = await getApplicationBySlug(application);

  if (!applicationData) {
    return <NotFound message="Aplikace nenalezena" />;
  }

  const categoryData = await getCategoryBySlug(category, applicationData);

  if (!categoryData) {
    return (
      <NotFound application={applicationData} message="Kategorie nenalezena" />
    );
  }

  const [questions, favouriteQuestionIds] = await Promise.all([
    getQuestionsByCategory(applicationData, categoryData),
    getFavouriteQuestions(applicationData.id),
  ]);

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="fixed top-0 left-0 right-0 z-50 bg-white shadow-sm h-[60px] sm:h-auto">
        <Menu application={applicationData} />
      </div>

      <QuestionsWrapper
        title={categoryData.title}
        description="Procvičování otázek s okamžitou zpětnou vazbou"
      >
        <Questions
          questions={questions}
          showCorrectAnswer={true}
          categoryName={category}
          application={applicationData}
          favouriteQuestionIds={favouriteQuestionIds}
          typeExamFinished={"sectionExam"}
        />
      </QuestionsWrapper>
    </div>
  );
}
