"use server";

import React from "react";
import Menu from "@/components/Menu/Menu";
import { getApplications } from "@/lib/services/applications";
import { getApplicationBySlug } from "@/lib/services/applications";
import { getCategories } from "@/lib/services/categories";
import NotFound from "@/components/NotFound/NotFound";
import StudyModesExplainer from "@/components/StudyModesExplainer/StudyModesExplainer";
import { CategoriesList } from "@/app/[application]/kategorie/CategoriesList/CategoriesList";
import StudyModeCards from "@/components/StudyModeCards/StudyModeCards";
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth.config";
import { ExtendedDefaultSession } from "@/types/extendedDefaultSession";

type Params = Promise<{ application: string }>;

export default async function CategoryPage({ params }: { params: Params }) {
  const { application } = await params;
  const applicationData = await getApplicationBySlug(application);

  if (!applicationData) {
    return <NotFound message="Aplikace nenalezena" />;
  }

  // Získání session pro Bearer token
  const session = (await getServerSession(
    authConfig,
  )) as ExtendedDefaultSession;
  const token = session?.user?.token || undefined;

  // Předání Bearer tokenu do getCategories
  const categories = await getCategories(applicationData.id.toString(), token);

  return (
    <>
      {/* Menu */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-white shadow-sm">
        <Menu application={applicationData} />
      </div>
      <div className="relative min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
        {/* Dekorativní prvek - horní část */}
        <div className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80">
          <div className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-emerald-300 to-blue-300 opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"></div>
        </div>

        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute w-full h-full bg-grid opacity-10"></div>
          <div className="absolute -top-40 -left-40 w-80 h-80 bg-emerald-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
          <div className="absolute top-0 -right-40 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
          <div className="absolute bottom-40 left-20 w-80 h-80 bg-emerald-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
        </div>

        {/* Main content */}
        <div className="relative container mx-auto pt-28 lg:pt-32 pb-20 px-4">
          <div className="max-w-7xl mx-auto">
            {/* Sekce kategorií */}
            <div className="mb-24">
              <StudyModesExplainer title="Vyberte si skupinu" />

              <CategoriesList
                categories={categories}
                application={applicationData}
              />
            </div>

            {/* Nová sekce - Další možnosti přípravy */}
            <div className="relative">
              <div className="relative max-w-7xl mx-auto">
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
                    Další možnosti přípravy
                  </h2>
                  <p className="mt-3 max-w-2xl mx-auto text-lg text-gray-600">
                    Prozkoumejte všechny dostupné testové otázky a vytvořte si
                    vlastní plán studia
                  </p>
                </div>

                <StudyModeCards application={applicationData} />
              </div>
            </div>
          </div>
        </div>

        {/* Dekorativní prvek - spodní část */}
        <div className="absolute inset-x-0 bottom-0 -z-10 transform-gpu overflow-hidden blur-3xl">
          <div className="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-blue-300 to-emerald-300 opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"></div>
        </div>
      </div>
    </>
  );
}

export async function generateStaticParams() {
  const applications = await getApplications({ withAuth: false });

  return applications.map((app) => ({
    application: app.slug,
  }));
}
