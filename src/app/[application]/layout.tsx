import dynamic from "next/dynamic";

const FloatingFavoriteButton = dynamic(() =>
  import("@/components/FloatingFavoriteButton/FloatingFavoriteButton").then(
    (mod) => mod.FloatingFavoriteButton,
  ),
);

export default async function ApplicationLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ application: string }>;
}) {
  const { application } = await params;

  return (
    <>
      {children}
      <FloatingFavoriteButton applicationSlug={application} />
    </>
  );
}
