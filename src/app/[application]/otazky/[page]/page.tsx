import React from "react";
import {
  getApplicationBySlug,
  getApplications,
} from "@/lib/services/applications";
import { getAllQuestionsForApplication } from "@/lib/services/getAllQuestionsForApplication";
import NotFound from "@/components/NotFound/NotFound";
import StaticMenu from "@/components/Menu/StaticMenu";
import { StaticQuestion } from "./StaticQuestion";
import { StructuredData } from "./structured-data";
import { generateMetadata } from "./metadata";
import { QuestionsWrapper } from "@/components/QuestionsWrapper/QuestionsWrapper";

// Počet otázek na stránku
const QUESTIONS_PER_PAGE = 50;

export { generateMetadata };

type Params = Promise<{ application: string; page: string }>;

export default async function QuestionsPage({ params }: { params: Params }) {
  const { application, page } = await params;
  const pageNumber = parseInt(page) || 1;

  try {
    // Načtení dat aplikace
    const applicationData = await getApplicationBySlug(application, {
      withAuth: false,
    });

    if (!applicationData) {
      return <NotFound message="Aplikace nenalezena" />;
    }

    // Načtení všech otázek
    const allQuestions = await getAllQuestionsForApplication(
      applicationData.id,
    );

    // Výpočet stránkování
    const totalQuestions = allQuestions.length;
    const totalPages = Math.ceil(totalQuestions / QUESTIONS_PER_PAGE);

    // Kontrola, zda je číslo stránky platné
    if (pageNumber < 1 || pageNumber > totalPages) {
      return <NotFound message="Stránka nenalezena" />;
    }

    // Získání otázek pro aktuální stránku
    const startIndex = (pageNumber - 1) * QUESTIONS_PER_PAGE;
    const endIndex = Math.min(startIndex + QUESTIONS_PER_PAGE, totalQuestions);
    const questionsForPage = allQuestions.slice(startIndex, endIndex);

    return (
      <div className="min-h-screen bg-gray-50">
        <div className="fixed top-0 left-0 right-0 z-50 bg-white shadow-sm">
          <StaticMenu application={applicationData} />
        </div>

        <QuestionsWrapper
          title={`Otázky pro ${applicationData.name} - Stránka ${pageNumber} z ${totalPages}`}
          description={`Prohlížejte otázky ${startIndex + 1} až ${endIndex} z celkových ${totalQuestions} otázek.`}
        >
          {questionsForPage.map((question, index) => (
            <div
              key={question.id}
              className="mb-8 bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden"
            >
              <StaticQuestion
                question={question}
                questionNumber={
                  (pageNumber - 1) * QUESTIONS_PER_PAGE + index + 1
                }
              />
            </div>
          ))}
        </QuestionsWrapper>

        {/* Strukturovaná data pro SEO */}
        <StructuredData questions={questionsForPage} pageNumber={pageNumber} />
      </div>
    );
  } catch (error) {
    console.error("Error fetching data:", error);
    return <NotFound message="Došlo k chybě při načítání dat" />;
  }
}

export async function generateStaticParams() {
  try {
    const applications = await getApplications({ withAuth: false });

    const params: { application: string; page: string }[] = [];

    // Pro každou aplikaci vygenerujeme stránky
    for (const app of applications) {
      try {
        const allQuestions = await getAllQuestionsForApplication(app.id);
        const totalPages = Math.ceil(allQuestions.length / QUESTIONS_PER_PAGE);

        // Vygenerujeme parametry pro každou stránku
        for (let page = 1; page <= totalPages; page++) {
          params.push({
            application: app.slug,
            page: page.toString(),
          });
        }

        console.log(
          `Generating static params for ${app.slug}: ${totalPages} pages (total ${allQuestions.length} questions)`,
        );
      } catch (appError) {
        console.error(
          `Error fetching questions for app ${app.slug}:`,
          appError,
        );
      }
    }

    return params;
  } catch (error) {
    console.error("Error generating static params:", error);
    return [];
  }
}
