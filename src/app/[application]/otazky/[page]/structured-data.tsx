import { AllQuestionsCollection_allQuestionsCollection_edges_node } from "@/graphql/types/AllQuestionsCollection";

interface StructuredDataProps {
  questions: AllQuestionsCollection_allQuestionsCollection_edges_node[];
  pageNumber: number;
}

export const StructuredData: React.FC<StructuredDataProps> = ({
  questions,
  pageNumber,
}) => {
  // Kontrola, zda otázky existují
  if (!questions || !Array.isArray(questions) || questions.length === 0) {
    return null;
  }

  // Vytvoření strukturovaných dat pro SEO - ItemList
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    itemListElement: questions
      .map((question, index) => {
        if (!question || !question.text || !question.id) return null;

        return {
          "@type": "ListItem",
          position: (pageNumber - 1) * 50 + index + 1,
          item: {
            "@type": "Question",
            name: question.text || "",
            acceptedAnswer: {
              "@type": "Answer",
              text:
                question.answers && Array.isArray(question.answers)
                  ? question.answers
                      .filter((a) => a && a.correct)
                      .map((a) => (a && a.text) || "")
                      .join(", ")
                  : "",
            },
          },
        };
      })
      .filter(Boolean),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
};
