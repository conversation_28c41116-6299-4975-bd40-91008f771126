import { Metadata } from "next";
import { getApplicationBySlug } from "@/lib/services/applications";
import { getAllQuestionsForApplication } from "@/lib/services/getAllQuestionsForApplication";

// Počet otázek na stránku
const QUESTIONS_PER_PAGE = 50;

type Params = Promise<{ application: string; page: string }>;

export async function generateMetadata({
  params,
}: {
  params: Params;
}): Promise<Metadata> {
  const { application, page } = await params;
  const pageNumber = parseInt(page) || 1;

  try {
    const applicationData = await getApplicationBySlug(application, {
      withAuth: false,
    });
    if (!applicationData) {
      return {
        title: "Aplikace nenalezena",
      };
    }

    // Načtení všech otázek
    const allQuestions = await getAllQuestionsForApplication(
      applicationData.id,
    );

    // Výpočet stránkování
    const totalQuestions = allQuestions.length;
    const totalPages = Math.ceil(totalQuestions / QUESTIONS_PER_PAGE);

    // Kontrol<PERSON>, zda je číslo stránky platné
    if (pageNumber < 1 || pageNumber > totalPages) {
      return {
        title: "Stránka nenalezena",
      };
    }

    // Získání otázek pro aktuální stránku
    const startIndex = (pageNumber - 1) * QUESTIONS_PER_PAGE;
    const endIndex = Math.min(startIndex + QUESTIONS_PER_PAGE, totalQuestions);

    return {
      title: `Otázky ${startIndex + 1}-${endIndex} | ${applicationData.name}`,
      description: `Seznam otázek pro ${applicationData.name} - stránka ${pageNumber} z ${totalPages}. Prohlížejte otázky ${startIndex + 1} až ${endIndex} z celkových ${totalQuestions} otázek.`,
      keywords: `zkoušky, online příprava, testové otázky, ${applicationData.name.toLowerCase()}, otázky, test, stránka ${pageNumber}`,
      authors: [{ name: "MossBee s.r.o." }],
      openGraph: {
        title: `Otázky - Stránka ${pageNumber} | ${applicationData.name}`,
        description: `Seznam otázek pro ${applicationData.name} - stránka ${pageNumber} z ${totalPages}. Prohlížejte otázky ${startIndex + 1} až ${endIndex} z celkových ${totalQuestions} otázek.`,
        type: "website",
        url: `${process.env.NEXTAUTH_URL || ""}/${application}/otazky/${pageNumber}`,
        images: [],
      },
      alternates: {
        canonical: `${process.env.NEXTAUTH_URL || ""}/${application}/otazky/${pageNumber}`,
      },
    };
  } catch (error) {
    console.error("Error generating metadata:", error);
    return {
      title: "Chyba při načítání dat",
    };
  }
}
