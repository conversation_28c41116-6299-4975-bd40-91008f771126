import { Exam_exam_questions_answers } from "@/graphql/types/Exam";

interface StaticAnswersProps {
  answers: Exam_exam_questions_answers[];
}

export const StaticAnswers: React.FC<StaticAnswersProps> = ({ answers }) => {
  // Kontrola, zda odpovědi existují
  if (!answers || !Array.isArray(answers) || answers.length === 0) {
    return (
      <div className="mt-4 text-gray-500">
        Nejsou k dispozici žádné odpovědi
      </div>
    );
  }
  const alphabet: { [key: number]: string } = {
    0: "A",
    1: "B",
    2: "C",
    3: "D",
    4: "E",
    5: "F",
  };

  return (
    <div className="space-y-3 mt-4">
      {answers.map((answer, index) => {
        // Kontrola, zda odpověď existuje a má všechny potřebné vlastnosti
        if (!answer || !answer.id) {
          return null;
        }
        // Styly pro odpovědi - spr<PERSON>vn<PERSON> odpovědi jsou zvýrazněny zeleně
        const styles = answer.correct
          ? {
              letter: "bg-green-600 text-white border-green-600",
              text: "text-green-700",
              container: "border-green-200 bg-green-50",
            }
          : {
              letter: "bg-gray-100 text-gray-700 border-gray-200",
              text: "text-gray-700",
              container: "border-gray-200 bg-white",
            };

        return (
          <div key={`answer_${answer.id}`} className="w-full">
            <div
              className={`
              flex items-stretch overflow-hidden
              border rounded-xl
              ${styles.container}
            `}
            >
              <div
                className={`
                flex items-center justify-center
                w-10 sm:w-14
                border-r
                ${styles.letter}
              `}
              >
                <span className="font-semibold text-lg sm:text-xl">
                  {alphabet[index]}
                </span>
              </div>

              <div className="flex-1 p-3.5 sm:p-4 text-left">
                <span
                  className={`
                  block text-[15px] sm:text-base
                  leading-relaxed
                  ${styles.text}
                `}
                >
                  {answer.text || "Odpověď bez textu"}
                </span>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};
