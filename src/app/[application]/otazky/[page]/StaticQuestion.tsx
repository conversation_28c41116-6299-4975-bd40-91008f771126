import React from "react";
import Image from "next/image";
import { AllQuestionsCollection_allQuestionsCollection_edges_node } from "@/graphql/types/AllQuestionsCollection";
import { StaticAnswers } from "./StaticAnswers";
import Styles from "./styles.module.css";

interface StaticQuestionProps {
  question: AllQuestionsCollection_allQuestionsCollection_edges_node;
  questionNumber?: number;
}

export const StaticQuestion: React.FC<StaticQuestionProps> = ({
  question,
  questionNumber,
}) => {
  // Kontrola, zda otázka existuje a má všechny potřebné vlastnosti
  if (!question || !question.text) {
    return <div className="p-4 text-red-500">Chybějící data otázky</div>;
  }
  return (
    <div className="bg-white rounded-lg shadow-sm p-3 sm:p-6">
      {/* Kontejner pro obrázek */}
      {question.image && question.image.url && (
        <div className={Styles.containerImage}>
          <Image
            width={200}
            height={200}
            className={Styles.imageExamList}
            src={question.image?.url}
            alt={question.image?.fileName || "image"}
            priority={true}
            style={{
              width: "auto",
              height: "auto",
              maxWidth: "100%",
              maxHeight: "200px",
            }}
          />
        </div>
      )}

      {/* Text otázky */}
      <div className={Styles.questionTextContainer}>
        <p className="text-sm sm:text-base">
          {questionNumber && <span>{questionNumber}. </span>}
          {question.text || "Otázka bez textu"}
        </p>
      </div>

      {/* Odpovědi */}
      {question.answers && question.answers.length > 0 ? (
        <StaticAnswers answers={question.answers} />
      ) : (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-700">
          Tato otázka nemá žádné odpovědi
        </div>
      )}
    </div>
  );
};
