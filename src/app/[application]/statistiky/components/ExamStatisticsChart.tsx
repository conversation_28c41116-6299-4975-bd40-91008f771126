"use client";

import React from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartData,
  ChartOptions,
} from "chart.js";
import { Line, Bar } from "react-chartjs-2";
import { UserExamStatistics_userExamStatistics_examStatistics } from "@/graphql/types/UserExamStatistics";

// Registrujeme potřebné komponenty Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
);

interface ExamStatisticsChartProps {
  statistics: UserExamStatistics_userExamStatistics_examStatistics[];
}

export const ExamStatisticsChart: React.FC<ExamStatisticsChartProps> = ({
  statistics,
}) => {
  return (
    <div className="space-y-8 mt-8">
      <SuccessRateChart statistics={statistics} />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <PointsChart statistics={statistics} />
        <CategoriesChart statistics={statistics} />
      </div>
    </div>
  );
};

export const SuccessRateChart: React.FC<ExamStatisticsChartProps> = ({
  statistics,
}) => {
  if (statistics.length === 0) {
    return null;
  }

  // Seřadíme statistiky podle data (od nejstaršího k nejnovějšímu pro graf)
  const sortedStats = [...statistics].sort(
    (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
  );

  // Připravíme data pro graf úspěšnosti
  const successRateData: ChartData<"line"> = {
    labels: sortedStats.map((stat) => {
      const date = new Date(stat.createdAt);
      return `${date.getDate()}.${date.getMonth() + 1}.${date.getFullYear()}`;
    }),
    datasets: [
      {
        label: "Úspěšnost (%)",
        data: sortedStats.map((stat) => stat.percentageSuccess),
        borderColor: "rgb(16, 185, 129)",
        backgroundColor: "rgba(16, 185, 129, 0.5)",
        tension: 0.3,
      },
    ],
  };

  const successRateOptions: ChartOptions<"line"> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
      },
      title: {
        display: true,
        text: "Vývoj úspěšnosti v čase",
        font: {
          size: 16,
        },
      },
    },
    scales: {
      y: {
        min: 0,
        max: 100,
        ticks: {
          callback: function (value) {
            return value + "\u2009%";
          },
        },
      },
    },
  };

  // Připravíme data pro graf úspěšnosti podle kategorií
  const categoriesMap = new Map<string, { success: number; total: number }>();

  statistics.forEach((stat) => {
    const categoryName = stat.category.title;
    if (!categoriesMap.has(categoryName)) {
      categoriesMap.set(categoryName, { success: 0, total: 0 });
    }

    const categoryData = categoriesMap.get(categoryName)!;
    categoryData.total += 1;
    if (stat.successful) {
      categoryData.success += 1;
    }
  });

  return (
    <div className="bg-white p-4 rounded-lg shadow h-full w-full">
      <div className="relative h-full w-full" style={{ minHeight: "300px" }}>
        <Line options={successRateOptions} data={successRateData} />
      </div>
    </div>
  );
};

export const PointsChart: React.FC<ExamStatisticsChartProps> = ({
  statistics,
}) => {
  if (statistics.length === 0) {
    return null;
  }

  // Seřadíme statistiky podle data (od nejstaršího k nejnovějšímu pro graf)
  const sortedStats = [...statistics].sort(
    (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
  );

  // Připravíme data pro graf bodů
  const pointsData: ChartData<"bar"> = {
    labels: sortedStats.map((stat) => {
      const date = new Date(stat.createdAt);
      return `${date.getDate()}.${date.getMonth() + 1}.${date.getFullYear()}`;
    }),
    datasets: [
      {
        label: "Získané body",
        data: sortedStats.map((stat) => stat.totalPoints),
        backgroundColor: "rgba(16, 185, 129, 0.7)",
      },
      {
        label: "Maximální body",
        data: sortedStats.map((stat) => stat.maximumPoints),
        backgroundColor: "rgba(59, 130, 246, 0.7)",
      },
    ],
  };

  const pointsOptions: ChartOptions<"bar"> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
      },
      title: {
        display: true,
        text: "Získané body v jednotlivých zkouškách",
        font: {
          size: 16,
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow h-full w-full">
      <div className="relative h-full w-full" style={{ minHeight: "300px" }}>
        <Bar options={pointsOptions} data={pointsData} />
      </div>
    </div>
  );
};

export const CategoriesChart: React.FC<ExamStatisticsChartProps> = ({
  statistics,
}) => {
  if (statistics.length === 0) {
    return null;
  }

  // Připravíme data pro graf úspěšnosti podle kategorií
  // Použijeme objekt místo Map pro lepší debugování
  const categoriesData: Record<string, { success: number; total: number }> = {};

  // Projdeme všechny statistiky a sečteme úspěšné a celkové pokusy pro každou kategorii
  statistics.forEach((stat) => {
    const categoryName = stat.category.title || `Kategorie ${stat.category.id}`;

    if (!categoriesData[categoryName]) {
      categoriesData[categoryName] = { success: 0, total: 0 };
    }

    categoriesData[categoryName].total += 1;
    if (stat.successful) {
      categoriesData[categoryName].success += 1;
    }
  });

  // Získáme názvy kategorií a vypočítáme procentuální úspěšnost
  const categoryLabels = Object.keys(categoriesData);

  // Zkusíme alternativní přístup - použijeme přímo percentageSuccess z dat
  const categoryAverages: Record<string, { sum: number; count: number }> = {};

  statistics.forEach((stat) => {
    const categoryName = stat.category.title || `Kategorie ${stat.category.id}`;

    if (!categoryAverages[categoryName]) {
      categoryAverages[categoryName] = { sum: 0, count: 0 };
    }

    categoryAverages[categoryName].sum += stat.percentageSuccess;
    categoryAverages[categoryName].count += 1;
  });

  const alternativeRates = categoryLabels.map((category) => {
    const avg =
      categoryAverages[category].sum / categoryAverages[category].count;
    return avg;
  });

  // Použijeme alternativní výpočet pro data grafu
  const categoryData: ChartData<"bar"> = {
    labels: categoryLabels,
    datasets: [
      {
        label: "Úspěšnost (%)",
        data: alternativeRates,
        backgroundColor: "rgba(59, 130, 246, 0.7)",
        borderColor: "rgba(59, 130, 246, 1)",
        borderWidth: 1,
        borderRadius: 4,
      },
    ],
  };

  const categoryOptions: ChartOptions<"bar"> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            const label = context.dataset.label || "";
            const value = context.parsed.x;
            const categoryName = categoryLabels[context.dataIndex];
            const attempts = categoryAverages[categoryName].count;
            return `${label}: ${value.toFixed(1)}\u2009% (průměr z ${attempts} pokusů)`;
          },
        },
      },
      title: {
        display: true,
        text: "Úspěšnost podle kategorií",
        font: {
          size: 16,
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        ticks: {
          callback: function (value) {
            return value + "\u2009%";
          },
        },
      },
    },
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow h-full w-full">
      <div className="relative h-full w-full" style={{ minHeight: "300px" }}>
        <Bar options={categoryOptions} data={categoryData} />
      </div>
    </div>
  );
};
