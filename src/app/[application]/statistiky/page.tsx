"use server";

import React from "react";
import Menu from "@/components/Menu/Menu";
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth.config";
import { ExtendedDefaultSession } from "@/types/extendedDefaultSession";
import { redirect } from "next/navigation";
import { getApplicationBySlug } from "@/lib/services/applications";
import { getUserExamStatistics } from "@/lib/services/userExamStatistics";
import { ExamStatisticsTable } from "./components/ExamStatisticsTable";
import dynamic from "next/dynamic";
import NotFound from "@/components/NotFound/NotFound";
import { ChartBar } from "lucide-react";

// Dynamicky importujeme klientské komponenty grafů, aby se vykreslovaly pouze na klientovi
const SuccessRateChart = dynamic(() =>
  import("./components/ExamStatisticsChart").then(
    (mod) => mod.SuccessRateChart,
  ),
);

const PointsChart = dynamic(() =>
  import("./components/ExamStatisticsChart").then((mod) => mod.PointsChart),
);

const CategoriesChart = dynamic(() =>
  import("./components/ExamStatisticsChart").then((mod) => mod.CategoriesChart),
);

type Params = Promise<{ application: string }>;

export default async function ExamStatisticsPage({
  params,
}: {
  params: Params;
}) {
  const session = (await getServerSession(
    authConfig,
  )) as ExtendedDefaultSession;

  if (!session) {
    redirect("/prihlaseni");
  }

  const { application } = await params;

  try {
    const applicationData = await getApplicationBySlug(application);

    if (!applicationData) {
      return <NotFound message="Aplikace nenalezena" />;
    }

    // Fetch exam statistics data server-side
    const statisticsData = await getUserExamStatistics(
      applicationData.id.toString(),
    );

    return (
      <div className="relative min-h-screen w-full bg-gradient-to-br from-gray-50 via-white to-gray-50">
        {/* Background grid pattern */}
        <div className="absolute inset-0 w-full h-full bg-grid opacity-10 pointer-events-none"></div>

        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -left-40 w-80 h-80 bg-emerald-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
          <div className="absolute -bottom-40 left-20 w-80 h-80 bg-emerald-50 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
        </div>

        <Menu application={applicationData} />
        <div className="app-layout relative">
          <div className="container mx-auto pt-5 lg:pt-10 pb-20 max-w-5xl px-4 sm:px-6 lg:px-8">
            <div className="relative bg-white/80 backdrop-blur-sm shadow-md rounded-lg p-6 border border-emerald-100 hover:shadow-lg transition-shadow duration-300">
              <div className="absolute -inset-0.5 rounded-lg opacity-10 blur-lg -z-10"></div>

              <div className="mb-6 px-1">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                  <div className="flex items-center">
                    <ChartBar className="h-6 w-6 text-emerald-600 mr-3" />
                    <h2 className="text-xl md:text-2xl font-bold text-gray-800">
                      Statistiky zkoušek
                    </h2>
                  </div>

                  <div className="text-gray-700 font-medium">
                    {applicationData.name}
                  </div>
                </div>
                <div className="h-0.5 w-16 bg-emerald-500 mt-2"></div>
              </div>
              {statisticsData ? (
                <>
                  {/* Nejprve zobrazíme první graf - vývoj úspěšnosti v čase */}
                  {statisticsData.userExamStatistics.examStatistics.length >
                    0 && (
                    <div className="mb-8">
                      <h3 className="text-lg font-semibold text-gray-700 mb-4">
                        Vývoj úspěšnosti v čase
                      </h3>
                      <div className="h-[300px] md:h-[350px]">
                        <SuccessRateChart
                          statistics={
                            statisticsData.userExamStatistics.examStatistics
                          }
                        />
                      </div>
                    </div>
                  )}

                  {/* Poté zobrazíme tabulku s daty */}
                  <div className="mb-8">
                    <h3 className="text-lg font-semibold text-gray-700 mb-4">
                      Přehled všech zkoušek
                    </h3>
                    <ExamStatisticsTable
                      statistics={
                        statisticsData.userExamStatistics.examStatistics
                      }
                    />
                  </div>

                  {/* Nakonec zobrazíme zbývající dva grafy */}
                  {statisticsData.userExamStatistics.examStatistics.length >
                    0 && (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
                      <div className="bg-white/80 backdrop-blur-sm shadow-md rounded-lg p-6 border border-blue-100 hover:shadow-lg transition-shadow duration-300">
                        <h3 className="text-lg font-semibold text-gray-700 mb-4">
                          Úspěšnost podle kategorií
                        </h3>
                        <div className="h-[300px] md:h-[350px]">
                          <CategoriesChart
                            statistics={
                              statisticsData.userExamStatistics.examStatistics
                            }
                          />
                        </div>
                      </div>
                      <div className="bg-white/80 backdrop-blur-sm shadow-md rounded-lg p-6 border border-emerald-100 hover:shadow-lg transition-shadow duration-300">
                        <h3 className="text-lg font-semibold text-gray-700 mb-4">
                          Body v jednotlivých zkouškách
                        </h3>
                        <div className="h-[300px] md:h-[350px]">
                          <PointsChart
                            statistics={
                              statisticsData.userExamStatistics.examStatistics
                            }
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className="bg-white/80 backdrop-blur-sm border border-red-200 rounded-lg p-6 my-4 text-center shadow-sm">
                  <div className="flex flex-col items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-12 w-12 text-red-400 mb-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <p className="text-red-600 font-medium">
                      Nepodařilo se načíst statistiky zkoušek
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error in ExamStatisticsPage:", error);
    return <NotFound message="Došlo k chybě při načítání dat" />;
  }
}
