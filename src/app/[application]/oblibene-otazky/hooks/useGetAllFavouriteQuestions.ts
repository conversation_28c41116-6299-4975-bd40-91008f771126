import { useQuery } from "@apollo/client";
import { GET_ALL_FAVOURITE_QUESTIONS } from "@/app/[application]/oblibene-otazky/query";
import { ExtendedDefaultSession } from "@/types/extendedDefaultSession";
import { useSession } from "next-auth/react";
import { useState, useCallback, useEffect, useRef } from "react";
import type {
  FavouriteQuestionsCollection,
  FavouriteQuestionsCollectionVariables,
  FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node,
  FavouriteQuestionsCollection_favouriteQuestionsCollection_pageInfo,
} from "@/graphql/types/FavouriteQuestionsCollection";
import { PAGE_LIMIT } from "@/app/[application]/oblibene-otazky/constants";

export const useGetAllFavouriteQuestions = (
  applicationId: number,
  initialData?: FavouriteQuestionsCollection,
) => {
  const { data: sessionData, status } = useSession();
  const session = sessionData as ExtendedDefaultSession;
  const [questions, setQuestions] = useState<
    FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node[]
  >(() =>
    initialData
      ? initialData.favouriteQuestionsCollection.edges.map((edge) => edge.node)
      : [],
  );
  const [pageInfo, setPageInfo] =
    useState<FavouriteQuestionsCollection_favouriteQuestionsCollection_pageInfo>(
      () =>
        initialData?.favouriteQuestionsCollection.pageInfo || {
          __typename: "PageInfo",
          hasNextPage: false,
          endCursor: null,
          startCursor: null,
          hasPreviousPage: false,
        },
    );
  const isLoadingMore = useRef(false);

  const { loading, error, fetchMore, refetch, data } = useQuery<
    FavouriteQuestionsCollection,
    FavouriteQuestionsCollectionVariables
  >(GET_ALL_FAVOURITE_QUESTIONS, {
    variables: {
      applicationId: applicationId.toString(),
      limit: PAGE_LIMIT,
      offset: 0,
    },
    context: {
      headers: {
        Authorization: `Bearer ${session?.user?.token}`,
      },
    },
    skip: status !== "authenticated",
  });

  useEffect(() => {
    if (data) {
      if (
        data.favouriteQuestionsCollection.pageInfo.endCursor !==
        pageInfo.endCursor
      )
        setQuestions(
          data.favouriteQuestionsCollection.edges.map((edge) => edge.node),
        );
      setPageInfo(data.favouriteQuestionsCollection.pageInfo);
    }
  }, [data, pageInfo.endCursor]);

  useEffect(() => {
    if (status === "authenticated") {
      refetch();
    }
  }, [status, refetch]);

  const loadMoreQuestions = useCallback(async () => {
    if (!pageInfo.hasNextPage || isLoadingMore.current) return;

    isLoadingMore.current = true;

    try {
      await fetchMore({
        variables: {
          offset: questions.length,
        },
        updateQuery: (prevResult, { fetchMoreResult }) => {
          if (!fetchMoreResult) {
            return prevResult;
          }

          return {
            ...prevResult,
            favouriteQuestionsCollection: {
              ...prevResult.favouriteQuestionsCollection,
              edges: [
                ...prevResult.favouriteQuestionsCollection.edges,
                ...fetchMoreResult.favouriteQuestionsCollection.edges,
              ],
              pageInfo: fetchMoreResult.favouriteQuestionsCollection.pageInfo,
            },
          };
        },
      });
    } finally {
      isLoadingMore.current = false;
    }
  }, [pageInfo.hasNextPage, questions.length, fetchMore]);

  return {
    loading,
    error,
    questions,
    setQuestions,
    loadMoreQuestions,
  };
};
