import { useEffect, useRef, useCallback } from "react";

export const useInfiniteScroll = (loadMore: () => Promise<void>) => {
  const isLoadingMore = useRef(false);

  useEffect(() => {
    const handleScroll = () => {
      if (
        window.innerHeight + document.documentElement.scrollTop >=
        document.documentElement.offsetHeight - 1
      ) {
        if (!isLoadingMore.current) {
          isLoadingMore.current = true;
          loadMore().finally(() => {
            isLoadingMore.current = false;
          });
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [loadMore]);

  const checkIfLoadMoreNeeded = useCallback(() => {
    if (document.documentElement.scrollHeight <= window.innerHeight) {
      if (!isLoadingMore.current) {
        isLoadingMore.current = true;
        loadMore().finally(() => {
          isLoadingMore.current = false;
        });
      }
    }
  }, [loadMore]);

  return { checkIfLoadMoreNeeded };
};
