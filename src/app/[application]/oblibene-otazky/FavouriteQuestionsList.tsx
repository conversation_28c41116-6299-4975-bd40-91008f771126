"use client";

import React, { useEffect, useState } from "react";
import { useMutation } from "@apollo/client";
import { useSession } from "next-auth/react";
import { ExtendedDefaultSession } from "@/types/extendedDefaultSession";
import { FavouriteQuestionsCollection } from "@/graphql/types/FavouriteQuestionsCollection";
import { REMOVE_FAVOURITE_QUESTION } from "@/app/[application]/kategorie/zkouska/[category]/Questions/Question/mutation";
import {
  RemoveFavouriteQuestion,
  RemoveFavouriteQuestionVariables,
} from "@/graphql/types/RemoveFavouriteQuestion";
import { useGetAllFavouriteQuestions } from "@/app/[application]/oblibene-otazky/hooks/useGetAllFavouriteQuestions";
import { useInfiniteScroll } from "@/app/[application]/oblibene-otazky/hooks/useInfiniteScroll";
import { EmptyState } from "@/app/[application]/oblibene-otazky/components/EmptyState";
import { FavouriteQuestion } from "@/app/[application]/oblibene-otazky/components/FavouriteQuestion";

interface FavouriteQuestionsListProps {
  applicationId: number;
  initialData: FavouriteQuestionsCollection;
  applicationSlug?: string;
}

const FavouriteQuestionsList: React.FC<FavouriteQuestionsListProps> = ({
  applicationId,
  initialData,
  applicationSlug,
}) => {
  const { data: sessionData } = useSession();
  const session = sessionData as ExtendedDefaultSession;

  const { error, questions, loadMoreQuestions, setQuestions } =
    useGetAllFavouriteQuestions(applicationId, initialData);

  const [hiddenQuestions, setHiddenQuestions] = useState<number[]>([]);
  const [removeFavouriteQuestion] = useMutation<
    RemoveFavouriteQuestion,
    RemoveFavouriteQuestionVariables
  >(REMOVE_FAVOURITE_QUESTION);

  const { checkIfLoadMoreNeeded } = useInfiniteScroll(loadMoreQuestions);

  useEffect(() => {
    if (typeof window !== "undefined") {
      // Vypnout automatickou obnovu pozice scrollu
      if ("scrollRestoration" in history) {
        history.scrollRestoration = "manual";
      }
      window.scrollTo(0, 0);
    }
  }, []);

  useEffect(() => {
    if (questions.length === 0) return;

    const timer = setTimeout(() => {
      checkIfLoadMoreNeeded();
    }, 500);

    return () => clearTimeout(timer);
  }, [questions, checkIfLoadMoreNeeded]);

  const handleRemoveFavourite = async (
    selectedApplicationId: number,
    questionId: number,
  ) => {
    try {
      await removeFavouriteQuestion({
        variables: {
          applicationId: selectedApplicationId.toString(),
          questionId: questionId.toString(),
        },
        context: {
          headers: {
            Authorization: `Bearer ${session?.user?.token}`,
          },
        },
      });
      setHiddenQuestions((prev) => [...prev, questionId]);
      setTimeout(() => {
        setQuestions((prev) =>
          prev.filter((question) => question.id !== questionId),
        );
      }, 500);
    } catch (error) {
      console.error("Failed to remove favourite question:", error);
    }
  };

  if (error) return <p>Error: {error.message}</p>;

  return (
    <div>
      {questions.length === 0 ? (
        <EmptyState applicationSlug={applicationSlug} />
      ) : (
        questions.map((question) => (
          <div key={question.id}>
            <FavouriteQuestion
              question={question}
              isHidden={hiddenQuestions.includes(question.id)}
              applicationId={applicationId}
              onRemove={handleRemoveFavourite}
            />
          </div>
        ))
      )}
    </div>
  );
};

export default FavouriteQuestionsList;
