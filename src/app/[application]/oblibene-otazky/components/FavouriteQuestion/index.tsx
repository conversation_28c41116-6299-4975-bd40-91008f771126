import React from "react";
import { QuestionMedia } from "./QuestionMedia";
import { QuestionAnswer } from "../QuestionAnswer";
import type { FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node } from "@/graphql/types/FavouriteQuestionsCollection";
import { Heart } from "lucide-react";

type FavouriteQuestionProps = {
  question: FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node;
  isHidden: boolean;
  applicationId: number;
  onRemove: (applicationId: number, questionId: number) => void;
};

export const FavouriteQuestion: React.FC<FavouriteQuestionProps> = ({
  question,
  isHidden,
  applicationId,
  onRemove,
}) => {
  if (isHidden) {
    return null;
  }

  return (
    <div
      className={`transition-opacity duration-500 ${isHidden ? "opacity-0 pb-0" : "opacity-100 pb-4"}`}
    >
      {/* <PERSON>lavn<PERSON> obsah <PERSON> */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        <div className="p-3 sm:p-6">
          {/* <PERSON><PERSON><PERSON> */}
          <QuestionMedia
            image={question?.image || undefined}
            video={question?.video || undefined}
          />

          {/* Text otázky */}
          <div className="mt-3 sm:mt-4 mb-4 sm:mb-6">
            <p className="text-sm sm:text-base text-gray-900 font-medium">
              {question.text}
            </p>
          </div>

          {/* Seznam odpovědí */}
          <div className="space-y-2 sm:space-y-3">
            {question.answers.map((answer, index) => (
              <QuestionAnswer key={answer.id} answer={answer} index={index} />
            ))}
          </div>
        </div>

        {/* Tlačítko pro odebrání - uvnitř karty */}
        <div className="px-3 sm:px-6 pb-3 sm:pb-6 pt-0">
          <div className="pt-3 sm:pt-4 border-t border-gray-100">
            <button
              onClick={() => onRemove(applicationId, question.id)}
              className="inline-flex items-center justify-center gap-2 px-4 py-2.5
                text-gray-400 bg-white hover:bg-gray-50
                rounded-xl border-2 border-gray-100
                transition-all duration-200
                shadow-sm"
            >
              <Heart className="w-5 h-5" fill="currentColor" strokeWidth={2} />
              <span className="text-sm font-medium">Odebrat z oblíbených</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
