import React from "react";
import Image from "next/image";
import { VideoPlayer } from "@/app/[application]/kategorie/zkouska/[category]/Questions/Question/VideoPlayer/VideoPlayer";
import type {
  FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node_image,
  FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node_video,
} from "@/graphql/types/FavouriteQuestionsCollection";

type QuestionMediaProps = {
  image?: FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node_image;
  video?: FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node_video;
};

export const QuestionMedia: React.FC<QuestionMediaProps> = ({
  image,
  video,
}) => (
  <>
    {image && (
      <div className="flex justify-center items-center">
        <div className="relative w-full max-w-[420px] rounded-lg overflow-hidden">
          <Image
            src={image.url}
            alt={image.fileName || "<PERSON>b<PERSON><PERSON><PERSON><PERSON> ot<PERSON><PERSON>ky"}
            width={420}
            height={300}
            priority={true}
            className="w-full h-auto object-contain"
            style={{
              maxHeight: "300px",
            }}
          />
        </div>
      </div>
    )}

    {video && (
      <div className="flex justify-center items-center">
        <div className="relative w-full max-w-[420px] rounded-lg overflow-hidden">
          <VideoPlayer video={video} autoplay={false} />
        </div>
      </div>
    )}
  </>
);
