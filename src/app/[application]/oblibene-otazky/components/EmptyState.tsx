import React from "react";
import { Heart, HelpCircle } from "lucide-react";
import Link from "next/link";

export const EmptyState: React.FC<{ applicationSlug?: string }> = ({
  applicationSlug,
}) => (
  <div className="p-8 md:p-12 bg-white rounded-lg shadow-md text-center min-h-[60vh] flex flex-col items-center justify-center">
    <div className="mb-6 bg-gray-100 p-6 rounded-full">
      <Heart className="w-16 h-16 text-gray-400" />
    </div>

    <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-3">
      Zatím nemáte žádné oblíbené ot<PERSON>ky
    </h2>
    <p className="text-gray-600 mb-8 max-w-md">
      P<PERSON>i proch<PERSON><PERSON> otázek si můžete přidávat oblíbené otázky kliknutím na
      ikonu srdíčka. Takto označen<PERSON> ot<PERSON>zky se zobrazí v této sekci pro pozdějš<PERSON>
      procvi<PERSON>.
    </p>

    {applicationSlug && (
      <Link
        href={`/${applicationSlug}/vsechny-otazky`}
        className="flex items-center justify-center gap-2 px-6 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors shadow-md hover:shadow-lg"
      >
        <HelpCircle className="w-5 h-5" />
        <span>Procházet všechny otázky</span>
      </Link>
    )}

    <div className="mt-12 pt-8 border-t border-gray-200 w-full max-w-md">
      <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
        <p>
          Oblíbené otázky vám pomáhají soustředit se na problémové oblasti a
          zlepšit vaši přípravu.
        </p>
      </div>
    </div>
  </div>
);
