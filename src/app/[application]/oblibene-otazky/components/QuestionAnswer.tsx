import React from "react";
import Image from "next/image";
import type { FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node_answers } from "@/graphql/types/FavouriteQuestionsCollection";

type AnswerProps = {
  answer: FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node_answers;
  index: number;
};

export const QuestionAnswer: React.FC<AnswerProps> = ({ answer, index }) => {
  const alphabet: { [key: number]: string } = {
    0: "A",
    1: "B",
    2: "C",
    3: "D",
    4: "E",
    5: "F",
  };

  const isCorrect = answer.correct;

  return (
    <button className="w-full group focus:outline-none">
      <div
        className={`
          flex items-stretch overflow-hidden
          border rounded-xl
          transition-all duration-200
          ${
            isCorrect
              ? "border-green-200 bg-green-50"
              : "border-gray-200 bg-white"
          }
          group-hover:border-gray-300
          group-active:scale-[0.99]
          group-focus:ring-2 group-focus:ring-blue-500 group-focus:ring-offset-2
        `}
      >
        <div
          className={`
            flex items-center justify-center
            w-10 sm:w-14
            border-r
            transition-colors duration-200
            ${
              isCorrect
                ? "bg-green-600 text-white border-green-600"
                : "bg-gray-100 text-gray-700 border-gray-200 group-hover:bg-gray-200"
            }
          `}
        >
          <span className="font-semibold text-base sm:text-xl">
            {alphabet[index]}
          </span>
        </div>

        <div className="flex-1 py-3 px-2 sm:p-4 text-left">
          <span
            className={`
            block text-[15px] sm:text-base
            leading-relaxed
            ${isCorrect ? "text-green-700" : "text-gray-700"}
          `}
          >
            {answer.text}
          </span>

          {answer.image && (
            <div className="mt-3">
              <Image
                src={answer.image.url}
                alt={answer.image.fileName || "Obrázek odpovědi"}
                priority={true}
                width={200}
                height={200}
                className="rounded-lg max-w-full h-auto"
              />
            </div>
          )}
        </div>
      </div>
    </button>
  );
};
