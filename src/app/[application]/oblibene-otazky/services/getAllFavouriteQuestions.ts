import { getClient } from "@/lib/client";
import { GET_ALL_FAVOURITE_QUESTIONS } from "@/app/[application]/oblibene-otazky/query";
import type {
  FavouriteQuestionsCollection,
  FavouriteQuestionsCollectionVariables,
} from "@/graphql/types/FavouriteQuestionsCollection";
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth.config";
import { ExtendedDefaultSession } from "@/types/extendedDefaultSession";

export const getAllFavouriteQuestions = async (
  applicationId: number,
  limit: number,
  offset: number,
): Promise<FavouriteQuestionsCollection> => {
  const session = (await getServerSession(
    authConfig,
  )) as ExtendedDefaultSession;
  const client = getClient();

  const { data } = await client.query<
    FavouriteQuestionsCollection,
    FavouriteQuestionsCollectionVariables
  >({
    query: GET_ALL_FAVOURITE_QUESTIONS,
    variables: {
      applicationId: applicationId.toString(),
      limit,
      offset,
    },
    context: {
      headers: {
        Authorization: `Bearer ${session?.user?.token}`,
      },
    },
  });

  return data;
};
