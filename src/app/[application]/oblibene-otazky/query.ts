import { gql } from "@apollo/client";

export const GET_ALL_FAVOURITE_QUESTIONS = gql`
  query FavouriteQuestionsCollection(
    $applicationId: ID!
    $limit: Int!
    $offset: Int!
  ) {
    favouriteQuestionsCollection(
      applicationId: $applicationId
      limit: $limit
      offset: $offset
    ) {
      totalCount
      edges {
        cursor
        node {
          id
          image {
            url
            fileName
          }
          video {
            url
            fileName
            thumbnailImage {
              url
              fileName
            }
          }
          text
          answers {
            id
            text
            image {
              url
              fileName
            }
            correct
          }
        }
      }
      pageInfo {
        hasNextPage
        endCursor
        startCursor
        hasPreviousPage
      }
    }
  }
`;
