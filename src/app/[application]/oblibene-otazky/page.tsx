"use server";

import React from "react";
import Menu from "@/components/Menu/Menu";
import FavouriteQuestionsList from "@/app/[application]/oblibene-otazky/FavouriteQuestionsList";
import { PAGE_LIMIT } from "@/app/[application]/oblibene-otazky/constants";
import { getApplicationBySlug } from "@/lib/services/applications";
import { getAllFavouriteQuestions } from "./services/getAllFavouriteQuestions";
import NotFound from "@/components/NotFound/NotFound";
import { QuestionsWrapper } from "@/components/QuestionsWrapper/QuestionsWrapper";
import { getQuestionInflection } from "@/utils/inflection";

type Params = Promise<{ application: string }>;

export default async function FavouriteQuestionsPage({
  params,
}: {
  params: Params;
}) {
  const { application } = await params;

  try {
    const applicationData = await getApplicationBySlug(application);

    if (!applicationData) {
      return <NotFound message="Aplikace nenalezena" />;
    }

    const initialData = await getAllFavouriteQuestions(
      applicationData.id,
      PAGE_LIMIT,
      0,
    );

    return (
      <div className="min-h-screen bg-gray-100">
        <div className="fixed top-0 left-0 right-0 z-50 bg-white shadow-sm h-[60px] sm:h-auto">
          <Menu application={applicationData} />
        </div>

        <QuestionsWrapper
          title={
            <div className="flex items-center gap-3">
              <span>Oblíbené otázky</span>
              <span className="text-base font-normal text-gray-500">
                ({initialData.favouriteQuestionsCollection.totalCount}{" "}
                {getQuestionInflection(
                  initialData.favouriteQuestionsCollection.totalCount,
                )}
                )
              </span>
            </div>
          }
          description="Seznam vašich uložených otázek pro pozdější procvičování"
        >
          <FavouriteQuestionsList
            applicationId={applicationData.id}
            initialData={initialData}
          />
        </QuestionsWrapper>
      </div>
    );
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    return <NotFound message="Došlo k chybě při načítání dat" />;
  }
}
