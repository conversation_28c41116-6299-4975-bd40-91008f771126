import { ImageResponse } from "next/og";

export const runtime = "edge";
export const alt = "Aktuální testy aplikace";
export const contentType = "image/png";
export const size = {
  width: 1200,
  height: 630,
};

const applicationNames: Record<string, string> = {
  autoskola: "Autoškola",
  "zbrojni-prukaz": "Zbrojní průkaz",
  "potapecsky-prukaz": "Potápěčský průkaz",
  straznik: "Strážník",
  "kapitanske-zkousky": "Kapitánské zkoušky",
};

type Params = Promise<{ application: string }>;

export default async function OGImage({ params }: { params: Params }) {
  try {
    const { application } = await params;

    const applicationName = applicationNames[application] || "Aktuální testy";

    // Použijeme statický obrázek pro Open Graph
    const baseUrl =
      process.env.NEXT_PUBLIC_BASE_URL || "https://aktualnitesty.cz";
    const imagePath = "/screenshots/mobile.png";
    const ogImageUrl = new URL(imagePath, baseUrl);

    return new ImageResponse(
      (
        <div
          style={{
            display: "flex",
            fontSize: 48,
            background: "white",
            width: "100%",
            height: "100%",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            position: "relative",
            overflow: "hidden",
          }}
        >
          {/* Použití obrázku jako pozadí */}
          <img
            src={ogImageUrl.toString()}
            alt="Aktuální testy aplikace"
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              objectFit: "cover",
            }}
          />

          {/* Případný overlay nebo text */}
          <div
            style={{
              position: "absolute",
              bottom: 32,
              left: 32,
              right: 32,
              padding: "12px 24px",
              backgroundColor: "rgba(255, 255, 255, 0.8)",
              borderRadius: 8,
              color: "#000",
              fontSize: 32,
              fontWeight: "bold",
              textAlign: "center",
            }}
          >
            {applicationName} - Online příprava na zkoušky
          </div>
        </div>
      ),
      {
        ...size,
      },
    );
  } catch (error) {
    console.error("Error generating OpenGraph image:", error);

    // Záložní fallback obrázek v případě chyby
    return new ImageResponse(
      (
        <div
          style={{
            display: "flex",
            fontSize: 48,
            background: "#10B981",
            color: "white",
            width: "100%",
            height: "100%",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            padding: 32,
            textAlign: "center",
          }}
        >
          <div style={{ fontSize: 64, fontWeight: "bold", marginBottom: 24 }}>
            Aktuální testy
          </div>
          <div>Online příprava na zkoušky</div>
        </div>
      ),
      {
        ...size,
      },
    );
  }
}
