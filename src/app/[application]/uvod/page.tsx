import React from "react";
import Menu from "@/components/Menu/Menu";
import {
  getApplicationBySlug,
  getApplications,
} from "@/lib/services/applications";
import NotFound from "@/components/NotFound/NotFound";
import { BookOpen, Target, Award, Clock, Wallet } from "lucide-react";
import Elevated from "@/components/Elevated/Elevated";
import dynamic from "next/dynamic";
import {
  getStudentLabel,
  StudentCount,
} from "@/components/StudentCount/StudentCount";

const HomeTextBox = dynamic(() => import("@/components/HomeTextBox"));
import { ThankYouBanner } from "@/components/ThankYouBanner/ThankYouBanner";
import { LockStamp } from "@/components/Stamp";
import { GuardExamDetails } from "@/components/ApplicationSpecific/GuardExamDetails";
import { WeaponsLicenseDetails } from "@/components/ApplicationSpecific/WeaponsLicenseDetails";
import { CaptainExamDetails } from "@/components/ApplicationSpecific/CaptainExamDetails";
import { DivingLicenseDetails } from "@/components/ApplicationSpecific/DivingLicenseDetails";
import { DrivingSchoolDetails } from "@/components/ApplicationSpecific/DrivingSchoolDetails";

export { generateMetadata } from "./metadata";

type Params = Promise<{ application: string }>;

export default async function CategoryPage({ params }: { params: Params }) {
  const { application } = await params;

  const applicationData = await getApplicationBySlug(application);

  if (!applicationData) {
    return <NotFound message="Aplikace nenalezena" />;
  }

  return (
    <div className="relative min-h-screen overflow-x-hidden bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Background grid pattern - skrytý na mobilech */}
      <div className="absolute inset-0 hidden md:block">
        <div className="absolute inset-0 w-full h-full bg-grid opacity-10 pointer-events-none"></div>
      </div>

      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -left-40 w-80 h-80 bg-emerald-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
      </div>

      {/* Menu */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200">
        <Menu application={applicationData} />
      </div>

      <main className="relative container mx-auto px-4 pt-28 lg:pt-32 pb-20">
        <div className="max-w-6xl mx-auto">
          {/* Hero Section */}
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center mb-16">
            {/* Text content */}
            <div className="w-full">
              <div className="space-y-6">
                <div>
                  <div className="mb-8">
                    <h1 className="text-4xl font-bold text-gray-900 mb-3">
                      {applicationData.name}
                    </h1>
                    {!applicationData.isPaid && (
                      <div className="flex items-center gap-3 text-emerald-700">
                        <Wallet className="h-5 w-5" />
                        <span className="font-medium">
                          {applicationData.price.toLocaleString("cs-CZ")} Kč
                        </span>
                        <span className="text-sm text-emerald-600">
                          (jednorázová platba)
                        </span>
                      </div>
                    )}
                  </div>
                  <p className="text-xl text-gray-700 mb-8">
                    Připravte se na zkoušky s naším interaktivním výukovým
                    programem. Aktuální testové otázky a praktická cvičení na
                    jednom místě.
                  </p>
                </div>
                <div className="flex flex-col sm:flex-row gap-4">
                  <HomeTextBox application={applicationData} />
                </div>
              </div>
            </div>

            {/* Interactive Feature Display */}
            <div className="relative w-full">
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-50 to-blue-50 rounded-3xl"></div>
              <div className="relative bg-white rounded-3xl p-6 sm:p-8 border border-gray-100 shadow-xl">
                <div className="grid grid-cols-2 gap-4 sm:gap-6">
                  {/* Stats boxes */}
                  <div className="flex flex-col items-center p-4 sm:p-6 bg-emerald-50 rounded-2xl border border-emerald-100 hover:bg-emerald-100/50 transition-colors duration-300 relative">
                    {!applicationData.isPaid && (
                      <div className="absolute -top-5 -right-3 sm:-right-4 z-10">
                        <LockStamp />
                      </div>
                    )}
                    <BookOpen className="w-8 h-8 sm:w-10 sm:h-10 text-emerald-600 mb-4" />
                    {applicationData.isPaid ? (
                      <>
                        <span className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
                          {applicationData.questionsTotalCount}
                        </span>
                        <span className="text-sm sm:text-base text-emerald-700 text-center">
                          Testových otázek
                        </span>
                      </>
                    ) : (
                      <>
                        <span className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
                          {Math.floor(applicationData.questionsTotalCount / 2)}/
                          {applicationData.questionsTotalCount}
                        </span>
                        <span className="text-sm sm:text-base text-emerald-700 text-center">
                          Otázek zdarma
                        </span>
                      </>
                    )}
                  </div>
                  <div className="flex flex-col items-center p-4 sm:p-6 bg-blue-50 rounded-2xl border border-blue-100 hover:bg-blue-100/50 transition-colors duration-300">
                    <Target className="w-8 h-8 sm:w-10 sm:h-10 text-blue-600 mb-4" />
                    <span className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
                      {applicationData.averageSuccessRate === 0
                        ? 100
                        : applicationData.averageSuccessRate}
                      &thinsp;%
                    </span>
                    <span className="text-sm sm:text-base text-blue-700 text-center">
                      Průměrná úspěšnost
                    </span>
                  </div>
                  <div className="flex flex-col items-center p-4 sm:p-6 bg-blue-50 rounded-2xl border border-blue-100 hover:bg-blue-100/50 transition-colors duration-300">
                    <Award className="w-8 h-8 sm:w-10 sm:h-10 text-blue-600 mb-4" />
                    <span className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
                      <StudentCount count={applicationData.studentsCount} />
                    </span>
                    <span className="text-sm sm:text-base text-blue-700 text-center">
                      {getStudentLabel(applicationData.studentsCount)}
                    </span>
                  </div>
                  <div className="flex flex-col items-center p-4 sm:p-6 bg-emerald-50 rounded-2xl border border-emerald-100 hover:bg-emerald-100/50 transition-colors duration-300">
                    <Clock className="w-8 h-8 sm:w-10 sm:h-10 text-emerald-600 mb-4" />
                    <span className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
                      24/7
                    </span>
                    <span className="text-sm sm:text-base text-emerald-700 text-center">
                      Dostupnost
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* ThankYouBanner přesunut mezi hlavní sekce */}
          <div className="mb-16">
            <ThankYouBanner application={applicationData} />
          </div>

          {/* Features Section */}
          <div className="grid md:grid-cols-2 gap-6 sm:gap-8">
            <div className="bg-gradient-to-br from-emerald-50 to-emerald-100/50 rounded-2xl p-8 border border-emerald-200 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2 bg-emerald-100 rounded-lg">
                  <Target className="w-6 h-6 text-emerald-600" />
                </div>
                <h3 className="text-2xl font-semibold text-gray-900">
                  Pro koho je kurz určen
                </h3>
              </div>
              <ul className="space-y-4">
                <li className="flex items-center gap-3 text-gray-700">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                  Začátečníci i pokročilí
                </li>
                <li className="flex items-center gap-3 text-gray-700">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                  Samostudium
                </li>
                <li className="flex items-center gap-3 text-gray-700">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                  Příprava na zkoušku
                </li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-2xl p-8 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <BookOpen className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="text-2xl font-semibold text-gray-900">
                  Klíčové vlastnosti
                </h3>
              </div>
              <ul className="space-y-4">
                <li className="flex items-center gap-3 text-gray-700">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  Aktuální otázky pro rok {new Date().getFullYear()}
                </li>
                <li className="flex items-center gap-3 text-gray-700">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  Získejte jistotu před zkouškou
                </li>
                <li className="flex items-center gap-3 text-gray-700">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  Procvičujte, dokud si nebudete jistí
                </li>
              </ul>
            </div>
          </div>

          {/* Application-specific content */}
          {applicationData.slug === "straznik" && (
            <div className="mb-16">
              <GuardExamDetails />
            </div>
          )}
          
          {applicationData.slug === "zbrojni-prukaz" && (
            <div className="mb-16">
              <WeaponsLicenseDetails />
            </div>
          )}
          
          {applicationData.slug === "kapitanske-zkousky" && (
            <div className="mb-16">
              <CaptainExamDetails />
            </div>
          )}
          
          {applicationData.slug === "potapecsky-prukaz" && (
            <div className="mb-16">
              <DivingLicenseDetails />
            </div>
          )}
          
          {applicationData.slug === "autoskola" && (
            <div className="mb-16">
              <DrivingSchoolDetails />
            </div>
          )}

          <Elevated application={applicationData} />
        </div>
      </main>
    </div>
  );
}

export async function generateStaticParams() {
  const applications = await getApplications({ withAuth: false });
  return applications.map((app) => ({
    application: app.slug,
  }));
}
