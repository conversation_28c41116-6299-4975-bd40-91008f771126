import { Metadata } from "next";

const applicationNames: Record<string, string> = {
  autoskola: "Autoškola",
  "zbrojni-prukaz": "Zbrojní průkaz",
  "potapecsky-prukaz": "Potápěčský průkaz",
  straznik: "Strážník",
  "kapitanske-zkousky": "Kapitánské zkoušky",
};

type Params = Promise<{ application: string }>;
export async function generateMetadata({
  params,
}: {
  params: Params;
}): Promise<Metadata> {
  const { application } = await params;

  const applicationName = applicationNames[application] || "Aktuální testy";

  const title = `${applicationName} - Aktuální testy online`;
  const description = `Připravte se na zkoušky z ${applicationName.toLowerCase()} s Aktuálními testy. Procvičujte testové otázky online a zvyšte své šance na úspěch.`;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      url: `https://aktualnitesty.cz/${application}/uvod`,
      siteName: "Aktuální testy",
      locale: "cs_CZ",
      type: "website",
      images: [
        {
          url: "/screenshots/mobile.png",
          width: 1080,
          height: 2340,
          alt: "Aktuální testy aplikace",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: ["/screenshots/mobile.png"],
    },
  };
}
