"use server";

import React from "react";
import { PAGE_LIMIT } from "@/constants/pagination";
import { getApplicationBySlug } from "@/lib/services/applications";
import { getQuestions } from "@/lib/services/getQuestions";
import NotFound from "@/components/NotFound/NotFound";
import { QuestionsList } from "@/components/QuestionsList";
import { QuestionsWrapper } from "@/components/QuestionsWrapper/QuestionsWrapper";
import Menu from "@/components/Menu";
import { getQuestionInflection } from "@/utils/inflection";

type Params = Promise<{ application: string }>;

export default async function AllQuestionsPage({ params }: { params: Params }) {
  const { application } = await params;

  try {
    const applicationData = await getApplicationBySlug(application);

    if (!applicationData) {
      return <NotFound message="Aplikace nenalezena" />;
    }

    const initialData = await getQuestions({
      applicationData,
      limit: PAGE_LIMIT,
      offset: 0,
    });

    return (
      <div className="min-h-screen bg-gray-100">
        <div className="fixed top-0 left-0 right-0 z-50 bg-white shadow-sm h-[60px] sm:h-auto">
          <Menu application={applicationData} />
        </div>

        <QuestionsWrapper
          title={
            <div className="flex items-center gap-3">
              <span>Všechny otázky</span>
              <span className="text-base font-normal text-gray-500">
                ({initialData.allQuestionsCollection.totalCount}{" "}
                {getQuestionInflection(
                  initialData.allQuestionsCollection.totalCount,
                )}
                )
              </span>
            </div>
          }
          description="Kompletní seznam všech dostupných testových otázek"
        >
          <QuestionsList
            applicationData={applicationData}
            initialData={initialData}
          />
        </QuestionsWrapper>
      </div>
    );
  } catch (error) {
    console.error("Error fetching data:", error);
    return <NotFound message="Došlo k chybě při načítání dat" />;
  }
}
