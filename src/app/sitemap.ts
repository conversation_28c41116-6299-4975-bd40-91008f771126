import { MetadataRoute } from "next";
import { getApplications } from "@/lib/services/applications";
import { getAllQuestionsForApplication } from "@/lib/services/getAllQuestionsForApplication";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // Použití proměnné prostředí NEXTAUTH_URL jako základní URL
  const baseUrl = process.env.NEXTAUTH_URL || "http://localhost:3000";

  // Získání všech aplikací
  const applications = await getApplications({ withAuth: false });

  // Základní stránky
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: "weekly" as const,
      priority: 1.0,
    },
  ];

  // Stránky aplikací
  const applicationPages = applications.map((app) => ({
    url: `${baseUrl}/${app.slug}/uvod`,
    lastModified: new Date(),
    changeFrequency: "weekly" as const,
    priority: 0.9,
  }));

  // <PERSON>r<PERSON>ky se všemi otázkami
  const allQuestionsPages = applications.map((app) => ({
    url: `${baseUrl}/${app.slug}/vsechny-otazky`,
    lastModified: new Date(),
    changeFrequency: "weekly" as const,
    priority: 0.8,
  }));

  // Stránky jednotlivých otázek
  let questionPages: MetadataRoute.Sitemap = [];

  for (const app of applications) {
    try {
      const allQuestions = await getAllQuestionsForApplication(app.id);

      // Všechny otázky pro danou aplikaci
      const questionsWithId = allQuestions.filter(
        (question) => question && question.id,
      );

      // Výpočet počtu stránek (50 otázek na stránku)
      const QUESTIONS_PER_PAGE = 50;
      const totalPages = Math.ceil(questionsWithId.length / QUESTIONS_PER_PAGE);

      console.log(
        `Generating sitemap for ${app.slug}: ${totalPages} pages (total ${questionsWithId.length} questions)`,
      );

      // Stránky s otázkami
      const appQuestionListPages = Array.from(
        { length: totalPages },
        (_, i) => ({
          url: `${baseUrl}/${app.slug}/otazky/${i + 1}`,
          lastModified: new Date(),
          changeFrequency: "weekly" as const,
          priority: 0.7,
        }),
      );

      // Generujeme URL pro stránky s otázkami
      questionPages = [...questionPages, ...appQuestionListPages];
    } catch (error) {
      console.error(`Error generating sitemap for app ${app.slug}:`, error);
    }
  }

  return [
    ...staticPages,
    ...applicationPages,
    ...allQuestionsPages,
    ...questionPages,
  ];
}
