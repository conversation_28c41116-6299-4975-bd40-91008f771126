import type { <PERSON>ada<PERSON>, Viewport } from "next";
import dynamic from "next/dynamic";
import { ApolloWrapper } from "@/lib/apollo-wrapper";
import { GoogleOAuthProvider } from "@react-oauth/google";
import { ToastProvider } from "@/components/ToastProvider";
import GoogleAnalytics from "@/components/GoogleAnalytics/GoogleAnalytics";
import dotenv from "dotenv";
import "../../public/output.css";
import "./globals.css";
import Footer from "@/components/Footer/Footer";

const CookieConsent = dynamic(() =>
  import("@/components/CookieConsent").then((mod) => mod.CookieConsent),
);

const ServiceWorkerRegistration = dynamic(
  () =>
    import("@/components/ServiceWorkerRegistration/ServiceWorkerRegistration"),
);

const SessionProviderWrapper = dynamic(
  () => import("@/lib/SessionProviderWrapper"),
);

const CartProvider = dynamic(() =>
  import("@/context/CartContext").then((mod) => mod.CartProvider),
);

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: "cover",
  themeColor: "#10B981",
};

export const metadata: Metadata = {
  title: "Aktuální testy online Autoškola | Zbrojní průkaz",
  description:
    "Aplikace pro přípravu na testy a zkoušky - autoškola, zbrojní průkaz a další",
  keywords:
    "autoškola, zbrojní průkaz, online testy, zkouška, řidičský průkaz, testové otázky",
  authors: [{ name: "Aktuální testy", url: "https://aktualnitesty.cz" }],
  creator: "Aktuální testy",
  publisher: "Aktuální testy",
  metadataBase: new URL("https://aktualnitesty.cz"),
  alternates: {
    canonical: "/",
    languages: {
      "cs-CZ": "/",
    },
  },
  openGraph: {
    title: "Aktuální testy online Autoškola | Zbrojní průkaz",
    description:
      "Aplikace pro přípravu na testy a zkoušky - autoškola, zbrojní průkaz a další",
    url: "https://aktualnitesty.cz",
    siteName: "Aktuální testy",
    locale: "cs_CZ",
    type: "website",
    images: [
      {
        url: "/screenshots/mobile.png",
        width: 1080,
        height: 2340,
        alt: "Aktuální testy aplikace",
      },
      {
        url: "/icons/icon-512x512.png",
        width: 512,
        height: 512,
        alt: "Aktuální testy logo",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Aktuální testy online Autoškola | Zbrojní průkaz",
    description:
      "Aplikace pro přípravu na testy a zkoušky - autoškola, zbrojní průkaz a další",
    images: ["/icons/icon-512x512.png"],
    creator: "@aktualnitesty",
    site: "@aktualnitesty",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
    },
  },
  manifest: "/manifest.json",
  applicationName: "Aktuální Testy",
  appleWebApp: {
    capable: true,
    statusBarStyle: "black-translucent",
    title: "Aktuální Testy",
  },
  formatDetection: {
    telephone: false,
  },
  icons: {
    icon: [
      { url: "/icons/icon-72x72.png", sizes: "72x72", type: "image/png" },
      { url: "/icons/icon-96x96.png", sizes: "96x96", type: "image/png" },
      { url: "/icons/icon-128x128.png", sizes: "128x128", type: "image/png" },
      { url: "/icons/icon-144x144.png", sizes: "144x144", type: "image/png" },
      { url: "/icons/icon-152x152.png", sizes: "152x152", type: "image/png" },
      { url: "/icons/icon-192x192.png", sizes: "192x192", type: "image/png" },
      { url: "/icons/icon-384x384.png", sizes: "384x384", type: "image/png" },
      { url: "/icons/icon-512x512.png", sizes: "512x512", type: "image/png" },
    ],
    apple: [
      {
        url: "/icons/apple-icon-120x120.png",
        sizes: "120x120",
        type: "image/png",
      },
      {
        url: "/icons/apple-icon-152x152.png",
        sizes: "152x152",
        type: "image/png",
      },
      {
        url: "/icons/apple-icon-180x180.png",
        sizes: "180x180",
        type: "image/png",
      },
    ],
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  dotenv.config();

  return (
    <html lang="cs">
      <head>
        <link rel="manifest" href="/manifest.json" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta
          name="apple-mobile-web-app-status-bar-style"
          content="black-translucent"
        />
        <link
          rel="apple-touch-icon"
          sizes="180x180"
          href="/icons/apple-icon-180x180.png"
        />
        <title>Aktuální testy</title>
      </head>
      <body>
        <GoogleAnalytics />
        <ApolloWrapper>
          <GoogleOAuthProvider clientId="615261397821-nka3g3f6ddqe2m2s5ebmic26edch9q2d.apps.googleusercontent.com">
            <SessionProviderWrapper>
              <CartProvider>
                {children}
                <ServiceWorkerRegistration />
                <Footer />
                <ToastProvider />
                <CookieConsent />
              </CartProvider>
            </SessionProviderWrapper>
          </GoogleOAuthProvider>
        </ApolloWrapper>
      </body>
    </html>
  );
}
