import ForgottenPasswordForm from "@/components/ForgottenPasswordForm/ForgottenPasswordForm";
import StaticMenu from "@/components/Menu/StaticMenu";

export default function ForgottenPasswordPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Menu */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200">
        <StaticMenu application={undefined} />
      </div>

      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute w-full h-full bg-grid opacity-10"></div>
        <div className="absolute -top-40 -left-40 w-80 h-80 bg-emerald-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
      </div>

      <div className="relative flex min-h-full flex-1 flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <h2 className="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900">
            Zapomenuté heslo
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Zadejte svůj email a my vám pošleme instrukce pro obnovení hesla
          </p>
        </div>

        <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
          <div className="bg-white/80 backdrop-blur-md px-6 py-12 shadow-lg sm:rounded-lg sm:px-12">
            <ForgottenPasswordForm />
          </div>
        </div>
      </div>
    </div>
  );
}
