"use client";

import { ActionResponse } from "@/types/registration";
import { signIn } from "next-auth/react";
import { validateAndRegisterUser } from "@/app/registrace/validateAndRegisterUser";
import type { LoginFormData } from "@/types/login";

export const handleSubmitForm = async (
  prevState: ActionResponse | null,
  formData: FormData,
): Promise<ActionResponse> => {
  const result = await validateAndRegisterUser(prevState, formData);

  if (result.success) {
    return await handleSignIn(result, formData);
  }

  return result;
};

const handleSignIn = async (
  result: ActionResponse,
  formData: FormData,
): Promise<ActionResponse> => {
  const signInResult = await signIn("credentials", {
    redirect: false,
    email: formData.get("email") as string,
    password: formData.get("password") as string,
  });

  const rawData: LoginFormData = {
    email: formData.get("email") as string,
    password: formData.get("password") as string,
  };

  if (signInResult?.error) {
    result.success = false;
    result.message =
      "Účet byl vytvořen, ale neporačilo se automatické prihlášení. Použijte přihlašovací formulář.";
    result.inputs = rawData;
  }

  return result;
};
