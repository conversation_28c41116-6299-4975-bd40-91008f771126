"use client";

import { useEffect } from "react";
import { showToast } from "@/components/Toast/config";
import { redirect } from "next/navigation";

const useSuccessHandler = (success: boolean, message: string) => {
  useEffect(() => {
    if (success) {
      showToast(`Registrace ${message} probě<PERSON>a <PERSON>.`, "success");
      redirect("/");
    }
  }, [success, message]);
};

export default useSuccessHandler;
