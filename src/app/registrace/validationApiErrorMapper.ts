"use server";

import { FORM_GLOBAL_ERROR_MESSAGE } from "@/app/registrace/constants/translations";
import type { RegistrationFormData } from "@/types/registration";

export const mappedValidationError = async (
  message: string,
  rawData: RegistrationFormData,
) => {
  const validationMap: { [key: string]: string } = {
    "Email validation": "email",
    "Password validation": "password",
  };

  for (const [key, value] of Object.entries(validationMap)) {
    if (message.startsWith(key)) {
      return {
        success: false,
        message: FORM_GLOBAL_ERROR_MESSAGE,
        errors: { [value]: [message.replace(`${key}: `, "")] },
        inputs: rawData,
      };
    }
  }

  return {
    success: false,
    message,
    inputs: rawData,
  };
};
