"use server";

import { z } from "zod";
import type {
  ActionResponse,
  RegistrationFormData,
} from "@/types/registration";
import { getClient } from "@/lib/client";
import { REGISTER_MUTATION } from "@/app/registrace/constants/getRegistrationMutation";
import { ApolloError } from "@apollo/client";
import { mappedValidationError } from "@/app/registrace/validationApiErrorMapper";
import { FORM_GLOBAL_ERROR_MESSAGE } from "@/app/registrace/constants/translations";

const addressSchema = z.object({
  email: z.string().trim().email("Email neni vallidní"),
  password: z.string().min(4, "Heslo musí být minimálně 4 znaky dlouhé"),
});

const registerUser = async (
  email: string,
  password: string,
  rawData: RegistrationFormData,
): Promise<ActionResponse> => {
  try {
    const client = getClient();
    const { data } = await client.mutate({
      mutation: REGISTER_MUTATION,
      variables: {
        email: email,
        password: password,
      },
    });

    return {
      success: true,
      message: data.register,
    };
  } catch (e: unknown) {
    if (e instanceof ApolloError) {
      return mappedValidationError(e.message, rawData);
    }
  }

  return {
    success: true,
    message: "User validated successfully!",
  };
};
export const validateAndRegisterUser = async (
  prevState: ActionResponse | null,
  formData: FormData,
): Promise<ActionResponse> => {
  try {
    const rawData: RegistrationFormData = {
      email: formData.get("email") as string,
      password: formData.get("password") as string,
    };

    // Validate the form data
    const validatedData = addressSchema.safeParse(rawData);

    if (!validatedData.success) {
      return {
        success: false,
        message: FORM_GLOBAL_ERROR_MESSAGE,
        errors: validatedData.error.flatten().fieldErrors,
        inputs: rawData,
      };
    }

    return registerUser(
      validatedData.data.email,
      validatedData.data.password,
      rawData,
    );
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error: unknown) {
    return {
      success: false,
      message: "An unexpected error occurred",
    };
  }
};
