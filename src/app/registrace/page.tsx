import dynamic from "next/dynamic";
import GoogleButton from "@/components/LoginForm/GoogleButton";
import StaticMenu from "@/components/Menu/StaticMenu";

const RegisterForm = dynamic(() => import("@/app/registrace/RegisterForm"));

export default function RegisterPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute w-full h-full bg-grid opacity-10"></div>
        <div className="absolute -top-40 -left-40 w-80 h-80 bg-emerald-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
      </div>

      {/* Menu */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200">
        <StaticMenu application={undefined} />
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center px-6">
        <div className="w-full max-w-md">
          <div className="bg-white/80 backdrop-blur-md shadow-xl rounded-2xl p-8">
            <div className="text-center mb-8">
              <h1 className="text-2xl font-bold text-gray-900">Registrace</h1>
              <p className="mt-2 text-sm text-gray-600">
                Vytvořte si nový účet
              </p>
            </div>

            <div className="space-y-6">
              <GoogleButton />

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">
                    Nebo pokračujte s emailem
                  </span>
                </div>
              </div>

              <RegisterForm />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
