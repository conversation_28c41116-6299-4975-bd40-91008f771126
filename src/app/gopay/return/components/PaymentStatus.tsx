"use client";

import { PaymentStatusType } from "@/types/payment";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import Link from "next/link";
import {
  Home,
  CreditCard,
  ShieldCheck,
  Clock,
  XCircle,
  PartyPopper,
} from "lucide-react";

interface PaymentStatusProps {
  paymentStatus: PaymentStatusType;
}

export function PaymentStatus({ paymentStatus }: PaymentStatusProps) {
  const router = useRouter();

  useEffect(() => {
    if (paymentStatus.status === "success") {
      const redirectTimeout = setTimeout(() => {
        router.push("/");
      }, 10000);

      return () => clearTimeout(redirectTimeout);
    }
  }, [paymentStatus.status, router]);

  return (
    <div className="bg-white/50 backdrop-blur-sm rounded-2xl p-8 shadow-xl">
      <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
        Stav platby
      </h1>

      {paymentStatus.status === "success" ? (
        <div className="mb-6 bg-gradient-to-br from-emerald-50 to-emerald-100/50 rounded-2xl p-6 border border-emerald-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-emerald-100 rounded-lg">
              <PartyPopper className="w-6 h-6 text-emerald-600" />
            </div>
            <h3 className="text-xl font-semibold text-emerald-900">
              {paymentStatus.message}
            </h3>
          </div>
          <p className="text-emerald-700">
            Máte plný přístup k zakoupeným testům. Přejeme hodně úspěchů při
            studiu!
          </p>
        </div>
      ) : paymentStatus.status === "error" ? (
        <div className="mb-6 bg-gradient-to-br from-red-50 to-red-100/50 rounded-2xl p-6 border border-red-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-red-100 rounded-lg">
              <XCircle className="w-6 h-6 text-red-600" />
            </div>
            <h3 className="text-xl font-semibold text-red-900">
              {paymentStatus.message}
            </h3>
          </div>
          <p className="text-red-700">
            Pro získání přístupu k testům prosím proveďte novou platbu.
          </p>
        </div>
      ) : (
        <div className="mb-6 bg-gradient-to-br from-yellow-50 to-yellow-100/50 rounded-2xl p-6 border border-yellow-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Clock className="w-6 h-6 text-yellow-600 animate-spin" />
            </div>
            <h3 className="text-xl font-semibold text-yellow-900">
              {paymentStatus.message}
            </h3>
          </div>
          <p className="text-yellow-700">
            Prosím vyčkejte na dokončení platby. Poté budete mít plný přistup k
            zakoupeným testům.
          </p>
        </div>
      )}

      {paymentStatus.status === "success" && (
        <p className="text-sm text-gray-500 text-center mb-6">
          Budete přesměrováni na hlavní stránku za 10 sekund...
        </p>
      )}

      <div className="mb-8 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-gray-100 shadow-sm">
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-emerald-100 rounded-lg">
              <CreditCard className="w-5 h-5 text-emerald-600" />
            </div>
            <h3 className="font-medium text-gray-900">Bezpečná platba</h3>
          </div>
          <p className="text-sm text-gray-600">
            Platba je zpracována přes zabezpečenou bránu GoPay
          </p>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-gray-100 shadow-sm">
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-blue-100 rounded-lg">
              <ShieldCheck className="w-5 h-5 text-blue-600" />
            </div>
            <h3 className="font-medium text-gray-900">Ověřená transakce</h3>
          </div>
          <p className="text-sm text-gray-600">
            Všechny transakce jsou šifrovány a monitorovány
          </p>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-gray-100 shadow-sm">
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Clock className="w-5 h-5 text-purple-600" />
            </div>
            <h3 className="font-medium text-gray-900">Okamžitý přístup</h3>
          </div>
          <p className="text-sm text-gray-600">
            Po úspěšné platbě získáte okamžitý přístup k obsahu
          </p>
        </div>
      </div>

      <div className="text-center">
        <Link
          href="/"
          className="inline-flex items-center gap-2 px-8 py-3 border border-transparent text-base font-medium rounded-xl 
            text-white bg-emerald-600 hover:bg-emerald-700 
            transition-all duration-300 
            shadow-xl hover:shadow-2xl"
        >
          <Home className="w-5 h-5" />
          Zpět na hlavní stránku
        </Link>
      </div>
    </div>
  );
}
