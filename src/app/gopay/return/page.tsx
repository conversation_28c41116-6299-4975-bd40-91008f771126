import { redirect } from "next/navigation";
import { checkPaymentStatus } from "./actions";
import Menu from "@/components/Menu";
import { PaymentStatus } from "./components/PaymentStatus";
import { Suspense } from "react";

export const dynamic = "force-dynamic";
export const revalidate = 0;

type Params = Promise<{
  id?: string;
}>;

export default async function GopayReturnPage({
  searchParams,
}: {
  searchParams: Params;
}) {
  const { id: paymentId } = await searchParams;

  if (!paymentId) {
    redirect("/");
  }

  const paymentStatus = await checkPaymentStatus(paymentId);

  return (
    <div className="relative min-h-screen w-full bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Background grid pattern */}
      <div className="absolute inset-0 w-full h-full bg-grid opacity-10 pointer-events-none"></div>

      {/* Menu */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200/20">
        <Suspense fallback={<div>Načítání...</div>}>
          <Menu application={undefined} />
        </Suspense>
      </div>

      {/* Content wrapper */}
      <div className="relative w-full">
        <div className="relative min-h-screen flex items-center">
          {/* Animated background elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className="absolute -top-40 -left-40 w-80 h-80 bg-emerald-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
            <div className="absolute -bottom-40 left-20 w-80 h-80 bg-emerald-50 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
          </div>

          {/* Main content */}
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-32">
            <div className="max-w-2xl mx-auto">
              <Suspense
                fallback={
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-emerald-500"></div>
                  </div>
                }
              >
                <PaymentStatus paymentStatus={paymentStatus} />
              </Suspense>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
