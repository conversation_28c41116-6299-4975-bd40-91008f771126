"use server";

import { gql } from "@apollo/client";
import { getClient } from "@/lib/client";
import { PaymentStatusType } from "@/types/payment";

const CHECK_PAYMENT_STATUS = gql`
  query CheckPaymentStatus($paymentId: String!) {
    checkPaymentStatus(paymentId: $paymentId) {
      status
      message
    }
  }
`;

interface PaymentStatusResponse {
  checkPaymentStatus: PaymentStatusType;
}

export async function checkPaymentStatus(
  paymentId: string,
): Promise<PaymentStatusType> {
  if (!paymentId) {
    return {
      status: "error",
      message: "Chybí ID platby",
    };
  }

  try {
    const client = getClient();
    const { data, errors } = await client.query<PaymentStatusResponse>({
      query: CHECK_PAYMENT_STATUS,
      variables: { paymentId },
      fetchPolicy: "no-cache",
    });

    if (errors) {
      console.error("GraphQL errors:", errors);
      return {
        status: "error",
        message: "<PERSON><PERSON>a chyba při ov<PERSON>ní platby",
      };
    }

    if (!data?.checkPaymentStatus) {
      return {
        status: "error",
        message: "Nepodařilo se získat informace o platbě",
      };
    }

    return data.checkPaymentStatus;
  } catch (error) {
    console.error("Payment status check error:", error);
    return {
      status: "error",
      message: "Nepodařilo se ověřit stav platby",
    };
  }
}
