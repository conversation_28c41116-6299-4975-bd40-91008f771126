import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

// 1. Specify protected and public routes
const protectedRoutes = [/^\/[^\/]+\/oblibene-otazky$/, "/kosik", "/profil"];

export default async function middleware(req: NextRequest) {
  // 2. Check if the current route is protected or public
  const path = req.nextUrl.pathname;
  const isProtectedRoute = protectedRoutes.some((route) =>
    typeof route === "string" ? route === path : route.test(path),
  );

  try {
    // 3. Get session using getToken
    const token = await getToken({ req });

    // 4. Redirect to /prihlaseni if the user is not authenticated
    if (isProtectedRoute && !token) {
      return NextResponse.redirect(new URL("/prihlaseni", req.nextUrl));
    }

    // 5. Redirect to / if the user is authenticated
    if (
      token &&
      (req.nextUrl.pathname.startsWith("/prihlaseni") ||
        req.nextUrl.pathname.startsWith("/zapomenute-heslo") ||
        req.nextUrl.pathname.startsWith("/zmenit-heslo") ||
        req.nextUrl.pathname.startsWith("/registrace"))
    ) {
      return NextResponse.redirect(new URL("/", req.nextUrl));
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    // Handle fetch error (e.g., redirect to an error page or show a notification)
    return NextResponse.redirect(new URL("/", req.nextUrl));
  }

  return NextResponse.next();
}

// Routes Middleware should not run on
export const config = {
  matcher: ["/((?!api|_next/static|_next/image|.*\\.png$).*)"],
};
