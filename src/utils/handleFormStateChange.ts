import { FORM_GLOBAL_ERROR_MESSAGE } from "@/app/registrace/constants/translations";
import { ActionResponse as ActionLoginResponse } from "@/types/login";
import { ActionResponse as ActionRegistrationResponse } from "@/types/registration";
import { ActionResponse as ActionForgottenPasswordResponse } from "@/types/forgottenPassword";
import { ActionResponse as ActionChangeForgottenPasswordResponse } from "@/types/changeForgottenPassword";
import { ActionResponse as ActionCartResponse } from "@/types/cart";

export const handleFormStateChange = (
  name: string,
  state:
    | ActionLoginResponse
    | ActionRegistrationResponse
    | ActionForgottenPasswordResponse
    | ActionChangeForgottenPasswordResponse
    | ActionCartResponse,
) => {
  if (state?.errors?.[name as keyof typeof state.errors]?.[0]) {
    delete state.errors[name as keyof typeof state.errors];
  }

  if (
    state?.message === FORM_GLOBAL_ERROR_MESSAGE &&
    state?.errors &&
    Object.keys(state.errors).length === 0
  ) {
    state.message = "";
  }
};
