import { ApolloClient, InMemoryCache, HttpLink, from } from "@apollo/client";

export function getClient() {
  const httpLink = new HttpLink({
    uri: process.env.NEXT_PUBLIC_GRAPHQL_HOST,
    headers: {
      authorization: `Basic ${btoa("root:password123")}`,
    },
  });

  return new ApolloClient({
    cache: new InMemoryCache(),
    link: from([httpLink]),
    defaultOptions: {
      query: {
        fetchPolicy: "network-only",
      },
      watchQuery: {
        fetchPolicy: "network-only",
      },
    },
    ssrMode: typeof window === "undefined",
  });
}
