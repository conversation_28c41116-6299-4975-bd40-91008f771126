"use server";

import { getClient } from "@/lib/client";
import { GET_USER_ORDERS } from "@/app/profil/query";
import { UserOrders } from "@/graphql/types/UserOrders";
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth.config";
import { ExtendedDefaultSession } from "@/types/extendedDefaultSession";

export async function getUserOrders(): Promise<UserOrders | null> {
  const session = (await getServerSession(
    authConfig,
  )) as ExtendedDefaultSession;

  if (!session?.user?.token) {
    return null;
  }

  const client = getClient();

  try {
    const { data } = await client.query<UserOrders>({
      query: GET_USER_ORDERS,
      context: {
        headers: {
          Authorization: `Bearer ${session.user.token}`,
        },
      },
    });

    return data;
  } catch (error) {
    console.error("Error fetching user orders:", error);
    return null;
  }
}
