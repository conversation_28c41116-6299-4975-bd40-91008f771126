"use server";

import { getClient } from "@/lib/client";
import { GET_ALL_QUESTIONS_COLLECTION } from "./query";
import type {
  AllQuestionsCollection,
  AllQuestionsCollectionVariables,
} from "@/graphql/types/AllQuestionsCollection";
import { Application_application } from "@/graphql/types/Application";
import { Category_category } from "@/graphql/types/Category";

interface GetQuestionsParams {
  applicationData: Application_application;
  categoryData?: Category_category;
  sectionId?: string;
  limit?: number;
  offset?: number;
}

export const getQuestions = async ({
  applicationData,
  categoryData,
  sectionId,
  limit = 10,
  offset = 0,
}: GetQuestionsParams): Promise<AllQuestionsCollection> => {
  const client = getClient();

  try {
    const { data } = await client.query<
      AllQuestionsCollection,
      AllQuestionsCollectionVariables
    >({
      query: GET_ALL_QUESTIONS_COLLECTION,
      variables: {
        applicationId: applicationData.id.toString(),
        categoryId: categoryData?.id?.toString(),
        sectionId,
        limit,
        offset,
      },
    });

    return data;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    return {
      allQuestionsCollection: {
        __typename: "QuestionPayloadsPaginator",
        totalCount: 0,
        edges: [],
        pageInfo: {
          __typename: "PageInfo",
          hasNextPage: false,
          hasPreviousPage: false,
          startCursor: null,
          endCursor: null,
        },
      },
    };
  }
};
