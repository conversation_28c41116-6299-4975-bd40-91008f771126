"use server";

import { getClient } from "@/lib/client";
import { gql } from "@apollo/client";
import { Exam, ExamVariables } from "@/graphql/types/Exam";
import { Application_application } from "@/graphql/types/Application";
import { Category_category } from "@/graphql/types/Category";

const GET_QUESTIONS_BY_CATEGORY = gql`
  query Exam($applicationId: ID!, $categoryId: ID!) {
    exam(applicationId: $applicationId, categoryId: $categoryId) {
      questions {
        questionGroup {
          title
        }
        answers {
          text
          correct
          image {
            url
            fileName
          }
          id
        }
        text
        id
        image {
          url
          fileName
        }
        video {
          url
          fileName
          thumbnailImage {
            url
            fileName
          }
        }
      }
    }
  }
`;

export async function getQuestionsByCategory(
  applicationData: Application_application,
  category: Category_category,
) {
  const client = getClient();

  try {
    const { data } = await client.query<Exam, ExamVariables>({
      query: GET_QUESTIONS_BY_CATEGORY,
      variables: {
        applicationId: applicationData.id.toString(),
        categoryId: category.id.toString(),
      },
    });

    return data.exam.questions;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    return [];
  }
}
