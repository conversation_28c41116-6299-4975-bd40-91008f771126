"use server";

import { getClient } from "@/lib/client";
import { gql } from "@apollo/client";
import {
  ExamByQuestionGroup,
  ExamByQuestionGroupVariables,
} from "@/graphql/types/ExamByQuestionGroup";
import { Application_application } from "@/graphql/types/Application";
import { Category_category } from "@/graphql/types/Category";
import { Section_section } from "@/graphql/types/Section";

const GET_EXAM_BY_QUESTION_GROUP = gql`
  query ExamByQuestionGroup(
    $applicationId: ID!
    $categoryId: ID!
    $questionGroupId: ID!
  ) {
    examByQuestionGroup(
      applicationId: $applicationId
      categoryId: $categoryId
      questionGroupId: $questionGroupId
    ) {
      questions {
        id
        image {
          url
          fileName
        }
        video {
          url
          fileName
          thumbnailImage {
            url
            fileName
          }
        }
        text
        answers {
          id
          text
          image {
            url
            fileName
          }
          correct
        }
      }
    }
  }
`;

export async function getQuestionsByQuestionGroup(
  applicationData: Application_application,
  categoryData: Category_category,
  sectionData: Section_section,
) {
  const client = getClient();

  try {
    const { data } = await client.query<
      ExamByQuestionGroup,
      ExamByQuestionGroupVariables
    >({
      query: GET_EXAM_BY_QUESTION_GROUP,
      variables: {
        applicationId: applicationData.id.toString(),
        categoryId: categoryData.id.toString(),
        questionGroupId: sectionData.questionGroup.id.toString(),
      },
    });

    return data.examByQuestionGroup.questions;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    return [];
  }
}
