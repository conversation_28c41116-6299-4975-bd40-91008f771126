import { gql } from "@apollo/client";

export const GET_APPLICATIONS = gql`
  query Applications {
    applications {
      application {
        id
        name
        slug
        token
        price
        questionsTotalCount
        isPaid
        studentsCount
        averageSuccessRate
      }
    }
  }
`;

export const GET_APPLICATION = gql`
  query Application($slug: String!) {
    application(slug: $slug) {
      id
      name
      slug
      price
      token
      questionsTotalCount
      isPaid
      studentsCount
      averageSuccessRate
    }
  }
`;

export const GET_ALL_QUESTIONS_COLLECTION = gql`
  query AllQuestionsCollection(
    $applicationId: ID!
    $limit: Int!
    $offset: Int!
    $categoryId: ID
    $sectionId: ID
  ) {
    allQuestionsCollection(
      applicationId: $applicationId
      limit: $limit
      offset: $offset
      categoryId: $categoryId
      sectionId: $sectionId
    ) {
      totalCount
      edges {
        cursor
        node {
          id
          codeMdcr
          text
          image {
            url
            fileName
          }
          video {
            url
            fileName
            thumbnailImage {
              url
              fileName
            }
          }
          answers {
            id
            text
            image {
              url
              fileName
            }
            correct
          }
          questionGroup {
            id
            title
            description
          }
        }
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
    }
  }
`;
