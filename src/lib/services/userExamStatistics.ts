"use server";

import { getClient } from "@/lib/client";
import { GET_USER_EXAM_STATISTICS } from "@/app/[application]/statistiky/query";
import {
  UserExamStatistics,
  UserExamStatisticsVariables,
} from "@/graphql/types/UserExamStatistics";
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth.config";
import { ExtendedDefaultSession } from "@/types/extendedDefaultSession";

export async function getUserExamStatistics(
  applicationId: string,
): Promise<UserExamStatistics | null> {
  const session = (await getServerSession(
    authConfig,
  )) as ExtendedDefaultSession;

  if (!session?.user?.token) {
    return null;
  }

  const client = getClient();

  try {
    const { data } = await client.query<
      UserExamStatistics,
      UserExamStatisticsVariables
    >({
      query: GET_USER_EXAM_STATISTICS,
      variables: {
        applicationId,
      },
      context: {
        headers: {
          Authorization: `Bearer ${session.user.token}`,
        },
      },
    });

    return data;
  } catch (error) {
    console.error("Error fetching user exam statistics:", error);
    return null;
  }
}
