"use server";

import { getClient } from "@/lib/client";
import { gql } from "@apollo/client";
import { Application_application } from "@/graphql/types/Application";
import { Category, CategoryVariables } from "@/graphql/types/Category";
import { Categories, CategoriesVariables } from "@/graphql/types/Categories";

const GET_CATEGORY_BY_SLUG = gql`
  query Category($slug: String!, $applicationId: ID!) {
    category(slug: $slug, applicationId: $applicationId) {
      id
      name
      slug
      title
      priority
      questionsCount
      timeLimitInMinutes
      maximumPoints
      pointsToPass
    }
  }
`;

const GET_CATEGORIES = gql`
  query Categories($applicationId: ID!) {
    categories(applicationId: $applicationId) {
      category {
        id
        name
        slug
        title
        priority
        questionsCount
        pointsToPass
        timeLimitInMinutes
        maximumPoints
        isFavourite
        image {
          id
          fileName
          url
        }
      }
    }
  }
`;

export async function getCategoryBySlug(
  slug: string,
  applicationData: Application_application,
) {
  const client = getClient();
  try {
    const { data } = await client.query<Category, CategoryVariables>({
      query: GET_CATEGORY_BY_SLUG,
      variables: {
        slug,
        applicationId: applicationData.id.toString(),
      },
    });

    return data.category;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    return null;
  }
}

export async function getCategories(applicationId: string, token?: string) {
  const client = getClient();

  try {
    const { data } = await client.query<Categories, CategoriesVariables>({
      query: GET_CATEGORIES,
      variables: {
        applicationId,
      },
      context: token
        ? {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        : undefined,
    });

    return data.categories?.category || [];
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    return [];
  }
}
