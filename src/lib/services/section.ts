"use server";

import { getClient } from "@/lib/client";
import { gql } from "@apollo/client";
import { Section, SectionVariables } from "@/graphql/types/Section";
import { Application_application } from "@/graphql/types/Application";
import { Category_category } from "@/graphql/types/Category";

const GET_SECTION_BY_ID = gql`
  query Section($applicationId: ID!, $categoryId: ID!, $sectionId: ID!) {
    section(
      applicationId: $applicationId
      categoryId: $categoryId
      sectionId: $sectionId
    ) {
      id
      questionCount
      pointsPerQuestion
      category {
        id
        name
        slug
      }
      questionGroup {
        id
        title
      }
      createdAt
    }
  }
`;

export async function getSectionBySectionId(
  applicationData: Application_application,
  categoryData: Category_category,
  sectionId: string,
) {
  const client = getClient();

  try {
    const { data } = await client.query<Section, SectionVariables>({
      query: GET_SECTION_BY_ID,
      variables: {
        applicationId: applicationData.id.toString(),
        categoryId: categoryData.id.toString(),
        sectionId,
      },
    });

    return data.section;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    return null;
  }
}
