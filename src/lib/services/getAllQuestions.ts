"use server";

import { getClient } from "@/lib/client";
import { GET_ALL_QUESTIONS_COLLECTION } from "./query";
import type {
  AllQuestionsCollection,
  AllQuestionsCollectionVariables,
} from "@/graphql/types/AllQuestionsCollection";

export const getAllQuestions = async (
  applicationId: number,
  limit: number,
  offset: number,
): Promise<AllQuestionsCollection> => {
  const client = getClient();

  const { data } = await client.query<
    AllQuestionsCollection,
    AllQuestionsCollectionVariables
  >({
    query: GET_ALL_QUESTIONS_COLLECTION,
    variables: {
      applicationId: applicationId.toString(),
      limit,
      offset,
    },
  });

  return data;
};
