"use server";

import { getClient } from "@/lib/client";
import { gql } from "@apollo/client";
import {
  ExamAllQuestionsByQuestionGroup,
  ExamAllQuestionsByQuestionGroupVariables,
} from "@/graphql/types/ExamAllQuestionsByQuestionGroup";
import { Application_application } from "@/graphql/types/Application";
import { Category_category } from "@/graphql/types/Category";
import { Section_section } from "@/graphql/types/Section";

const GET_ALL_QUESTIONS = gql`
  query ExamAllQuestionsByQuestionGroup(
    $applicationId: ID!
    $categoryId: ID!
    $questionGroupId: ID!
  ) {
    examAllQuestionsByQuestionGroup(
      applicationId: $applicationId
      categoryId: $categoryId
      questionGroupId: $questionGroupId
    ) {
      questions {
        id
        image {
          url
          fileName
        }
        video {
          url
          fileName
          thumbnailImage {
            url
            fileName
          }
        }
        text
        answers {
          id
          text
          image {
            url
            fileName
          }
          correct
        }
      }
    }
  }
`;

export async function getAllQuestionsBySection(
  applicationData: Application_application,
  categoryData: Category_category,
  sectionData: Section_section,
) {
  const client = getClient();

  try {
    const { data } = await client.query<
      ExamAllQuestionsByQuestionGroup,
      ExamAllQuestionsByQuestionGroupVariables
    >({
      query: GET_ALL_QUESTIONS,
      variables: {
        applicationId: applicationData.id.toString(),
        categoryId: categoryData.id.toString(),
        questionGroupId: sectionData.questionGroup.id.toString(),
      },
    });

    return data.examAllQuestionsByQuestionGroup.questions;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    return [];
  }
}
