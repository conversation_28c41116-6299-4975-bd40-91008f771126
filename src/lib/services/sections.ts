"use server";

import { getClient } from "@/lib/client";
import { gql } from "@apollo/client";
import { Sections } from "@/graphql/types/Sections";
import { Application_application } from "@/graphql/types/Application";
import { Category_category } from "@/graphql/types/Category";

const GET_SECTIONS = gql`
  query Sections($applicationId: ID!, $categoryId: ID!) {
    sections(applicationId: $applicationId, categoryId: $categoryId) {
      section {
        id
        category {
          title
          name
          slug
          questionsCount
          maximumPoints
          image {
            fileName
            filePath
          }
          priority
          timeLimitInMinutes
          pointsToPass
        }
        questionGroup {
          title
          description
          id
          createdAt
        }
        questionCount
        pointsPerQuestion
      }
    }
  }
`;

export async function getSectionsByApplication(
  applicationData: Application_application,
  category: Category_category,
) {
  const client = getClient();

  const { data } = await client.query<Sections>({
    query: GET_SECTIONS,
    variables: {
      applicationId: applicationData.id,
      categoryId: category?.id || 0,
    },
  });

  return data?.sections?.section || [];
}
