"use server";

import { getClient } from "@/lib/client";
import { GET_APPLICATION, GET_APPLICATIONS } from "@/lib/services/query";
import { Application, ApplicationVariables } from "@/graphql/types/Application";
import { Applications } from "@/graphql/types/Applications";
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth.config";
import { ExtendedDefaultSession } from "@/types/extendedDefaultSession";

interface Options {
  withAuth?: boolean;
}

export async function getApplications(options: Options = { withAuth: true }) {
  const client = getClient();
  let session = null;

  if (options.withAuth) {
    session = (await getServerSession(authConfig)) as ExtendedDefaultSession;
  }

  const { data } = await client.query<Applications>({
    query: GET_APPLICATIONS,
    context: session?.user?.token
      ? {
          headers: {
            Authorization: `Bearer ${session.user.token}`,
          },
        }
      : undefined,
  });

  if (!data || !data.applications || !data.applications.application) {
    return [];
  }

  return data.applications.application;
}

export async function getApplicationBySlug(
  slug: string,
  options: Options = { withAuth: true },
) {
  const client = getClient();
  let session = null;

  if (options.withAuth) {
    session = (await getServerSession(authConfig)) as ExtendedDefaultSession;
  }

  try {
    const { data } = await client.query<Application, ApplicationVariables>({
      query: GET_APPLICATION,
      variables: {
        slug,
      },
      context: session?.user?.token
        ? {
            headers: {
              Authorization: `Bearer ${session.user.token}`,
            },
          }
        : undefined,
    });

    return data?.application;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    return null;
  }
}
