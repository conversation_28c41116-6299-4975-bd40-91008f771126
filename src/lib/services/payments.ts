"use server";

import { getClient } from "@/lib/client";
import { gql } from "@apollo/client";
import { PaymentMethodTypes } from "@/graphql/types/PaymentMethodTypes";

const GET_PAYMENT_METHOD_TYPES = gql`
  query PaymentMethodTypes {
    paymentMethodTypes {
      paymentMethodType {
        id
        name
        description
        paymentMethods {
          id
          name
          imageUrl
          position
          instrument
          instrumentSwift
        }
      }
    }
  }
`;

export async function getPaymentMethodTypes() {
  const client = getClient();
  try {
    const { data } = await client.query<PaymentMethodTypes>({
      query: GET_PAYMENT_METHOD_TYPES,
    });

    return data.paymentMethodTypes.paymentMethodType;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    return [];
  }
}
