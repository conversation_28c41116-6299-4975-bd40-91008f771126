"use server";

import { getAllQuestions } from "./getAllQuestions";
import { AllQuestionsCollection_allQuestionsCollection_edges_node } from "@/graphql/types/AllQuestionsCollection";

export async function getAllQuestionsForApplication(
  applicationId: number,
): Promise<AllQuestionsCollection_allQuestionsCollection_edges_node[]> {
  // Velikost dávky pro načítání otázek - zvýšeno pro rychlejší načítání
  const BATCH_SIZE = 1500;
  let allQuestions: AllQuestionsCollection_allQuestionsCollection_edges_node[] =
    [];
  let offset = 0;
  let hasMore = true;

  // Logování začátku načítání otázek
  console.log(
    `Loading questions for application ${applicationId} with batch size ${BATCH_SIZE}`,
  );

  while (hasMore) {
    console.log(
      `Loading batch of questions: offset=${offset}, total loaded so far=${allQuestions.length}`,
    );
    const data = await getAllQuestions(applicationId, BATCH_SIZE, offset);
    const questions = data.allQuestionsCollection.edges.map(
      (edge) => edge.node,
    );
    allQuestions = [...allQuestions, ...questions];

    hasMore = data.allQuestionsCollection.pageInfo.hasNextPage;
    offset += BATCH_SIZE;

    // Bezpečnostní pojistka proti nekonečné smyčce
    if (allQuestions.length >= data.allQuestionsCollection.totalCount) {
      hasMore = false;
    }

    console.log(
      `Loaded ${questions.length} questions in this batch, total=${allQuestions.length}, hasMore=${hasMore}`,
    );
  }

  console.log(
    `Finished loading all ${allQuestions.length} questions for application ${applicationId}`,
  );

  return allQuestions;
}
