"use client";

import {
  Apollo<PERSON><PERSON>,
  Apollo<PERSON><PERSON>ider,
  InM<PERSON>ory<PERSON>ache,
  HttpLink,
  from,
} from "@apollo/client";
import { useMemo } from "react";

function makeClient() {
  const httpLink = new HttpLink({
    uri: process.env.NEXT_PUBLIC_GRAPHQL_HOST,
    headers: {
      authorization: `Basic ${btoa("root:password123")}`,
    },
  });

  return new ApolloClient({
    cache: new InMemoryCache(),
    link: from([httpLink]),
    defaultOptions: {
      query: {
        fetchPolicy: "network-only",
      },
      watchQuery: {
        fetchPolicy: "network-only",
      },
    },
    ssrMode: typeof window === "undefined",
  });
}

export function ApolloWrapper({ children }: { children: React.ReactNode }) {
  const client = useMemo(() => makeClient(), []);

  return <ApolloProvider client={client}>{children}</ApolloProvider>;
}
