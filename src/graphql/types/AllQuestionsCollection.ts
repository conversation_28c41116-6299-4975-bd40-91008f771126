/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: AllQuestionsCollection
// ====================================================

export interface AllQuestionsCollection_allQuestionsCollection_edges_node_image {
  __typename: "ImagePayload";
  url: string;
  fileName: string;
}

export interface AllQuestionsCollection_allQuestionsCollection_edges_node_video_thumbnailImage {
  __typename: "ImagePayload";
  url: string;
  fileName: string;
}

export interface AllQuestionsCollection_allQuestionsCollection_edges_node_video {
  __typename: "VideoPayload";
  url: string;
  fileName: string;
  thumbnailImage: AllQuestionsCollection_allQuestionsCollection_edges_node_video_thumbnailImage;
}

export interface AllQuestionsCollection_allQuestionsCollection_edges_node_answers_image {
  __typename: "ImagePayload";
  url: string;
  fileName: string;
}

export interface AllQuestionsCollection_allQuestionsCollection_edges_node_answers {
  __typename: "AnswerPayload";
  id: number;
  text: string | null;
  image: AllQuestionsCollection_allQuestionsCollection_edges_node_answers_image | null;
  correct: boolean;
}

export interface AllQuestionsCollection_allQuestionsCollection_edges_node_questionGroup {
  __typename: "QuestionGroupPayload";
  id: number;
  title: string;
  description: string | null;
}

export interface AllQuestionsCollection_allQuestionsCollection_edges_node {
  __typename: "QuestionPayload";
  id: number;
  codeMdcr: string | null;
  text: string;
  image: AllQuestionsCollection_allQuestionsCollection_edges_node_image | null;
  video: AllQuestionsCollection_allQuestionsCollection_edges_node_video | null;
  answers: AllQuestionsCollection_allQuestionsCollection_edges_node_answers[];
  questionGroup: AllQuestionsCollection_allQuestionsCollection_edges_node_questionGroup;
}

export interface AllQuestionsCollection_allQuestionsCollection_edges {
  __typename: "QuestionEdge";
  cursor: string;
  node: AllQuestionsCollection_allQuestionsCollection_edges_node;
}

export interface AllQuestionsCollection_allQuestionsCollection_pageInfo {
  __typename: "PageInfo";
  /**
   * When paginating forwards, are there more items?
   */
  hasNextPage: boolean;
  /**
   * When paginating backwards, are there more items?
   */
  hasPreviousPage: boolean;
  /**
   * When paginating backwards, the cursor to continue.
   */
  startCursor: string | null;
  /**
   * When paginating forwards, the cursor to continue.
   */
  endCursor: string | null;
}

export interface AllQuestionsCollection_allQuestionsCollection {
  __typename: "QuestionPayloadsPaginator";
  totalCount: number;
  edges: AllQuestionsCollection_allQuestionsCollection_edges[];
  pageInfo: AllQuestionsCollection_allQuestionsCollection_pageInfo;
}

export interface AllQuestionsCollection {
  allQuestionsCollection: AllQuestionsCollection_allQuestionsCollection;
}

export interface AllQuestionsCollectionVariables {
  applicationId: string;
  limit: number;
  offset: number;
  categoryId?: string | null;
  sectionId?: string | null;
}
