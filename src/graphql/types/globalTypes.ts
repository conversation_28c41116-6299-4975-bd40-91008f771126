/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

//==============================================================
// START Enums and Input Objects
//==============================================================

export interface ExamAnswerInput {
  questionId: number;
  answerId: number;
}

export interface PaymentDataInput {
  paymentMethodId: number;
  items: string[];
}

//==============================================================
// END Enums and Input Objects
//==============================================================
