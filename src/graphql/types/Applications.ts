/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: Applications
// ====================================================

export interface Applications_applications_application {
  __typename: "ApplicationPayload";
  id: number;
  name: string;
  slug: string;
  token: string;
  price: number;
  questionsTotalCount: number;
  isPaid: boolean;
  studentsCount: number;
  averageSuccessRate: number;
}

export interface Applications_applications {
  __typename: "ApplicationsPayload";
  application: Applications_applications_application[];
}

export interface Applications {
  applications: Applications_applications;
}
