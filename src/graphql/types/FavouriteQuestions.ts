/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: FavouriteQuestions
// ====================================================

export interface FavouriteQuestions_favouriteQuestions_questions {
  __typename: "QuestionPayload";
  id: number;
}

export interface FavouriteQuestions_favouriteQuestions {
  __typename: "FavouriteQuestionsPayload";
  questions: FavouriteQuestions_favouriteQuestions_questions[];
}

export interface FavouriteQuestions {
  favouriteQuestions: FavouriteQuestions_favouriteQuestions;
}

export interface FavouriteQuestionsVariables {
  applicationId: string;
}
