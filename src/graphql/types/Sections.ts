/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: Sections
// ====================================================

export interface Sections_sections_section_category_image {
  __typename: "ImagePayload";
  fileName: string;
  filePath: string;
}

export interface Sections_sections_section_category {
  __typename: "CategoryPayload";
  title: string;
  name: string;
  slug: string;
  questionsCount: number;
  maximumPoints: number;
  image: Sections_sections_section_category_image | null;
  priority: number;
  timeLimitInMinutes: number;
  pointsToPass: number;
}

export interface Sections_sections_section_questionGroup {
  __typename: "QuestionGroupPayload";
  title: string;
  description: string | null;
  id: number;
  createdAt: any;
}

export interface Sections_sections_section {
  __typename: "SectionPayload";
  id: number;
  category: Sections_sections_section_category;
  questionGroup: Sections_sections_section_questionGroup;
  questionCount: number;
  pointsPerQuestion: number;
}

export interface Sections_sections {
  __typename: "SectionsPayload";
  section: Sections_sections_section[];
}

export interface Sections {
  sections: Sections_sections;
}

export interface SectionsVariables {
  applicationId: string;
  categoryId: string;
}
