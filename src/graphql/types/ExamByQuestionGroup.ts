/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: ExamByQuestionGroup
// ====================================================

export interface ExamByQuestionGroup_examByQuestionGroup_questions_image {
  __typename: "ImagePayload";
  url: string;
  fileName: string;
}

export interface ExamByQuestionGroup_examByQuestionGroup_questions_video_thumbnailImage {
  __typename: "ImagePayload";
  url: string;
  fileName: string;
}

export interface ExamByQuestionGroup_examByQuestionGroup_questions_video {
  __typename: "VideoPayload";
  url: string;
  fileName: string;
  thumbnailImage: ExamByQuestionGroup_examByQuestionGroup_questions_video_thumbnailImage;
}

export interface ExamByQuestionGroup_examByQuestionGroup_questions_answers_image {
  __typename: "ImagePayload";
  url: string;
  fileName: string;
}

export interface ExamByQuestionGroup_examByQuestionGroup_questions_answers {
  __typename: "AnswerPayload";
  id: number;
  text: string | null;
  image: ExamByQuestionGroup_examByQuestionGroup_questions_answers_image | null;
  correct: boolean;
}

export interface ExamByQuestionGroup_examByQuestionGroup_questions {
  __typename: "QuestionPayload";
  id: number;
  image: ExamByQuestionGroup_examByQuestionGroup_questions_image | null;
  video: ExamByQuestionGroup_examByQuestionGroup_questions_video | null;
  text: string;
  answers: ExamByQuestionGroup_examByQuestionGroup_questions_answers[];
}

export interface ExamByQuestionGroup_examByQuestionGroup {
  __typename: "ExamPayload";
  questions: ExamByQuestionGroup_examByQuestionGroup_questions[];
}

export interface ExamByQuestionGroup {
  examByQuestionGroup: ExamByQuestionGroup_examByQuestionGroup;
}

export interface ExamByQuestionGroupVariables {
  applicationId: string;
  categoryId: string;
  questionGroupId: string;
}
