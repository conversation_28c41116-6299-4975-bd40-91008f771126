/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: ActiveForgottenPassword
// ====================================================

export interface ActiveForgottenPassword_activeForgottenPassword_user {
  __typename: "UserPayload";
  email: string;
}

export interface ActiveForgottenPassword_activeForgottenPassword {
  __typename: "ForgottenPasswordPayload";
  id: string;
  validTo: any;
  user: ActiveForgottenPassword_activeForgottenPassword_user;
}

export interface ActiveForgottenPassword {
  activeForgottenPassword: ActiveForgottenPassword_activeForgottenPassword;
}

export interface ActiveForgottenPasswordVariables {
  forgottenPasswordToken: string;
}
