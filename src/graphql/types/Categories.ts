/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: Categories
// ====================================================

export interface Categories_categories_category_image {
  __typename: "ImagePayload";
  id: string;
  fileName: string;
  url: string;
}

export interface Categories_categories_category {
  __typename: "CategoryPayload";
  id: number;
  name: string;
  slug: string;
  title: string;
  priority: number;
  questionsCount: number;
  pointsToPass: number;
  timeLimitInMinutes: number;
  maximumPoints: number;
  isFavourite: boolean;
  image: Categories_categories_category_image | null;
}

export interface Categories_categories {
  __typename: "CategoriesPayload";
  category: Categories_categories_category[];
}

export interface Categories {
  categories: Categories_categories;
}

export interface CategoriesVariables {
  applicationId: string;
}
