/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ExamAnswerInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: EvaluateExam
// ====================================================

export interface EvaluateExam_evaluateExam_sections {
  __typename: "SectionResultPayload";
  sectionId: number;
  title: string;
  totalPoints: number;
  maximumPoints: number;
}

export interface EvaluateExam_evaluateExam {
  __typename: "EvaluatedExamPayload";
  isSuccessful: boolean;
  maximumPoints: number;
  percentageSuccess: number;
  pointsToPass: number;
  totalPoints: number;
  sections: EvaluateExam_evaluateExam_sections[];
}

export interface EvaluateExam {
  evaluateExam: EvaluateExam_evaluateExam;
}

export interface EvaluateExamVariables {
  applicationId: string;
  categorySlug: string;
  answers: ExamAnswerInput[];
}
