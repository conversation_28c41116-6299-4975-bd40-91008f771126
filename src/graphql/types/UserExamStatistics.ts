/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: UserExamStatistics
// ====================================================

export interface UserExamStatistics_userExamStatistics_examStatistics_category {
  __typename: "CategoryPayload";
  id: number;
  name: string;
  title: string;
}

export interface UserExamStatistics_userExamStatistics_examStatistics {
  __typename: "ExamStatisticPayload";
  id: string;
  totalPoints: number;
  maximumPoints: number;
  percentageSuccess: number;
  successful: boolean;
  createdAt: any;
  category: UserExamStatistics_userExamStatistics_examStatistics_category;
}

export interface UserExamStatistics_userExamStatistics {
  __typename: "ExamStatisticsPayload";
  examStatistics: UserExamStatistics_userExamStatistics_examStatistics[];
}

export interface UserExamStatistics {
  userExamStatistics: UserExamStatistics_userExamStatistics;
}

export interface UserExamStatisticsVariables {
  applicationId: string;
}
