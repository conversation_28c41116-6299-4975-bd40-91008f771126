/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: LevelsExperienceLevels
// ====================================================

export interface LevelsExperienceLevels_experienceLevels_experienceLevel {
  __typename: "ExperienceLevelPayload";
  level: number;
}

export interface LevelsExperienceLevels_experienceLevels {
  __typename: "ExperienceLevelsPayload";
  experienceLevel: LevelsExperienceLevels_experienceLevels_experienceLevel[];
}

export interface LevelsExperienceLevels {
  experienceLevels: LevelsExperienceLevels_experienceLevels;
}
