/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: ExperienceLevels
// ====================================================

export interface ExperienceLevels_experienceLevels_experienceLevel {
  __typename: "ExperienceLevelPayload";
  level: number;
}

export interface ExperienceLevels_experienceLevels {
  __typename: "ExperienceLevelsPayload";
  experienceLevel: ExperienceLevels_experienceLevels_experienceLevel[];
}

export interface ExperienceLevels {
  experienceLevels: ExperienceLevels_experienceLevels;
}
