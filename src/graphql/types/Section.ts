/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: Section
// ====================================================

export interface Section_section_category {
  __typename: "CategoryPayload";
  id: number;
  name: string;
  slug: string;
}

export interface Section_section_questionGroup {
  __typename: "QuestionGroupPayload";
  id: number;
  title: string;
}

export interface Section_section {
  __typename: "SectionPayload";
  id: number;
  questionCount: number;
  pointsPerQuestion: number;
  category: Section_section_category;
  questionGroup: Section_section_questionGroup;
  createdAt: any;
}

export interface Section {
  section: Section_section;
}

export interface SectionVariables {
  applicationId: string;
  categoryId: string;
  sectionId: string;
}
