/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: Exam
// ====================================================

export interface Exam_exam_questions_questionGroup {
  __typename: "QuestionGroupPayload";
  title: string;
}

export interface Exam_exam_questions_answers_image {
  __typename: "ImagePayload";
  url: string;
  fileName: string;
}

export interface Exam_exam_questions_answers {
  __typename: "AnswerPayload";
  text: string | null;
  correct: boolean;
  image: Exam_exam_questions_answers_image | null;
  id: number;
}

export interface Exam_exam_questions_image {
  __typename: "ImagePayload";
  url: string;
  fileName: string;
}

export interface Exam_exam_questions_video_thumbnailImage {
  __typename: "ImagePayload";
  url: string;
  fileName: string;
}

export interface Exam_exam_questions_video {
  __typename: "VideoPayload";
  url: string;
  fileName: string;
  thumbnailImage: Exam_exam_questions_video_thumbnailImage;
}

export interface Exam_exam_questions {
  __typename: "QuestionPayload";
  questionGroup: Exam_exam_questions_questionGroup;
  answers: Exam_exam_questions_answers[];
  text: string;
  id: number;
  image: Exam_exam_questions_image | null;
  video: Exam_exam_questions_video | null;
}

export interface Exam_exam {
  __typename: "ExamPayload";
  questions: Exam_exam_questions[];
}

export interface Exam {
  exam: Exam_exam;
}

export interface ExamVariables {
  applicationId: string;
  categoryId: string;
}
