/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: PaymentMethodTypes
// ====================================================

export interface PaymentMethodTypes_paymentMethodTypes_paymentMethodType_paymentMethods {
  __typename: "PaymentMethodPayload";
  id: number;
  name: string;
  imageUrl: string | null;
  position: number;
  instrument: string;
  instrumentSwift: string | null;
}

export interface PaymentMethodTypes_paymentMethodTypes_paymentMethodType {
  __typename: "PaymentMethodTypePayload";
  id: number;
  name: string;
  description: string;
  paymentMethods: PaymentMethodTypes_paymentMethodTypes_paymentMethodType_paymentMethods[];
}

export interface PaymentMethodTypes_paymentMethodTypes {
  __typename: "PaymentMethodTypesPayload";
  paymentMethodType: PaymentMethodTypes_paymentMethodTypes_paymentMethodType[];
}

export interface PaymentMethodTypes {
  paymentMethodTypes: PaymentMethodTypes_paymentMethodTypes;
}
