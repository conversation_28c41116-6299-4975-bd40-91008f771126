/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: Application
// ====================================================

export interface Application_application {
  __typename: "ApplicationPayload";
  id: number;
  name: string;
  slug: string;
  price: number;
  token: string;
  questionsTotalCount: number;
  isPaid: boolean;
  studentsCount: number;
  averageSuccessRate: number;
}

export interface Application {
  application: Application_application;
}

export interface ApplicationVariables {
  slug: string;
}
