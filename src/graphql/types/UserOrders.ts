/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: UserOrders
// ====================================================

export interface UserOrders_userOrders_orders_items {
  __typename: "OrderItemPayload";
  id: string;
  name: string;
  price: number;
}

export interface UserOrders_userOrders_orders {
  __typename: "OrderPayload";
  id: string;
  state: string;
  price: number;
  vat: number;
  paidAt: any | null;
  createdAt: any;
  items: UserOrders_userOrders_orders_items[];
}

export interface UserOrders_userOrders {
  __typename: "OrdersPayload";
  orders: UserOrders_userOrders_orders[];
}

export interface UserOrders {
  userOrders: UserOrders_userOrders;
}
