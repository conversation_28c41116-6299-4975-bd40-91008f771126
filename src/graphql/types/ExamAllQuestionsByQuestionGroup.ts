/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: ExamAllQuestionsByQuestionGroup
// ====================================================

export interface ExamAllQuestionsByQuestionGroup_examAllQuestionsByQuestionGroup_questions_image {
  __typename: "ImagePayload";
  url: string;
  fileName: string;
}

export interface ExamAllQuestionsByQuestionGroup_examAllQuestionsByQuestionGroup_questions_video_thumbnailImage {
  __typename: "ImagePayload";
  url: string;
  fileName: string;
}

export interface ExamAllQuestionsByQuestionGroup_examAllQuestionsByQuestionGroup_questions_video {
  __typename: "VideoPayload";
  url: string;
  fileName: string;
  thumbnailImage: ExamAllQuestionsByQuestionGroup_examAllQuestionsByQuestionGroup_questions_video_thumbnailImage;
}

export interface ExamAllQuestionsByQuestionGroup_examAllQuestionsByQuestionGroup_questions_answers_image {
  __typename: "ImagePayload";
  url: string;
  fileName: string;
}

export interface ExamAllQuestionsByQuestionGroup_examAllQuestionsByQuestionGroup_questions_answers {
  __typename: "AnswerPayload";
  id: number;
  text: string | null;
  image: ExamAllQuestionsByQuestionGroup_examAllQuestionsByQuestionGroup_questions_answers_image | null;
  correct: boolean;
}

export interface ExamAllQuestionsByQuestionGroup_examAllQuestionsByQuestionGroup_questions {
  __typename: "QuestionPayload";
  id: number;
  image: ExamAllQuestionsByQuestionGroup_examAllQuestionsByQuestionGroup_questions_image | null;
  video: ExamAllQuestionsByQuestionGroup_examAllQuestionsByQuestionGroup_questions_video | null;
  text: string;
  answers: ExamAllQuestionsByQuestionGroup_examAllQuestionsByQuestionGroup_questions_answers[];
}

export interface ExamAllQuestionsByQuestionGroup_examAllQuestionsByQuestionGroup {
  __typename: "ExamPayload";
  questions: ExamAllQuestionsByQuestionGroup_examAllQuestionsByQuestionGroup_questions[];
}

export interface ExamAllQuestionsByQuestionGroup {
  examAllQuestionsByQuestionGroup: ExamAllQuestionsByQuestionGroup_examAllQuestionsByQuestionGroup;
}

export interface ExamAllQuestionsByQuestionGroupVariables {
  applicationId: string;
  categoryId: string;
  questionGroupId: string;
}
