/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

import { ExamAnswerInput } from "./globalTypes";

// ====================================================
// GraphQL mutation operation: EvaluateQuestionGroupExam
// ====================================================

export interface EvaluateQuestionGroupExam_evaluateQuestionGroupExam {
  __typename: "EvaluatedQuestionGroupExamPayload";
  maximumPoints: number;
  totalPoints: number;
  percentageSuccess: number;
}

export interface EvaluateQuestionGroupExam {
  evaluateQuestionGroupExam: EvaluateQuestionGroupExam_evaluateQuestionGroupExam;
}

export interface EvaluateQuestionGroupExamVariables {
  applicationId: string;
  categorySlug: string;
  answers: ExamAnswerInput[];
  questionGroupId: number;
  evaluateAllQuestions: boolean;
}
