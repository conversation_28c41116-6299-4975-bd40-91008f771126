/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: Category
// ====================================================

export interface Category_category {
  __typename: "CategoryPayload";
  id: number;
  name: string;
  slug: string;
  title: string;
  priority: number;
  questionsCount: number;
  timeLimitInMinutes: number;
  maximumPoints: number;
  pointsToPass: number;
}

export interface Category {
  category: Category_category;
}

export interface CategoryVariables {
  slug: string;
  applicationId: string;
}
