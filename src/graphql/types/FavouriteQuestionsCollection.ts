/* tslint:disable */
/* eslint-disable */
// @generated
// This file was automatically generated and should not be edited.

// ====================================================
// GraphQL query operation: FavouriteQuestionsCollection
// ====================================================

export interface FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node_image {
  __typename: "ImagePayload";
  url: string;
  fileName: string;
}

export interface FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node_video_thumbnailImage {
  __typename: "ImagePayload";
  url: string;
  fileName: string;
}

export interface FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node_video {
  __typename: "VideoPayload";
  url: string;
  fileName: string;
  thumbnailImage: FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node_video_thumbnailImage;
}

export interface FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node_answers_image {
  __typename: "ImagePayload";
  url: string;
  fileName: string;
}

export interface FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node_answers {
  __typename: "AnswerPayload";
  id: number;
  text: string | null;
  image: FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node_answers_image | null;
  correct: boolean;
}

export interface FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node {
  __typename: "QuestionPayload";
  id: number;
  image: FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node_image | null;
  video: FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node_video | null;
  text: string;
  answers: FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node_answers[];
}

export interface FavouriteQuestionsCollection_favouriteQuestionsCollection_edges {
  __typename: "QuestionEdge";
  cursor: string;
  node: FavouriteQuestionsCollection_favouriteQuestionsCollection_edges_node;
}

export interface FavouriteQuestionsCollection_favouriteQuestionsCollection_pageInfo {
  __typename: "PageInfo";
  /**
   * When paginating forwards, are there more items?
   */
  hasNextPage: boolean;
  /**
   * When paginating forwards, the cursor to continue.
   */
  endCursor: string | null;
  /**
   * When paginating backwards, the cursor to continue.
   */
  startCursor: string | null;
  /**
   * When paginating backwards, are there more items?
   */
  hasPreviousPage: boolean;
}

export interface FavouriteQuestionsCollection_favouriteQuestionsCollection {
  __typename: "QuestionPayloadsPaginator";
  totalCount: number;
  edges: FavouriteQuestionsCollection_favouriteQuestionsCollection_edges[];
  pageInfo: FavouriteQuestionsCollection_favouriteQuestionsCollection_pageInfo;
}

export interface FavouriteQuestionsCollection {
  favouriteQuestionsCollection: FavouriteQuestionsCollection_favouriteQuestionsCollection;
}

export interface FavouriteQuestionsCollectionVariables {
  applicationId: string;
  limit: number;
  offset: number;
}
