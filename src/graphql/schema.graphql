schema {
  query: Query
  mutation: Mutation
  subscription: Subscription
}
scalar DateTime
type Query {
  allQuestionsCollection(applicationId: ID!, limit: Int!, offset: Int!, 
    "Optional category ID to filter questions"
    categoryId: ID,
    "Optional section ID to filter questions"
    sectionId: ID
  ): QuestionPayloadsPaginator!
  experienceLevels(applicationId: ID!): ExperienceLevelsPayload!
  experienceLevel(applicationId: ID!, currentLevel: Int!): ExperienceLevelPayload
  exam(applicationId: ID!, categoryId: ID!): ExamPayload!
  examByQuestionGroup(applicationId: ID!, categoryId: ID!, questionGroupId: ID!): ExamPayload!
  examAllQuestionsByQuestionGroup(applicationId: ID!, categoryId: ID!, questionGroupId: ID!): ExamPayload!
  loggedUser: UserPayload!
  sections(applicationId: ID!, categoryId: ID!): SectionsPayload!
  section(applicationId: ID!, categoryId: ID!, sectionId: ID!): SectionPayload!
  userExamStatistics(applicationId: ID!): ExamStatisticsPayload!
  applications: ApplicationsPayload!
  application(slug: String!): ApplicationPayload!
  userOrders: OrdersPayload!
  categories(applicationId: ID!): CategoriesPayload!
  category(slug: String!, applicationId: ID!): CategoryPayload!
  favouriteQuestions(applicationId: ID!): FavouriteQuestionsPayload!
  favouriteQuestionsCollection(applicationId: ID!, limit: Int!, offset: Int!): QuestionPayloadsPaginator!
  favouriteCategories(applicationId: ID!): FavouriteCategoriesPayload!
  activeForgottenPassword(forgottenPasswordToken: ID!): ForgottenPasswordPayload!
  paymentMethodTypes: PaymentMethodTypesPayload!
  checkPaymentStatus(paymentId: String!): PaymentStatusPayload!
}
type QuestionPayloadsPaginator {
  edges: [QuestionEdge!]!
  totalCount: Int!
  pageInfo: PageInfo!
}
type QuestionEdge {
  node: QuestionPayload!
  cursor: String!
}
type QuestionPayload {
  id: Int!
  questionGroup: QuestionGroupPayload!
  answers: [AnswerPayload!]!
  codeMdcr: String
  image: ImagePayload
  video: VideoPayload
  text: String!
}
type QuestionGroupPayload {
  id: Int!
  title: String!
  description: String
  createdAt: DateTime!
}
type AnswerPayload {
  id: Int!
  text: String
  image: ImagePayload
  correct: Boolean!
}
type ImagePayload {
  id: ID!
  fileName: String!
  filePath: String!
  url: String!
  width: Int!
  height: Int!
  extension: String!
  mimetype: String!
  etag: String!
  createdAt: DateTime!
}
type VideoPayload {
  id: ID!
  fileName: String!
  filePath: String!
  url: String!
  extension: String!
  mimetype: String!
  etag: String!
  createdAt: DateTime!
  thumbnailImage: ImagePayload!
}
"Information about pagination in a connection."
type PageInfo {
  "When paginating forwards, are there more items?"
  hasNextPage: Boolean!
  "When paginating backwards, are there more items?"
  hasPreviousPage: Boolean!
  "When paginating backwards, the cursor to continue."
  startCursor: String
  "When paginating forwards, the cursor to continue."
  endCursor: String
}
type ExperienceLevelsPayload {
  experienceLevel: [ExperienceLevelPayload!]!
}
type ExperienceLevelPayload {
  id: Int!
  level: Int!
  name: String!
  experienceFrom: Int!
  experienceTo: Int!
  image: ImagePayload
}
type ExamPayload {
  questions: [QuestionPayload!]!
}
type UserPayload {
  email: String!
  isVerified: Boolean!
}
type SectionsPayload {
  section: [SectionPayload!]!
}
type SectionPayload {
  id: Int!
  questionCount: Int!
  pointsPerQuestion: Int!
  category: CategoryPayload!
  questionGroup: QuestionGroupPayload!
  createdAt: DateTime!
  questionGroupId: Int!
}
type CategoryPayload {
  id: Int!
  name: String!
  slug: String!
  title: String!
  priority: Int!
  questionsCount: Int!
  timeLimitInMinutes: Int!
  maximumPoints: Int!
  pointsToPass: Int!
  image: ImagePayload
  isFavourite: Boolean!
}
type ExamStatisticsPayload {
  examStatistics: [ExamStatisticPayload!]!
}
type ExamStatisticPayload {
  id: ID!
  totalPoints: Int!
  maximumPoints: Int!
  percentageSuccess: Int!
  successful: Boolean!
  createdAt: DateTime!
  category: CategoryPayload!
}
type ApplicationsPayload {
  application: [ApplicationPayload!]!
}
type ApplicationPayload {
  id: Int!
  name: String!
  price: Int!
  slug: String!
  token: String!
  questionsTotalCount: Int!
  isPaid: Boolean!
  studentsCount: Int!
  averageSuccessRate: Int!
}
type OrdersPayload {
  orders: [OrderPayload!]!
}
type OrderPayload {
  id: ID!
  state: String!
  price: Int!
  vat: Int!
  paidAt: DateTime
  createdAt: DateTime!
  canceledAt: DateTime
  items: [OrderItemPayload!]!
}
type OrderItemPayload {
  id: ID!
  name: String!
  price: Int!
}
type CategoriesPayload {
  category: [CategoryPayload!]!
}
type FavouriteQuestionsPayload {
  questions: [QuestionPayload!]!
}
type FavouriteCategoriesPayload {
  categories: [CategoryPayload!]!
}
type ForgottenPasswordPayload {
  id: String!
  user: UserPayload!
  validTo: DateTime!
}
type PaymentMethodTypesPayload {
  paymentMethodType: [PaymentMethodTypePayload!]!
}
type PaymentMethodTypePayload {
  id: Int!
  name: String!
  description: String!
  paymentMethods: [PaymentMethodPayload!]!
}
type PaymentMethodPayload {
  id: Int!
  name: String!
  imageUrl: String
  position: Int!
  instrument: String!
  instrumentSwift: String
}
type PaymentStatusPayload {
  status: String!
  message: String!
}
type Mutation {
  forgottenPassword(email: String!): String!
  changeForgottenPassword(forgottenPasswordToken: ID!, password: String!): Boolean!
  createOrder(paymentData: PaymentDataInput!): PaymentPayload!
  login(username: String!, password: String!): String!
  loginGoogleEmail(loginHash: String!): String!
  evaluateExam(applicationId: ID!, categorySlug: String!, answers: [ExamAnswerInput!]!): EvaluatedExamPayload!
  evaluateQuestionGroupExam(applicationId: ID!, categorySlug: String!, questionGroupId: Int!, answers: [ExamAnswerInput!]!, evaluateAllQuestions: Boolean!): EvaluatedQuestionGroupExamPayload!
  register(email: String!, password: String!): String!
  test: String!
  registerDeviceToken(token: String!): String!
  addFavouriteQuestion(applicationId: ID!, questionId: ID!): Boolean!
  removeFavouriteQuestion(applicationId: ID!, questionId: ID!): Boolean!
  addFavouriteCategory(applicationId: ID!, categoryId: ID!): Boolean!
  removeFavouriteCategory(applicationId: ID!, categoryId: ID!): Boolean!
}
type PaymentPayload {
  gatewayUrl: String!
  embedJs: String!
  status: String!
}
type EvaluatedExamPayload {
  isSuccessful: Boolean!
  totalPoints: Int!
  maximumPoints: Int!
  pointsToPass: Int!
  percentageSuccess: Int!
  sections: [SectionResultPayload!]!
}
type SectionResultPayload {
  sectionId: Int!
  title: String!
  totalPoints: Int!
  maximumPoints: Int!
}
type EvaluatedQuestionGroupExamPayload {
  totalPoints: Int!
  maximumPoints: Int!
  percentageSuccess: Int!
}
type Subscription {
  something: String!
}
input PaymentDataInput {
  paymentMethodId: Int!
  items: [String!]!
}
input ExamAnswerInput {
  questionId: Int!
  answerId: Int!
}

