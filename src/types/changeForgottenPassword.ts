import { ActiveForgottenPassword_activeForgottenPassword } from "@/graphql/types/ActiveForgottenPassword";

export interface ChangeForgottenPasswordFormData {
  password: string;
}

export interface ActionResponse {
  success: boolean;
  message: string;
  errors?: {
    [K in keyof ChangeForgottenPasswordFormData]?: string[];
  };
  inputs?: ChangeForgottenPasswordFormData;
}

export interface ActionActiveForgottenPasswordResponse {
  success: boolean;
  message: string;
  data?: ActiveForgottenPassword_activeForgottenPassword;
}
