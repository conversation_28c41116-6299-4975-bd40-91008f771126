"use client";

import { useState, useCallback, useEffect } from "react";
import { useQuery } from "@apollo/client";
import { GET_ALL_QUESTIONS_COLLECTION } from "@/lib/services/query";
import { PAGE_LIMIT } from "@/constants/pagination";
import {
  AllQuestionsCollection,
  AllQuestionsCollectionVariables,
  AllQuestionsCollection_allQuestionsCollection_edges_node as Question,
  AllQuestionsCollection_allQuestionsCollection_pageInfo,
} from "@/graphql/types/AllQuestionsCollection";
import { Application_application } from "@/graphql/types/Application";
import { Category_category } from "@/graphql/types/Category";

interface UseGetQuestionsProps {
  applicationData: Application_application;
  categoryData?: Category_category;
  sectionId?: string;
  initialData?: AllQuestionsCollection;
}

export const useGetQuestions = ({
  applicationData,
  categoryData,
  sectionId,
  initialData,
}: UseGetQuestionsProps) => {
  const [questions, setQuestions] = useState<Question[]>(
    initialData?.allQuestionsCollection?.edges?.map((edge) => edge.node) || [],
  );
  const [pageInfo, setPageInfo] =
    useState<AllQuestionsCollection_allQuestionsCollection_pageInfo>(
      initialData?.allQuestionsCollection?.pageInfo || {
        __typename: "PageInfo",
        hasNextPage: false,
        hasPreviousPage: false,
        startCursor: null,
        endCursor: null,
      },
    );
  const [totalCount, setTotalCount] = useState(
    initialData?.allQuestionsCollection?.totalCount || 0,
  );
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const totalPages = Math.ceil(totalCount / PAGE_LIMIT);

  const variables = {
    applicationId: applicationData.id.toString(),
    categoryId: categoryData?.id?.toString(),
    sectionId: sectionId,
    limit: PAGE_LIMIT,
    offset: 0,
  };

  const { fetchMore } = useQuery<
    AllQuestionsCollection,
    AllQuestionsCollectionVariables
  >(GET_ALL_QUESTIONS_COLLECTION, {
    variables,
    skip: true,
  });

  const fetchQuestions = useCallback(
    async (page: number) => {
      if (page < 1 || page > totalPages) return;

      setLoading(true);
      const offset = (page - 1) * PAGE_LIMIT;

      try {
        const fetchVariables = {
          applicationId: applicationData.id.toString(),
          categoryId: categoryData?.id?.toString(),
          sectionId: sectionId,
          limit: PAGE_LIMIT,
          offset,
        };

        const result = await fetchMore({
          variables: fetchVariables,
        });

        if (result.data) {
          const newQuestions = result.data.allQuestionsCollection.edges.map(
            (edge) => edge.node,
          );

          setQuestions(newQuestions);
          setPageInfo(result.data.allQuestionsCollection.pageInfo);
          setTotalCount(result.data.allQuestionsCollection.totalCount);
          setCurrentPage(page);
        }
      } catch (err) {
        setError(
          err instanceof Error ? err : new Error("Chyba při načítání otázek"),
        );
      } finally {
        setLoading(false);
      }
    },
    [applicationData.id, categoryData?.id, sectionId, totalPages, fetchMore],
  );

  useEffect(() => {
    if (!initialData) {
      fetchQuestions(1);
    }
  }, [
    applicationData.id,
    categoryData?.id,
    sectionId,
    initialData,
    fetchQuestions,
  ]);

  const goToPage = (page: number) => {
    if (page < 1 || page > totalPages || page === currentPage) return;
    fetchQuestions(page);
    window.scrollTo(0, 0);
  };

  return {
    questions,
    loading,
    error,
    pageInfo,
    totalCount,
    currentPage,
    totalPages,
    goToPage,
  };
};
