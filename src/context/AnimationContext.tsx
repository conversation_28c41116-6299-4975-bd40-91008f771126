"use client";

import React, { createContext, useContext, useState } from "react";

interface AnimationContextType {
  currentWordIndex: number;
  setCurrentWordIndex: React.Dispatch<React.SetStateAction<number>>;
}

const AnimationContext = createContext<AnimationContextType | undefined>(
  undefined,
);

export const AnimationProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [currentWordIndex, setCurrentWordIndex] = useState(0);

  return (
    <AnimationContext.Provider
      value={{ currentWordIndex, setCurrentWordIndex }}
    >
      {children}
    </AnimationContext.Provider>
  );
};

export const useAnimation = () => {
  const context = useContext(AnimationContext);
  if (context === undefined) {
    throw new Error("useAnimation must be used within an AnimationProvider");
  }
  return context;
};
