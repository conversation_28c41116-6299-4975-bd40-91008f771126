"use client";

import React, { createContext, useState, useContext, useEffect } from "react";
import { Applications_applications_application } from "@/graphql/types/Applications";

export type CartItem = {
  id: string;
  name: string;
  price: number;
};

export type PaymentMethod = {
  id: number;
};

type CartContextType = {
  items: CartItem[];
  paymentMethod: PaymentMethod;
  addItem: (item: CartItem) => void;
  removeItem: (id: string) => void;
  updatePaymentMethod: (id: number) => void;
  clearCart: () => void;
  totalPrice: number;
  updateApplications: (
    applications: Applications_applications_application[],
  ) => void;
};

const CartContext = createContext<CartContextType | undefined>(undefined);

export const CartProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [items, setItems] = useState<CartItem[]>([]);
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>({ id: 1 });
  const [totalPrice, setTotalPrice] = useState(0);
  const [applications, setApplications] = useState<
    Applications_applications_application[]
  >([]);

  useEffect(() => {
    // Načtení košíku z localStorage při inicializaci
    const savedCart = localStorage.getItem("cart");

    if (savedCart) setItems(JSON.parse(savedCart));
  }, []);

  useEffect(() => {
    // Aktualizace celkové ceny
    const newTotal = items.reduce((total, item) => total + item.price, 0);
    setTotalPrice(newTotal);

    // Uložení košíku do localStorage
    localStorage.setItem("cart", JSON.stringify(items));
  }, [items]);

  // Kontrola a aktualizace položek podle aktuálních aplikací
  useEffect(() => {
    if (applications.length > 0 && items.length > 0) {
      // Odfiltrování položek bez odpovídajícího tokenu v aplikacích
      const validItems = items.filter((item) =>
        applications.some((app) => app.token === item.id),
      );

      // Kontrola a aktualizace ceny i názvu
      const updatedItems = validItems.map((item) => {
        const matchingApp = applications.find((app) => app.token === item.id);
        if (matchingApp) {
          // Aktualizace pokud se liší cena nebo název
          if (
            matchingApp.price !== item.price ||
            matchingApp.name !== item.name
          ) {
            return {
              ...item,
              name: matchingApp.name,
              price: matchingApp.price,
            };
          }
        }
        return item;
      });

      // Pokud došlo ke změně (rozdílný počet položek nebo rozdíl v datech), aktualizovat košík
      if (JSON.stringify(updatedItems) !== JSON.stringify(items)) {
        setItems(updatedItems);
      }
    }
  }, [applications, items]);

  const addItem = (item: CartItem) => {
    if (!items.some((i) => i.id === item.id)) {
      setItems([...items, item]);
    }
  };

  const removeItem = (id: string) => {
    setItems(items.filter((item) => item.id !== id));
  };

  const updatePaymentMethod = (id: number) => {
    setPaymentMethod({ id: id });
  };

  const clearCart = () => {
    setItems([]);
    localStorage.removeItem("cart");
  };

  const updateApplications = (
    newApplications: Applications_applications_application[],
  ) => {
    setApplications(newApplications);
  };

  return (
    <CartContext.Provider
      value={{
        items,
        paymentMethod,
        addItem,
        removeItem,
        updatePaymentMethod,
        clearCart,
        totalPrice,
        updateApplications,
      }}
    >
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error("useCart musí být použit uvnitř CartProvider");
  }
  return context;
};
