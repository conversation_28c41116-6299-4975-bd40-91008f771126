import React from "react";
import { PartyPopper } from "lucide-react";
import { Application_application } from "@/graphql/types/Application";

interface ThankYouBannerProps {
  application: Application_application;
}

export const ThankYouBanner: React.FC<ThankYouBannerProps> = ({
  application,
}) => {
  if (!application.isPaid) return null;

  return (
    <div className="relative mb-8">
      <div className="relative overflow-hidden bg-white border border-emerald-100 rounded-2xl p-6 shadow-sm">
        {/* Jemný dekorativní prvek */}
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-50 to-blue-50 opacity-40" />

        <div className="relative flex items-center gap-4">
          <div className="p-3 bg-emerald-50 rounded-xl">
            <PartyPopper className="w-6 h-6 text-emerald-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold mb-1 text-gray-900">
              Děkujeme za vaši důvěru!
            </h2>
            <p className="text-gray-600">
              Máte přístup ke kompletní přípravě na zkouškou. Přejeme hodně
              úspěchů při studiu!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
