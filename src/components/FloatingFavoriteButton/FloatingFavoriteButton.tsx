"use client";

import { Heart } from "lucide-react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { useState } from "react";
import { useSession } from "next-auth/react";
import { LeaveTestModal } from "../LeaveTestModal/LeaveTestModal";
import { RegisterPromptModal } from "../RegisterPromptModal/RegisterPromptModal";

interface Props {
  applicationSlug: string;
  count?: number;
}

export const FloatingFavoriteButton = ({ applicationSlug, count }: Props) => {
  const pathname = usePathname();
  const [isLeaveTestModalOpen, setIsLeaveTestModalOpen] = useState(false);
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);
  const { status } = useSession();

  // Skryjeme tlačítko na těchto stránkách
  if (
    pathname?.includes("/oblibene-otazky") ||
    pathname?.includes("/vsechny-otazky")
  ) {
    return null;
  }

  // Kontrola, zda je uživatel v testu, cvičení nebo všech otázkách
  const isInTest =
    pathname?.includes("/kategorie/zkouska/") ||
    (pathname?.includes("/kategorie/") && pathname?.includes("/cviceni/")) ||
    (pathname?.includes("/kategorie/") &&
      pathname?.includes("/vsechny-otazky/"));

  const handleClick = (e: React.MouseEvent) => {
    if (status !== "authenticated") {
      e.preventDefault();
      setIsRegisterModalOpen(true);
      return;
    }

    if (isInTest) {
      e.preventDefault();
      setIsLeaveTestModalOpen(true);
    }
  };

  const buttonContent = (
    <>
      <Heart
        className={`w-5 h-5 ${
          status === "authenticated"
            ? "text-rose-500 fill-rose-500"
            : "text-gray-400"
        }`}
      />
      {status === "authenticated" && count !== undefined && (
        <span className="font-semibold text-gray-700">{count}</span>
      )}
      <span className="hidden sm:inline text-gray-700">Oblíbené otázky</span>
    </>
  );

  return (
    <>
      <Link
        href={
          status === "authenticated"
            ? `/${applicationSlug}/oblibene-otazky`
            : "#"
        }
        onClick={handleClick}
        className="fixed bottom-4 right-4 z-40 flex items-center gap-2 bg-white px-4 py-3 rounded-full shadow-lg border border-gray-200 hover:shadow-xl transition-shadow"
      >
        {buttonContent}
      </Link>

      <LeaveTestModal
        isOpen={isLeaveTestModalOpen}
        onClose={() => setIsLeaveTestModalOpen(false)}
        href={`/${applicationSlug}/oblibene-otazky`}
      />

      <RegisterPromptModal
        isOpen={isRegisterModalOpen}
        onClose={() => setIsRegisterModalOpen(false)}
      />
    </>
  );
};
