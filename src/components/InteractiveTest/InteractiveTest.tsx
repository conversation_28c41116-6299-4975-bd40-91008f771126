"use client";

import React, { useState } from "react";
import { CheckCircle, XCircle, ArrowRight } from "lucide-react";

interface AnswerProps {
  letter: string;
  text: string;
  isSelected?: boolean;
  isCorrect?: boolean;
  isIncorrect?: boolean;
  onClick?: () => void;
}

// Pomocná komponenta pro odpověď
const Answer: React.FC<AnswerProps> = ({
  letter,
  text,
  isSelected = false,
  isCorrect = false,
  isIncorrect = false,
  onClick = () => {},
}) => {
  // Určení stylů pro odpověď
  let styles = {
    letter: "bg-gray-100 text-gray-700 border-gray-200",
    text: "text-gray-700",
    container: "border-gray-200 bg-white",
  };

  if (isCorrect) {
    styles = {
      letter: "bg-green-600 text-white border-green-600",
      text: "text-green-700",
      container: "border-green-200 bg-green-50",
    };
  } else if (isIncorrect) {
    styles = {
      letter: "bg-red-600 text-white border-red-600",
      text: "text-red-700",
      container: "border-red-200 bg-red-50",
    };
  }

  // Přidání modrého okraje pro vybrané odpovědi
  if (isSelected) {
    styles.container += " ring-2 ring-blue-500";
  }

  return (
    <div
      className={`
        flex items-stretch overflow-hidden
        border rounded-xl
        transition-all duration-200
        ${styles.container}
        cursor-pointer hover:shadow-md
      `}
      onClick={onClick}
    >
      <div
        className={`
          flex items-center justify-center
          w-8 sm:w-10 md:w-12
          border-r
          transition-colors duration-200
          ${styles.letter}
        `}
      >
        <span className="font-semibold text-base sm:text-lg">{letter}</span>
      </div>

      <div className="flex-1 p-2 sm:p-3 text-left">
        <span
          className={`
            block text-[13px] sm:text-[15px]
            leading-tight sm:leading-relaxed
            ${styles.text}
          `}
        >
          {text}
        </span>
      </div>
    </div>
  );
};

interface FeedbackProps {
  isCorrect: boolean;
  message: string;
}

// Komponenta pro zpětnou vazbu
const Feedback: React.FC<FeedbackProps> = ({ isCorrect, message }) => {
  return (
    <div
      className={`mt-3 sm:mt-4 p-2 sm:p-3 rounded-lg border ${
        isCorrect ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"
      } animate-fadeIn`}
    >
      <div className="flex items-center mb-1">
        {isCorrect ? (
          <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-600 mr-1.5 sm:mr-2" />
        ) : (
          <XCircle className="w-4 h-4 sm:w-5 sm:h-5 text-red-600 mr-1.5 sm:mr-2" />
        )}
        <div
          className={`text-xs sm:text-sm font-medium ${
            isCorrect ? "text-green-700" : "text-red-700"
          }`}
        >
          {isCorrect ? "Správně!" : "Špatně!"}
        </div>
      </div>
      <div
        className={`text-xs sm:text-sm ${isCorrect ? "text-green-700" : "text-red-700"}`}
      >
        {message}
      </div>
    </div>
  );
};

interface QuestionAnswer {
  letter: string;
  text: string;
  isCorrect?: boolean;
}

interface Question {
  text: string;
  answers: QuestionAnswer[];
  feedback: string;
}

export function InteractiveTest() {
  // Stav pro sledování interakce s testem
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [showFeedback, setShowFeedback] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  // Testovací otázky
  const questions: Question[] = [
    {
      text: "Jaká je maximální povolená rychlost v obci?",
      answers: [
        { letter: "A", text: "50 km/h", isCorrect: true },
        { letter: "B", text: "60 km/h" },
        { letter: "C", text: "70 km/h" },
      ],
      feedback: "Maximální povolená rychlost v obci je 50 km/h.",
    },
    {
      text: "Jaká je maximální povolená rychlost mimo obec?",
      answers: [
        { letter: "A", text: "70 km/h" },
        { letter: "B", text: "90 km/h", isCorrect: true },
        { letter: "C", text: "110 km/h" },
      ],
      feedback: "Maximální povolená rychlost mimo obec je 90 km/h.",
    },
  ];

  // Funkce pro výběr odpovědi
  const handleAnswerClick = (index: number) => {
    if (showFeedback) return; // Zabránit výběru po zobrazení zpětné vazby

    setSelectedAnswer(index);
    setShowFeedback(true);
  };

  // Funkce pro přechod na další otázku
  const handleNextQuestion = () => {
    setIsAnimating(true);

    setTimeout(() => {
      setSelectedAnswer(null);
      setShowFeedback(false);
      setCurrentQuestion((prev) => (prev + 1) % questions.length);
      setIsAnimating(false);
    }, 500);
  };

  // Odstraněna automatická animace - student si musí sám kliknout

  const currentQ = questions[currentQuestion];
  const isCorrectAnswer =
    selectedAnswer !== null &&
    currentQ.answers[selectedAnswer].isCorrect === true;

  return (
    <div className="relative rounded-2xl overflow-hidden bg-white/80 backdrop-blur-sm border border-emerald-100 p-3 sm:p-5 shadow-xl hover:shadow-2xl transition-all duration-300 mx-auto max-w-md sm:max-w-none">
      <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-emerald-500 to-blue-500"></div>

      <div className="mb-3 sm:mb-4">
        <h2 className="text-lg sm:text-xl font-bold text-gray-900">
          Vyzkoušejte interaktivní test
        </h2>
        <p className="text-xs sm:text-sm text-gray-600">
          Okamžitá zpětná vazba a vysvětlení
        </p>
      </div>

      <div
        className={`transition-opacity duration-500 ${isAnimating ? "opacity-0" : "opacity-100"}`}
      >
        {/* Otázka */}
        <div className="bg-white rounded-lg shadow-sm p-3 sm:p-4 mb-3 sm:mb-4">
          <div className="flex justify-between items-center mb-2 sm:mb-3">
            <div className="text-xs sm:text-sm text-gray-500">
              Otázka {currentQuestion + 1}/{questions.length}
            </div>
          </div>

          <div className="mb-3 sm:mb-4">
            <p className="text-sm sm:text-base">{currentQ.text}</p>
          </div>

          {/* Odpovědi */}
          <div className="space-y-1.5 sm:space-y-2">
            {currentQ.answers.map((answer, index) => (
              <Answer
                key={index}
                letter={answer.letter}
                text={answer.text}
                isSelected={selectedAnswer === index}
                isCorrect={showFeedback && answer.isCorrect}
                isIncorrect={
                  showFeedback && selectedAnswer === index && !answer.isCorrect
                }
                onClick={() => handleAnswerClick(index)}
              />
            ))}
          </div>

          {/* Zpětná vazba */}
          {showFeedback && (
            <Feedback isCorrect={isCorrectAnswer} message={currentQ.feedback} />
          )}
        </div>

        {/* Tlačítko pro další otázku */}
        {showFeedback && (
          <div className="flex justify-end">
            <button
              onClick={handleNextQuestion}
              className="inline-flex items-center px-3 py-1.5 sm:px-4 sm:py-2 bg-emerald-600 text-white rounded-lg text-xs sm:text-sm font-medium hover:bg-emerald-700 transition-colors"
            >
              Další otázka
              <ArrowRight className="ml-1.5 sm:ml-2 w-3 h-3 sm:w-4 sm:h-4" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

export default InteractiveTest;
