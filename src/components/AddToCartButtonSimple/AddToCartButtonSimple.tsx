"use client";

import React, { useState, useCallback } from "react";
import { useCart } from "@/context/CartContext";
import { Application_application } from "@/graphql/types/Application";
import { ShoppingCart, Check, LogIn } from "lucide-react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";

type AddToCartButtonSimpleProps = {
  application: Application_application;
  onSuccess?: () => void;
  className?: string;
};

export default function AddToCartButtonSimple({
  application,
  onSuccess,
  className = "group/button flex w-full items-center justify-center gap-3 px-6 py-2.5 bg-gradient-to-br from-emerald-600 to-emerald-700 text-white font-medium rounded-xl hover:from-emerald-500 hover:to-emerald-600 transition-all duration-200 shadow-sm hover:shadow-md",
}: AddToCartButtonSimpleProps) {
  const { addItem } = useCart();
  const [isAdded, setIsAdded] = useState(false);
  const { status } = useSession();
  const router = useRouter();

  const handleAddToCart = useCallback(() => {
    // Pokud uživatel není přihlášen, přesměrujeme ho na přihlášení
    if (status !== "authenticated") {
      router.push("/prihlaseni");
      return;
    }

    // Přidáme položku do košíku
    addItem({
      id: application.token,
      name: application.name,
      price: application.price,
    });

    // Nastavíme příznak, že položka byla přidána
    setIsAdded(true);

    // Pokud je definována callback funkce onSuccess, zavoláme ji
    if (onSuccess) {
      onSuccess();
    }

    // Přesměrujeme do košíku
    router.push("/kosik");
  }, [application, addItem, status, router, onSuccess]);

  // Pokud je aplikace již zakoupena, zobrazíme jiné tlačítko
  if (application.isPaid) {
    return (
      <button disabled className={className + " opacity-50 cursor-not-allowed"}>
        <Check className="w-5 h-5 mr-2" />
        <span>Již zakoupeno</span>
      </button>
    );
  }

  // Zobrazení tlačítka podle stavu přihlášení
  return (
    <button onClick={handleAddToCart} disabled={isAdded} className={className}>
      {isAdded ? (
        // Stav po přidání do košíku
        <>
          <Check className="w-5 h-5" />
          <span className="group-hover/button:translate-x-0.5 transition-transform duration-150">
            Přidáno do košíku
          </span>
        </>
      ) : status !== "authenticated" ? (
        // Stav pro nepřihlášeného uživatele
        <>
          <LogIn className="w-5 h-5" />
          <span className="group-hover/button:translate-x-0.5 transition-transform duration-150">
            Přihlásit se
          </span>
        </>
      ) : (
        // Stav pro přihlášeného uživatele
        <>
          <ShoppingCart className="w-5 h-5" />
          <span className="group-hover/button:translate-x-0.5 transition-transform duration-150">
            Přidat do košíku
          </span>
        </>
      )}
    </button>
  );
}
