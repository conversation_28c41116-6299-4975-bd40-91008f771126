import { FunctionComponent } from "react";
import Image from "next/image";
import { Application_application } from "@/graphql/types/Application";
import { Apple, Brain, Sparkles, Smartphone } from "lucide-react";
import { APP_STORE_LINKS } from "@/constants/appStoreLinks";

interface Props {
  application: Application_application;
}

const Elevated: FunctionComponent<Props> = ({ application }) => {
  const appLinks =
    APP_STORE_LINKS[application.slug as keyof typeof APP_STORE_LINKS];

  // Pokud aplikace nemá odkazy na mobilní aplikace, nezobrazíme CTA sekci
  if (!appLinks?.android && !appLinks?.ios) {
    return null;
  }

  return (
    <div className="relative py-16 mt-16">
      {/* Gradient Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-emerald-50 via-blue-50 to-emerald-50 rounded-3xl" />

      <div className="relative m-auto px-6 xl:container md:px-12 lg:px-20">
        <div className="mb-16">
          <h2 className="text-center space-y-4">
            <span className="block text-4xl md:text-5xl font-bold text-gray-900">
              Mobilní Aplikace
            </span>
          </h2>
          <p className="mt-6 text-lg text-center text-emerald-600 max-w-2xl mx-auto leading-relaxed">
            Stáhněte si naši mobilní aplikaci a mějte přístup ke všem materiálům
            i bez připojení k internetu.
          </p>
        </div>

        <div className="mt-8 grid gap-8 md:grid-cols-2 lg:grid-cols-3 md:grid-rows-1 auto-rows-fr">
          {/* Mobilní Aplikace */}
          <div className="relative h-full">
            <div className="absolute inset-0 bg-white rounded-3xl transform transition-all duration-200 group-hover:scale-[1.02]" />
            <div className="relative h-full rounded-3xl p-8 py-12 border-2 border-emerald-200 bg-white shadow-sm">
              <div className="flex flex-col items-center justify-center h-full space-y-8">
                <div className="relative mx-auto w-16 h-16">
                  <Image
                    src={`/${application.slug}/icon/ios/AppIcons/<EMAIL>`}
                    className="rounded-xl shadow-lg shadow-emerald-200/50 transition-transform duration-200 transform group-hover:scale-110"
                    width="64"
                    height="64"
                    alt="aplikace"
                  />
                  <div className="absolute -right-2 -bottom-2 bg-emerald-500 rounded-full p-1">
                    <Smartphone className="w-4 h-4 text-white" />
                  </div>
                </div>
                <div className="space-y-4 text-center">
                  <h3 className="text-2xl font-semibold text-gray-900">
                    Mobilní aplikace
                  </h3>
                  <p className="text-gray-600">
                    Stáhněte si naši mobilní aplikaci a mějte přístup ke všem
                    materiálům i bez připojení k internetu.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Efektivní Učení */}
          <div className="relative h-full">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-100/50 to-blue-50/50 rounded-3xl transform transition-all duration-200 group-hover:scale-[1.02]" />
            <div className="relative h-full rounded-3xl p-8 py-12 border border-blue-200">
              <div className="flex flex-col items-center justify-center h-full space-y-8">
                <div className="relative mx-auto w-16 h-16 flex items-center justify-center">
                  <div className="absolute inset-0 bg-blue-100 rounded-xl rotate-6" />
                  <div className="absolute inset-0 bg-blue-200/50 rounded-xl -rotate-6" />
                  <Brain className="relative w-8 h-8 text-blue-600" />
                </div>
                <div className="space-y-4 text-center">
                  <h3 className="text-2xl font-semibold text-gray-900">
                    Chytré opakování
                  </h3>
                  <p className="text-gray-600">
                    Adaptivní systém učení přizpůsobený vašim potřebám
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Offline Přístup */}
          <div className="relative h-full">
            <div className="absolute inset-0 bg-white rounded-3xl transform"></div>
            <div className="relative h-full rounded-3xl p-4 sm:p-8 sm:py-12 border-2 border-emerald-200 bg-white shadow-sm">
              <div className="flex flex-col items-center justify-center h-full space-y-6">
                <div className="relative w-16 h-16 flex items-center justify-center">
                  <div className="absolute inset-0 bg-emerald-100 rounded-xl rotate-6" />
                  <div className="absolute inset-0 bg-emerald-200/50 rounded-xl -rotate-6" />
                  <Sparkles className="relative w-8 h-8 text-emerald-600" />
                </div>
                <div className="space-y-3 text-center">
                  <h3 className="text-xl sm:text-2xl font-semibold text-gray-900">
                    Offline režim
                  </h3>
                  <p className="text-sm sm:text-base text-gray-600 max-w-prose">
                    Stáhněte si otázky do svého zařízení a procvičujte i bez
                    připojení k internetu.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section with enhanced visibility */}
        <div className="mt-8 relative">
          {/* Subtle animated gradient background */}
          <div className="absolute inset-0 bg-gradient-to-r from-emerald-100/50 via-blue-100/30 to-emerald-100/50 rounded-2xl blur-xl animate-pulse" />

          {/* Content container with padding and backdrop blur */}
          <div className="relative backdrop-blur-sm rounded-2xl p-0 sm:p-8">
            {/* Buttons container */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-2 sm:gap-4 w-full px-0 sm:px-8">
              {appLinks.android && (
                <a
                  href={appLinks.android}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-center w-full sm:w-auto px-4 sm:px-8 py-3 sm:py-4 
                    text-white bg-emerald-600 hover:bg-emerald-700 
                    rounded-xl 
                    transition-all duration-300 
                    shadow-xl hover:shadow-2xl hover:-translate-y-1
                    text-base font-semibold
                    min-w-[200px]
                    whitespace-nowrap"
                >
                  <Smartphone className="w-5 h-5 mr-2 sm:mr-3" />
                  <span>Stáhnout pro Android</span>
                </a>
              )}

              {appLinks.ios && (
                <a
                  href={appLinks.ios}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-center w-full sm:w-auto px-4 sm:px-8 py-3 sm:py-4
                    text-emerald-600 bg-white 
                    border-2 border-emerald-600 
                    rounded-xl hover:bg-emerald-50 
                    transition-all duration-300
                    shadow-lg hover:shadow-xl hover:-translate-y-1
                    text-base font-semibold
                    group
                    min-w-[200px]
                    whitespace-nowrap"
                >
                  <Apple className="w-5 h-5 mr-2 sm:mr-3 group-hover:scale-110 transition-transform duration-300" />
                  <span>Stáhnout pro iOS</span>
                </a>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Elevated;
