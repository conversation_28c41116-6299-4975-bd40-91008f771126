import { CheckCircle, AlertCircle, AlertTriangle, Info } from "lucide-react";

export const SuccessIcon = () => (
  <div className="w-8 h-8 rounded-lg flex items-center justify-center">
    <CheckCircle className="w-5 h-5 text-emerald-500 stroke-[2]" />
  </div>
);

export const ErrorIcon = () => (
  <div className="w-8 h-8 rounded-lg flex items-center justify-center">
    <AlertCircle className="w-5 h-5 text-red-500 stroke-[2]" />
  </div>
);

export const WarningIcon = () => (
  <div className="w-8 h-8 rounded-lg flex items-center justify-center">
    <AlertTriangle className="w-5 h-5 text-amber-500 stroke-[2]" />
  </div>
);

export const InfoIcon = () => (
  <div className="w-8 h-8 rounded-lg flex items-center justify-center">
    <Info className="w-5 h-5 text-blue-500 stroke-[2]" />
  </div>
);
