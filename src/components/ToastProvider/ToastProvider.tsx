"use client";

import { ToastContainer } from "react-toastify";
import { SuccessIcon, ErrorIcon, WarningIcon } from "./icons";
import "react-toastify/dist/ReactToastify.css";

export const ToastProvider = () => {
  const getIcon = ({ type }: { type: string }) => {
    switch (type) {
      case "success":
        return <SuccessIcon />;
      case "error":
        return <ErrorIcon />;
      case "warning":
        return <WarningIcon />;
      default:
        return <SuccessIcon />;
    }
  };

  return (
    <ToastContainer
      position="top-right"
      autoClose={4000}
      hideProgressBar={false}
      newestOnTop
      closeOnClick
      rtl={false}
      pauseOnFocusLoss
      draggable
      pauseOnHover
      theme="light"
      toastClassName="!bg-white !shadow-lg !rounded-xl !border !border-gray-100"
      progressClassName="!bg-emerald-500"
      closeButton={false}
      icon={getIcon}
    />
  );
};
