/* Definice CSS proměnných přímo v selektoru .stamp */
.stamp {
  /* Výchozí hodnoty CSS proměnných */
  --stamp-border-color: rgba(16, 185, 129, 0.15);
  --stamp-dashed-color: rgba(16, 185, 129, 0.2);
  --stamp-text-color: rgba(16, 185, 129, 0.8);

  /* Styly pro razítko */
  width: 48px;
  height: 48px;
  background: #ffffff;
  border: 1px solid var(--stamp-border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-origin: center;
  transform: rotate(12deg);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
}

.stamp::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border: 1px dashed var(--stamp-dashed-color);
  border-radius: 50%;
  animation: stampRotate 45s linear infinite;
}

.stampContent {
  color: var(--stamp-text-color);
  font-weight: 500;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes stampRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
