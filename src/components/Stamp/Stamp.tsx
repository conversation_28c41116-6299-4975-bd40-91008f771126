import { LockKeyhole } from "lucide-react";
import React from "react";
import styles from "./styles.module.css";

interface StampProps {
  children?: React.ReactNode;
  className?: string;
  color?: "amber" | "emerald";
}

export const Stamp: React.FC<StampProps> = ({
  children,
  className = "",
  color = "emerald",
}) => {
  // Inline styly pro barvy
  const colorStyle =
    color === "amber"
      ? ({
          "--stamp-border-color": "rgba(245, 158, 11, 0.3)",
          "--stamp-dashed-color": "rgba(245, 158, 11, 0.2)",
          "--stamp-text-color": "rgb(245, 158, 11)",
        } as React.CSSProperties)
      : undefined; // Pro emerald použijeme výchozí hodnoty z CSS

  return (
    <div className={`${styles.stamp} ${className}`} style={colorStyle}>
      <div className={styles.stampContent}>{children}</div>
    </div>
  );
};

export const LockStamp: React.FC<{ className?: string }> = ({ className }) => (
  <Stamp className={className} color="amber">
    <LockKeyhole className="w-5 h-5" strokeWidth={2.5} />
  </Stamp>
);

export const NewStamp: React.FC<{ className?: string }> = ({ className }) => (
  <Stamp className={className}>
    <span className="text-[10px] font-medium tracking-wider uppercase">
      NOVÉ
    </span>
  </Stamp>
);

export const TopStamp: React.FC<{ className?: string }> = ({ className }) => (
  <Stamp className={className}>
    <span className="text-[10px] font-medium tracking-wider uppercase">
      TOP
    </span>
  </Stamp>
);
