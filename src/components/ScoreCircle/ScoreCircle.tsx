interface ScoreCircleProps {
  percentage: number;
  colorClasses: {
    bg: string;
    text: string;
  };
  size?: "sm" | "lg";
}

export const ScoreCircle = ({
  percentage,
  colorClasses,
  size = "lg",
}: ScoreCircleProps) => {
  const dimensions = size === "lg" ? "w-32 h-32" : "w-24 h-24 sm:w-32 sm:h-32";
  const textSize = size === "lg" ? "text-4xl" : "text-3xl sm:text-4xl";

  return (
    <div className="flex justify-center">
      <div className="relative inline-flex">
        <div className={`${dimensions} rounded-full bg-gray-100`}>
          <div
            className={`${dimensions} rounded-full absolute top-0 left-0 
            ${colorClasses.bg} opacity-50`}
            style={{
              clipPath: `polygon(0 0, 100% 0, 100% 100%, 0 100%, 0 ${100 - percentage}%)`,
            }}
          />
          <div className="w-full h-full flex items-center justify-center">
            <div className="text-center w-full flex items-center justify-center">
              <span
                className={`${textSize} font-bold ${colorClasses.text} leading-none`}
              >
                {percentage}&thinsp;%
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
