"use client";

import { X, Check } from "lucide-react";
import AddToCartButtonSimple from "@/components/AddToCartButtonSimple/AddToCartButtonSimple";
import { Application_application } from "@/graphql/types/Application";
import { useSession } from "next-auth/react";

interface Props {
  isOpen: boolean;
  onCloseAction: () => void;
  title?: string;
  features?: string[];
  application: Application_application;
}

export const PaymentRequiredModal = ({
  isOpen,
  onCloseAction,
  title = "Prémiová funkce",
  features = [
    "Testy obsahují všechny otázky",
    "Zobrazení chybných otázek",
    "Přístup do sekce výuka",
    "Statistiky a analýza výsledků",
  ],
  application,
}: Props) => {
  const { status } = useSession();
  const isAuthenticated = status === "authenticated";

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center px-6">
      <div
        className="fixed inset-0 bg-black/50"
        onClick={onCloseAction}
        aria-hidden="true"
      />
      <div className="relative bg-white shadow-xl rounded-2xl p-8 max-w-md w-full">
        <button
          onClick={onCloseAction}
          className="absolute right-4 top-4 text-gray-400 hover:text-gray-600"
        >
          <X className="w-5 h-5" />
        </button>

        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
          <p className="mt-2 text-sm text-gray-600">
            Zakupte si plnou verzi a získejte:
          </p>
        </div>

        <ul className="space-y-3 mb-8">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center text-gray-700">
              <Check className="w-5 h-5 mr-3 text-emerald-500 flex-shrink-0" />
              {feature}
            </li>
          ))}
        </ul>

        <div className="space-y-4">
          {/* Informace pro nepřihlášené uživatele */}
          {!isAuthenticated && (
            <div className="text-center text-sm text-gray-500 mb-1">
              Pro nákup našich produktů je nutné být přihlášený
            </div>
          )}

          <AddToCartButtonSimple
            application={application}
            onSuccess={onCloseAction}
          />
          <button
            onClick={onCloseAction}
            className="flex items-center justify-center w-full gap-2 px-4 py-2.5 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <span>Pokračovat bez zakoupení</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default PaymentRequiredModal;
