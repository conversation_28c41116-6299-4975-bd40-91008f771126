"use client";

import React, { FunctionComponent } from "react";
import { redirect } from "next/navigation";
import AddToCartButton from "@/app/[application]/kategorie/[category]/components/AddToCartButton";
import { Application_application } from "@/graphql/types/Application";
import { PlayCircle } from "lucide-react";

interface Props {
  application: Application_application;
}

const HomeTextBox: FunctionComponent<Props> = ({ application }) => {
  return (
    <div
      className={`flex ${application.isPaid ? "flex-col-reverse sm:flex-row" : "flex-col sm:flex-row"} gap-4`}
    >
      <AddToCartButton application={application} />
      <button
        className="flex items-center justify-center gap-2 px-8 py-4 bg-emerald-600 text-white rounded-xl hover:bg-emerald-500 transition-all duration-200 transform hover:-translate-y-1 shadow-lg hover:shadow-xl shadow-emerald-200"
        onClick={() => redirect(`/${application.slug}/kategorie`)}
      >
        <PlayCircle className="w-5 h-5" />
        <span className="font-semibold">Vybrat test</span>
      </button>
    </div>
  );
};

export default HomeTextBox;
