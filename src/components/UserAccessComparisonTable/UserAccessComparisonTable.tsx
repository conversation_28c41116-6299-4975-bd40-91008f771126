import React from "react";
import { Check, X } from "lucide-react";

interface Feature {
  name: string;
  guest: boolean;
  authenticated: boolean;
  paid: boolean;
  note?: string;
}

interface UserAccessComparisonTableProps {
  className?: string;
}

export const UserAccessComparisonTable = async ({
  className = "",
}: UserAccessComparisonTableProps) => {
  // Definice funkcí a jejich dostupnosti pro jednotlivé typy uživatelů
  const features: Feature[] = [
    {
      name: "Test zdarma",
      guest: true,
      authenticated: true,
      paid: true,
      note: "paid", // Speciální poznámka pro třetí sloupec
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
      guest: false,
      authenticated: true,
      paid: true,
    },
    {
      name: "<PERSON>blíben<PERSON> kategor<PERSON>",
      guest: false,
      authenticated: true,
      paid: true,
    },
    {
      name: "Statistik<PERSON>",
      guest: false,
      authenticated: true,
      paid: true,
    },
    {
      name: "<PERSON>š<PERSON><PERSON>á<PERSON>",
      guest: false,
      authenticated: false,
      paid: true,
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON> do sekce výuka",
      guest: false,
      authenticated: false,
      paid: true,
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON> chybn<PERSON> odpovědí",
      guest: false,
      authenticated: false,
      paid: true,
    },
  ];

  // Komponenta pro zobrazení ikony dostupnosti funkce
  const FeatureAvailability = ({
    available,
    note,
  }: {
    available: boolean;
    note?: string;
  }) => {
    if (note === "paid") {
      return (
        <div className="flex items-center justify-center">
          <span className="text-sm text-gray-500 italic">Plný přístup</span>
        </div>
      );
    }

    if (available) {
      return (
        <div className="flex items-center justify-center">
          <div className="p-1.5 bg-emerald-100 rounded-full">
            <Check className="w-4 h-4 text-emerald-600" strokeWidth={3} />
          </div>
          <span className="sr-only">Ano</span>
        </div>
      );
    }

    return (
      <div className="flex items-center justify-center">
        <div className="p-1.5 bg-red-100 rounded-full">
          <X className="w-4 h-4 text-red-600" strokeWidth={3} />
        </div>
        <span className="sr-only">Ne</span>
      </div>
    );
  };

  return (
    <div className={`w-full max-w-6xl mx-auto ${className}`}>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
        {/* Karta pro nepřihlášeného uživatele */}
        <div className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 border-b border-gray-100 h-[120px] md:h-[140px] lg:h-[120px] flex flex-col justify-center">
            <h3 className="text-xl font-semibold text-gray-900 text-center">
              Nepřihlášený
            </h3>
            <p className="mt-2 text-sm text-gray-500 text-center">
              Základní přístup bez registrace
            </p>
          </div>
          <div className="p-6">
            <ul className="space-y-4">
              {features.map((feature, index) => (
                <li
                  key={index}
                  className="flex items-center justify-between py-2"
                >
                  <span className="text-gray-700">{feature.name}</span>
                  <FeatureAvailability
                    available={feature.guest}
                    note={undefined}
                  />
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Karta pro přihlášeného uživatele */}
        <div className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-6 border-b border-gray-100 h-[120px] md:h-[140px] lg:h-[120px] flex flex-col justify-center">
            <h3 className="text-xl font-semibold text-gray-900 text-center">
              Přihlášený
            </h3>
            <p className="mt-2 text-sm text-gray-500 text-center">
              Uživatel s vytvořeným účtem
            </p>
          </div>
          <div className="p-6">
            <ul className="space-y-4">
              {features.map((feature, index) => (
                <li
                  key={index}
                  className="flex items-center justify-between py-2"
                >
                  <span className="text-gray-700">{feature.name}</span>
                  <FeatureAvailability
                    available={feature.authenticated}
                    note={undefined}
                  />
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Karta pro uživatele se zakoupenou aplikací */}
        <div className="bg-white rounded-2xl border-2 border-emerald-100 shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 relative">
          <div className="absolute top-0 right-0 bg-emerald-500 text-white px-4 py-1 text-xs font-semibold rounded-bl-lg z-10">
            Doporučeno
          </div>
          <div className="p-6 pt-8 md:pt-10 lg:pt-8 border-b border-gray-100 bg-gradient-to-r from-emerald-50 to-blue-50 h-[120px] md:h-[140px] lg:h-[120px] flex flex-col justify-center">
            <h3 className="text-xl font-semibold text-gray-900 text-center mt-2 md:mt-4 lg:mt-2">
              Zakoupená aplikace
            </h3>
            <p className="mt-2 text-sm text-gray-500 text-center">
              Plný přístup ke všem funkcím
            </p>
          </div>
          <div className="p-6">
            <ul className="space-y-4">
              {features.map((feature, index) => (
                <li
                  key={index}
                  className="flex items-center justify-between py-2"
                >
                  <span className="text-gray-700">{feature.name}</span>
                  <FeatureAvailability
                    available={feature.paid}
                    note={feature.note}
                  />
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserAccessComparisonTable;
