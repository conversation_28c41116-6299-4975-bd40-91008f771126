.handleCategoryItem {
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    transform-origin: top center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08), 
                0 1px 2px rgba(0,0,0,0.12);
}

.handleCategoryItem:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Stylování razítka */
.stamp {
    width: 48px;
    height: 48px;
    background: #ffffff;
    border: 1px solid rgba(16, 185, 129, 0.15);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transform-origin: center;
    animation: stampAppear 0.5s ease-out forwards;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.stamp::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    border: 1px dashed rgba(16, 185, 129, 0.2);
    border-radius: 50%;
    animation: stampRotate 45s linear infinite;
}

@keyframes stampAppear {
    from {
        opacity: 0;
        transform: scale(0.5) rotate(45deg);
    }
    to {
        opacity: 1;
        transform: scale(1) rotate(12deg);
    }
}

@keyframes stampRotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Přidáme stín pod svorkou */
.handleCategoryItem::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 96px; /* Šířka svorky */
    height: 1px;
    background: rgba(0,0,0,0.05);
    border-radius: 1px;
}

.animated {
    animation: fadeInUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.animatedFast {
    animation: fadeInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.image3d img {
    border-radius: 10px;
    transition: all 0.3s ease;
}
.animated:hover img {
    transform: scale(1.02);
}

.ExampleAdSlot{
    margin-top: 16px;
    height: 250px;
    width: 970px;
    display: block;
    border: solid;
}

@media (min-width: 1280px) {
    .ExampleAdSlot{
        display: none !important;
    }
}

@media (max-width: 1000px) {
    .ExampleAdSlot{
        width: 728px;
        height: 90px;
    }
}

@media (max-width: 750px) {
    .ExampleAdSlot{
        width: 336px;
        height: 280px;
    }
}
