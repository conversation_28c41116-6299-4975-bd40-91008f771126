"use server";

import React, { FunctionComponent } from "react";
import Image from "next/image";
import Link from "next/link";
import Styles from "../styles.module.css";
import { Anchor, Ship, Siren } from "lucide-react";
import { Stamp, NewStamp, TopStamp } from "@/components/Stamp";

const CategoryItem: FunctionComponent<{
  href: string;
  imgSrc: string;
  title: string;
  questions: number;
  slug: string;
  price: number;
  isPaid?: boolean;
}> = ({ href, imgSrc, title, questions, slug, price, isPaid }) => {
  const getStampContent = (slug: string): React.ReactNode | null => {
    switch (slug) {
      case "autoskola":
        return <TopStamp />;
      case "zbrojni-prukaz":
        return <NewStamp />;
      case "potapecsky-prukaz":
        return (
          <Stamp>
            <Anchor className="w-4 h-4" />
          </Stamp>
        );
      case "straznik":
        return (
          <Stamp>
            <Siren className="w-5 h-5" />
          </Stamp>
        );
      case "kapitanske-zkousky":
        return (
          <Stamp>
            <Ship className="w-5 h-5" />
          </Stamp>
        );
      default:
        return null;
    }
  };

  const stampContent = getStampContent(slug);

  // Formátování ceny s mezerou mezi tisíci
  const formattedPrice = price.toLocaleString("cs-CZ");

  return (
    <Link href={href} className="block group relative">
      <div
        className={`${Styles.animated} ${Styles.handleCategoryItem} bg-white/50 p-6 rounded-2xl border border-gray-100`}
      >
        {/* Otisk razítka - zobrazí se pouze pokud existuje obsah */}
        {stampContent && (
          <div className="absolute -top-3 -right-3 z-10">{stampContent}</div>
        )}

        <div className={`${Styles.image3d} space-y-8`}>
          <div className="flex items-center gap-6">
            <div className="bg-gradient-to-br from-emerald-500/10 to-blue-500/10 p-4 rounded-2xl">
              <Image
                src={imgSrc}
                width={64}
                height={64}
                alt={title}
                priority={true}
                className="mx-auto transition-transform duration-300 group-hover:scale-110"
              />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-gray-900">{title}</h3>
              <p className="text-lg text-gray-600">Online příprava</p>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center text-base text-gray-700">
              <svg
                className="w-6 h-6 mr-3 text-emerald-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 6h11M9 12h11M9 18h11M5 6h.01M5 12h.01M5 18h.01"
                />
              </svg>
              {questions} testových otázek
            </div>
            <div className="flex items-center text-base text-gray-700">
              <svg
                className="w-6 h-6 mr-3 text-emerald-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15.042 21.672L13.684 16.6m0 0l-2.51 2.225.569-9.47 5.227 7.917-3.286-.672zM12 2.25V4.5m5.834.166l-1.591 1.591M20.25 10.5H18M7.757 14.743l-1.59 1.59M6 10.5H3.75m4.007-4.243l-1.59-1.59"
                />
              </svg>
              Interaktivní učení
            </div>
          </div>

          <div className="mt-8 flex items-center justify-between border-t border-gray-100 pt-6">
            {/* Levá strana - cena/stav */}
            <div
              className={
                isPaid
                  ? "rounded-full px-4 py-1.5 bg-emerald-50 text-emerald-700"
                  : "text-gray-600"
              }
            >
              <div className="flex items-center gap-2 text-sm font-medium">
                {isPaid ? (
                  <span>Zakoupeno</span>
                ) : (
                  <div className="rounded-full px-4 py-1.5 bg-gray-50 text-gray-700 border">
                    <span>{formattedPrice} Kč</span>
                  </div>
                )}
              </div>
            </div>

            {/* Pravá strana - tlačítko */}
            <div className="flex items-center gap-2 font-medium text-emerald-600 text-lg">
              {isPaid ? <span>Začít test</span> : <span>Zkusit zdarma</span>}
              <svg
                className="w-5 h-5 transform transition-transform duration-300 group-hover:translate-x-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M5 12h14m-7-7l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default CategoryItem;
