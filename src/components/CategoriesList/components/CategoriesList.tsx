"use server";

import React, { FunctionComponent } from "react";
import CategoryItem from "./CategoryItem";
import { Applications_applications_application } from "@/graphql/types/Applications";

interface CategoriesListProps {
  applications: Applications_applications_application[];
}

const CategoriesList: FunctionComponent<CategoriesListProps> = ({
  applications,
}) => {
  return (
    <div className="my-16 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {applications.map((application) => (
        <CategoryItem
          key={application.id}
          href={`/${application.slug}/uvod`}
          imgSrc={`/${application.slug}/icon/web/android-chrome-192x192.png`}
          title={application.name}
          questions={application.questionsTotalCount}
          slug={application.slug}
          price={application.price}
          isPaid={application.isPaid}
        />
      ))}
    </div>
  );
};

export default CategoriesList;
