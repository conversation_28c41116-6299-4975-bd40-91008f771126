"use server";

import { FunctionComponent } from "react";
import Link from "next/link";
import dynamic from "next/dynamic";
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth.config";
import { ExtendedDefaultSession } from "@/types/extendedDefaultSession";
import { Application_application } from "@/graphql/types/Application";
import StaticMenu from "./StaticMenu";
import { getApplications } from "@/lib/services/applications";

// Dynamicky importujeme klientské komponenty
const CartLink = dynamic(() => import("./CartLink"));
const MobileCartLink = dynamic(() => import("./components/MobileCartLink"));
const UserMenu = dynamic(() => import("./UserMenu"));
const Logout = dynamic(() => import("./Logout"));

interface Props {
  application: Application_application | undefined;
}

const Menu: FunctionComponent<Props> = async ({ application }) => {
  const session = (await getServerSession(
    authConfig,
  )) as ExtendedDefaultSession;

  const applications = await getApplications();

  // Přidáme dynamické položky k základnímu menu
  const additionalDesktopItems = (
    <>
      {session && <CartLink />}
      {session && application && (
        <>
          <Link
            href={`/${application.slug}/oblibene-otazky`}
            className="text-gray-600 hover:text-emerald-500 transition-colors"
          >
            Oblíbené otázky
          </Link>
          <Link
            href={`/${application.slug}/statistiky`}
            className="text-gray-600 hover:text-emerald-500 transition-colors"
          >
            Statistiky
          </Link>
        </>
      )}
      {!session ? (
        <Link
          href="/prihlaseni"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-full text-white bg-emerald-600 hover:bg-emerald-700 transition-all duration-200 shadow-sm hover:shadow-emerald-500/50"
        >
          Přihlášení
        </Link>
      ) : (
        <UserMenu email={session.user?.email || ""} />
      )}
    </>
  );

  const additionalMobileItems = (
    <>
      {session && (
        <div className="px-2 py-0.5">
          <h3 className="text-xs font-medium text-gray-500 mb-1">Nákup</h3>
          <MobileCartLink />
        </div>
      )}
      {session && application && (
        <>
          <div className="px-2 py-0.5">
            <h3 className="text-xs font-medium text-gray-500 mb-1">Aplikace</h3>
            <div className="space-y-0.5">
              <Link
                href={`/${application.slug}/oblibene-otazky`}
                className="flex items-center w-full px-2 py-1.5 rounded hover:bg-gray-50 transition-all duration-200"
              >
                <div className="mr-2 p-1.5 rounded bg-gray-50 text-blue-600">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                    />
                  </svg>
                </div>
                <span className="font-medium text-xs text-gray-700">
                  Oblíbené otázky
                </span>
              </Link>
              <Link
                href={`/${application.slug}/statistiky`}
                className="flex items-center w-full px-2 py-1.5 rounded hover:bg-gray-50 transition-all duration-200"
              >
                <div className="mr-2 p-1.5 rounded bg-gray-50 text-indigo-600">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                </div>
                <span className="font-medium text-xs text-gray-700">
                  Statistiky
                </span>
              </Link>
            </div>
          </div>
        </>
      )}
      {!session ? (
        <div className="px-2 py-0.5">
          <h3 className="text-xs font-medium text-gray-500 mb-1">Účet</h3>
          <Link
            href="/prihlaseni"
            className="flex items-center w-full px-2 py-1.5 rounded hover:bg-gray-50 transition-all duration-200"
          >
            <div className="mr-2 p-1.5 rounded bg-gray-50 text-emerald-600">
              <svg
                className="w-4 h-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"
                />
              </svg>
            </div>
            <span className="font-medium text-xs text-gray-700">
              Přihlášení
            </span>
          </Link>
        </div>
      ) : (
        <div className="px-2 py-0.5">
          <h3 className="text-xs font-medium text-gray-500 mb-1">Účet</h3>
          <Link
            href={`/profil`}
            className="flex items-center w-full px-2 py-1.5 rounded hover:bg-gray-50 transition-all duration-200"
          >
            <div className="mr-2 p-1.5 rounded bg-gray-50 text-emerald-600">
              <svg
                className="w-4 h-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                />
              </svg>
            </div>
            <span className="font-medium text-xs text-gray-700 truncate">
              {session.user?.email}
            </span>
          </Link>
          <Logout className="flex items-center w-full px-2 py-1.5 rounded hover:bg-gray-50 transition-all duration-200 mt-0.5">
            <div className="mr-2 p-1.5 rounded bg-gray-50 text-red-600">
              <svg
                className="w-4 h-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                />
              </svg>
            </div>
            <span className="font-medium text-xs text-gray-700">
              Odhlásit se
            </span>
          </Logout>
        </div>
      )}
    </>
  );

  return (
    <StaticMenu
      application={application}
      applications={applications}
      additionalDesktopItems={additionalDesktopItems}
      additionalMobileItems={additionalMobileItems}
    />
  );
};

export default Menu;
