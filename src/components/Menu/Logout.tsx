"use client";
import { signOut } from "next-auth/react";
import React from "react";

interface Props {
  className?: string;
  children?: React.ReactNode;
}

const Logout: React.FC<Props> = ({ className, children }) => {
  return (
    <button
      onClick={() => signOut()}
      className={
        className ||
        "block text-left px-4 py-2 text-sm text-gray-500 hover:bg-gradient-to-r hover:from-emerald-50 hover:to-blue-50 hover:text-emerald-600 transition-colors"
      }
    >
      {children || "Odhlásit"}
    </button>
  );
};

export default Logout;
