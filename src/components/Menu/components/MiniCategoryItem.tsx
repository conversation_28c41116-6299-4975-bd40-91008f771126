"use client";

import React from "react";
import Link from "next/link";
import { BookOpen, Shield, Anchor, Car, Ship, Siren } from "lucide-react";

interface MiniCategoryItemProps {
  slug: string;
  title: string;
  size?: "sm" | "md";
}

export const MiniCategoryItem: React.FC<MiniCategoryItemProps> = ({
  slug,
  title,
  size = "md",
}) => {
  // Určení velikosti ikon a paddingu podle parametru size
  const iconSize = size === "sm" ? "w-5 h-5" : "w-6 h-6";
  const containerSize = size === "sm" ? "w-6 h-6" : "w-8 h-8";
  const padding = size === "sm" ? "p-1.5" : "p-2";
  const margin = size === "sm" ? "mr-2" : "mr-3";
  const textSize = size === "sm" ? "text-sm" : "";

  // Funkce pro získání správné ikony podle slugu
  const getIcon = () => {
    switch (slug) {
      case "zbrojni-prukaz":
        return <Shield className={iconSize} />;
      case "autoskola":
        return <Car className={iconSize} />;
      case "potapecsky-prukaz":
        return <Anchor className={iconSize} />;
      case "straznik":
        return <Siren className={iconSize} />;
      case "kapitanske-zkousky":
        return <Ship className={iconSize} />;
      default:
        return <BookOpen className={iconSize} />;
    }
  };

  return (
    <Link
      href={`/${slug}/uvod`}
      className="flex items-center p-2 sm:p-3 rounded-lg hover:bg-gray-50 transition-all duration-200 border border-gray-100 group/card"
    >
      <div
        className={`${margin} ${padding} bg-gradient-to-br from-emerald-500/10 to-blue-500/10 rounded-lg group-hover/card:from-emerald-500/15 group-hover/card:to-blue-500/15 transition-all duration-200`}
      >
        <div
          className={`${containerSize} flex items-center justify-center text-emerald-600`}
        >
          {getIcon()}
        </div>
      </div>
      <div className="flex-1 truncate">
        <span
          className={`font-medium ${textSize} text-gray-700 group-hover/card:text-gray-900 transition-colors`}
        >
          {title}
        </span>
      </div>
    </Link>
  );
};

export default MiniCategoryItem;
