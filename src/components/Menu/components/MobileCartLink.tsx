"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useCart } from "@/context/CartContext";
import { ShoppingCart } from "lucide-react";

export default function MobileCartLink() {
  const { items } = useCart();
  const pathname = usePathname();

  if (pathname === "/kosik") {
    return null;
  }

  return (
    <Link
      href="/kosik"
      className="flex items-center w-full px-2 py-1.5 rounded hover:bg-gray-50 transition-all duration-200"
    >
      <div className="mr-2 p-1.5 rounded bg-gray-50 text-emerald-600 relative">
        <ShoppingCart className="w-4 h-4" />
        {items.length > 0 && (
          <span className="absolute -top-1 -right-1 flex items-center justify-center min-w-[16px] h-4 text-[10px] text-white bg-emerald-600 rounded-full px-1">
            {items.length}
          </span>
        )}
      </div>
      <span className="font-medium text-xs text-gray-700"><PERSON><PERSON><PERSON></span>
    </Link>
  );
}
