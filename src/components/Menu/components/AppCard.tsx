"use client";

import React from "react";
import Link from "next/link";
import { <PERSON>chor, Car, Ship, Shield, Siren, GraduationCap } from "lucide-react";

interface AppCardProps {
  slug: string;
  title: string;
  isMobile?: boolean;
}

export const AppCard: React.FC<AppCardProps> = ({
  slug,
  title,
  isMobile = false,
}) => {
  // Funkce pro získání správné ikony podle slugu
  const getIcon = () => {
    const iconClass = isMobile ? "w-4 h-4" : "w-6 h-6";

    switch (slug) {
      case "zbrojni-prukaz":
        return <Shield className={iconClass} />;
      case "autoskola":
        return <Car className={iconClass} />;
      case "potapecsky-prukaz":
        return <Anchor className={iconClass} />;
      case "straznik":
        return <Siren className={iconClass} />;
      case "kapitanske-zkousky":
        return <Ship className={iconClass} />;
      default:
        return <GraduationCap className={iconClass} />;
    }
  };

  // Funkce pro získání barvy ikony podle slugu
  const getIconColor = () => {
    switch (slug) {
      case "zbrojni-prukaz":
        return "text-blue-600";
      case "autoskola":
        return "text-emerald-600";
      case "potapecsky-prukaz":
        return "text-cyan-600";
      case "straznik":
        return "text-amber-600";
      case "kapitanske-zkousky":
        return "text-indigo-600";
      default:
        return "text-emerald-600";
    }
  };

  if (isMobile) {
    return (
      <Link
        href={`/${slug}/uvod`}
        className="flex items-center w-full px-2 py-1.5 rounded hover:bg-gray-50 transition-all duration-200"
      >
        <div className={`mr-2 p-1.5 rounded bg-gray-50 ${getIconColor()}`}>
          {getIcon()}
        </div>
        <span className="font-medium text-xs text-gray-700">{title}</span>
      </Link>
    );
  }

  return (
    <Link
      href={`/${slug}/uvod`}
      className="block transition-all duration-200 hover:-translate-y-1 h-full"
    >
      <div className="bg-white rounded-lg shadow-sm hover:shadow border border-gray-100 overflow-hidden transition-all duration-200 h-full">
        <div className="p-4 h-full">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg bg-gray-50 ${getIconColor()}`}>
              {getIcon()}
            </div>
            <span className="font-medium text-gray-700 line-clamp-2">
              {title}
            </span>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default AppCard;
