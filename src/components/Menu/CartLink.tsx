"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useCart } from "@/context/CartContext";

export default function CartLink() {
  const { items } = useCart();
  const pathname = usePathname();

  if (pathname === "/kosik") {
    return null;
  }

  return (
    <Link
      href="/kosik"
      className="relative inline-flex items-center text-gray-600 hover:text-emerald-500 transition-colors md:px-0 px-4 py-2 w-full md:w-auto group"
    >
      <div className="flex items-center justify-between w-full md:justify-start relative">
        <span>Košík</span>
        {items.length > 0 && (
          <span className="flex items-center justify-center min-w-[20px] h-5 text-xs text-white bg-emerald-600 rounded-full px-1.5 md:absolute md:-top-2 md:-right-2 md:translate-x-0 translate-x-1 group-hover:bg-emerald-500 transition-colors">
            {items.length}
          </span>
        )}
        <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500/20 to-blue-500/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
      </div>
    </Link>
  );
}
