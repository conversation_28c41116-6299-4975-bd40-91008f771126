"use client";

import { User } from "lucide-react";
import Logout from "./Logout";
import Link from "next/link";

interface Props {
  email: string;
}

const UserMenu = ({ email }: Props) => {
  return (
    <div className="relative group">
      <div className="inline-flex items-center transition-colors cursor-pointer md:px-0 px-4 py-2 w-full md:w-auto">
        <div className="relative">
          <User className="w-5 h-5 text-gray-600 group-hover:text-emerald-500 transition-colors" />
          <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500/20 to-blue-500/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
        </div>
      </div>

      <div
        className="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 border border-gray-100
                    opacity-0 invisible group-hover:opacity-100 group-hover:visible
                    transition-all duration-200 transform origin-top-right"
      >
        <div className="px-4 py-2 border-b border-gray-100">
          <Link
            href={`/profil`}
            className="text-sm text-gray-500 hover:text-emerald-600 transition-colors truncate block"
          >
            {email}
          </Link>
        </div>
        <Logout className="block w-full text-left px-4 py-2 text-sm text-gray-500 hover:bg-gradient-to-r hover:from-emerald-50 hover:to-blue-50 hover:text-emerald-600 transition-colors" />
      </div>
    </div>
  );
};

export default UserMenu;
