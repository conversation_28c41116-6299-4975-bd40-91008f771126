import { FunctionComponent, ReactNode } from "react";
import Link from "next/link";
import { Application_application } from "@/graphql/types/Application";
import { ChevronDown } from "lucide-react";
import AppCard from "./components/AppCard";

interface Props {
  application: Application_application | undefined;
  applications?: Application_application[];
  additionalDesktopItems?: ReactNode;
  additionalMobileItems?: ReactNode;
}

const StaticMenu: FunctionComponent<Props> = ({
  application,
  applications = [],
  additionalDesktopItems,
  additionalMobileItems,
}) => {
  const getTitle = (application: Application_application) => {
    if (application?.slug === "zbrojni-prukaz") {
      return "Zbrojní průkaz ČR";
    } else if (application?.slug === "autoskola") {
      return "Autoškola ČR";
    } else if (application?.slug === "potapecsky-prukaz") {
      return "Potápěčský průkaz ČR";
    } else if (application?.slug === "straznik") {
      return "Strážník ČR";
    } else if (application?.slug === "kapitanske-zkousky") {
      return "Kapitánsk<PERSON> zkou<PERSON>";
    }
    return "Průkazy online";
  };

  const showApplicationsMenu = applications.length > 0;

  return (
    <div className="relative z-50">
      <nav className="mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo a název */}
          <div className="flex items-center space-x-4">
            <Link
              href="/"
              className="flex items-center space-x-2 text-gray-900 hover:text-emerald-600 transition-colors group relative"
            >
              <span className="text-xl font-semibold">Aktuální testy</span>
              <div className="hidden sm:flex items-center space-x-1">
                <div className="h-3 w-3 rounded-full bg-blue-400/80"></div>
                <div className="h-5 w-1.5 bg-blue-500"></div>
              </div>
              <div className="absolute -inset-2 bg-gradient-to-r from-emerald-500/10 to-blue-500/10 rounded-lg blur opacity-0 group-hover:opacity-100 transition-all duration-300 hidden sm:block"></div>
            </Link>
            {application && (
              <Link
                href={`/${application.slug}/uvod`}
                className="hidden sm:block text-xl font-bold text-gray-700 hover:text-emerald-600 transition-colors relative group"
              >
                <span>{getTitle(application)}</span>
                <div className="absolute -inset-2 bg-gradient-to-r from-emerald-500/10 to-blue-500/10 rounded-lg blur opacity-0 group-hover:opacity-100 transition-all duration-300 hidden sm:block"></div>
              </Link>
            )}
          </div>

          {/* Desktop navigace */}
          <div className="hidden md:flex items-center space-x-6">
            {showApplicationsMenu && (
              <div className="relative group">
                <button className="flex items-center space-x-1 text-gray-600 hover:text-emerald-500 transition-colors cursor-pointer relative">
                  <span>Kategorie</span>
                  <ChevronDown className="w-4 h-4 transition-transform group-hover:rotate-180" />
                  <div className="absolute -inset-2 bg-gradient-to-r from-emerald-500/10 to-blue-500/10 rounded-lg blur opacity-0 group-hover:opacity-100 transition-all duration-300 hidden sm:block"></div>
                </button>

                <div
                  className="absolute top-full right-0 mt-2 w-[600px] max-w-[95vw] bg-white rounded-lg shadow-md p-4 border border-gray-100
                              opacity-0 invisible group-hover:opacity-100 group-hover:visible
                              transition-all duration-200 transform origin-top-right"
                >
                  <h3 className="text-sm font-medium text-gray-500 mb-3">
                    Vyberte aplikaci
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3 auto-rows-fr">
                    {applications.map((app) => (
                      <AppCard
                        key={app.id}
                        slug={app.slug}
                        title={getTitle(app)}
                      />
                    ))}
                  </div>
                </div>
              </div>
            )}

            {additionalDesktopItems}
          </div>

          {/* Mobilní menu toggle */}
          <div className="md:hidden">
            <input
              type="checkbox"
              name="toggle_nav"
              id="toggle_nav"
              className="peer hidden"
            />
            <label
              htmlFor="toggle_nav"
              className="cursor-pointer p-2 flex items-center justify-center h-10 w-10 hover:bg-gray-50 sm:hover:bg-gradient-to-r sm:hover:from-emerald-50 sm:hover:to-blue-50 rounded-lg transition-colors relative group"
            >
              <svg
                className="h-5 w-5 text-gray-600 group-hover:text-emerald-500 transition-colors"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 6h16M4 12h16m-7 6h7"
                ></path>
              </svg>
              <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500/10 to-blue-500/10 rounded-lg blur opacity-0 group-hover:opacity-100 transition-all duration-300 hidden sm:block"></div>
            </label>

            {/* Mobilní menu */}
            <div className="absolute right-0 top-full w-[280px] max-w-[95vw] hidden peer-checked:block">
              <div className="bg-white rounded-lg shadow-md border border-gray-100 py-1">
                {showApplicationsMenu && (
                  <>
                    <div className="px-2 py-0.5">
                      <h3 className="text-xs font-medium text-gray-500 mb-1">
                        Aplikace
                      </h3>
                      <div className="space-y-0.5">
                        {applications.map((app) => (
                          <AppCard
                            key={app.id}
                            slug={app.slug}
                            title={getTitle(app)}
                            isMobile={true}
                          />
                        ))}
                      </div>
                    </div>
                    <div className="border-t border-gray-100 my-0.5"></div>
                  </>
                )}
                {additionalMobileItems}
              </div>
            </div>
          </div>
        </div>
      </nav>
    </div>
  );
};

export default StaticMenu;
