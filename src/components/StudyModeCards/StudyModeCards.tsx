"use client";

import React from "react";
import Link from "next/link";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ChevronRight } from "lucide-react";
import { Application_application } from "@/graphql/types/Application";
import { Category_category } from "@/graphql/types/Category";
import FavouriteQuestionsLink from "@/app/[application]/kategorie/FavouriteQuestionsLink/FavouriteQuestionsLink";

interface StudyModeCardsProps {
  application: Application_application;
  category?: Category_category;
}

export const StudyModeCards: React.FC<StudyModeCardsProps> = ({
  application,
  category,
}) => {
  const applicationSlug = application.slug;

  return (
    <div className="grid md:grid-cols-2 gap-8">
      {/* Karta 1 - Kompletní databáze otázek */}
      <div className="bg-white rounded-2xl shadow-lg p-8 border border-gray-100 relative overflow-hidden group hover:shadow-xl transition-all duration-300">
        <div className="flex items-center gap-4 mb-6">
          <div className="p-3 bg-purple-100 rounded-xl">
            <ListFilter className="w-6 h-6 text-purple-600" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900">
            Kompletní databáze otázek
          </h3>
        </div>
        <p className="text-gray-600 mb-6">
          Procházejte všechny dostupné testové otázky. Ideální pro systematickou
          přípravu a detailní studium jednotlivých témat.
        </p>
        {category ? (
          <Link
            href={`/${applicationSlug}/kategorie/${category.slug}/vsechny-otazky`}
            className="inline-flex items-center justify-center w-full px-4 py-3.5 bg-white border-2 border-gray-200 text-gray-700 font-medium rounded-xl hover:bg-gray-50 transition-all duration-200 group/button"
          >
            <span className="group-hover/button:translate-x-0.5 transition-transform duration-150">
              Zobrazit otázky v kategorii
            </span>
            <ChevronRight className="ml-2 w-5 h-5 text-gray-400" />
          </Link>
        ) : (
          <Link
            href={`/${applicationSlug}/vsechny-otazky`}
            className="inline-flex items-center justify-center w-full px-4 py-3.5 bg-white border-2 border-gray-200 text-gray-700 font-medium rounded-xl hover:bg-gray-50 transition-all duration-200 group/button"
          >
            <span className="group-hover/button:translate-x-0.5 transition-transform duration-150">
              Zobrazit všechny otázky
            </span>
            <ChevronRight className="ml-2 w-5 h-5 text-gray-400" />
          </Link>
        )}
      </div>

      {/* Karta 2 - Oblíbené otázky */}
      <div className="bg-white rounded-2xl shadow-lg p-8 border border-gray-100 relative overflow-hidden group hover:shadow-xl transition-all duration-300">
        <div className="flex items-center gap-4 mb-6">
          <div className="p-3 bg-blue-100 rounded-xl">
            <BookOpen className="w-6 h-6 text-blue-600" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900">
            Oblíbené otázky
          </h3>
        </div>
        <p className="text-gray-600 mb-6">
          Vytvořte si vlastní seznam otázek pro opakování. Označte si důležité
          nebo obtížné otázky a vraťte se k nim později.
        </p>
        <FavouriteQuestionsLink applicationSlug={applicationSlug} />
      </div>
    </div>
  );
};

export default StudyModeCards;
