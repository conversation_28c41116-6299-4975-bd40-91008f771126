import React from "react";
import { 
  Waves, 
  Users, 
  BookOpen, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  FileText,
  GraduationCap,
  Star,
  Target,
  Fish,
  Gauge
} from "lucide-react";

export const DivingLicenseDetails = () => {
  return (
    <div className="space-y-12">
      {/* Hlavní popis <PERSON> */}
      <div className="bg-gradient-to-br from-cyan-50 to-blue-50 rounded-3xl p-8 border border-cyan-100">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-3 bg-cyan-100 rounded-xl">
            <Fish className="w-8 h-8 text-cyan-600" />
          </div>
          <h2 className="text-3xl font-bold text-gray-900">
            Potápěčský průkaz
          </h2>
        </div>
        <p className="text-lg text-gray-700 leading-relaxed">
          Získejte certifikaci pro bezpečné potápění. <PERSON><PERSON><PERSON> v<PERSON> platforma vás připraví 
          na teoretické zkoušky podle standardů PADI, SSI a dalších certifikačních agentur 
          pro rok {new Date().getFullYear()}.
        </p>
      </div>

      {/* Typy certifikací */}
      <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
        <div className="flex items-center gap-3 mb-8">
          <Waves className="w-6 h-6 text-blue-600" />
          <h3 className="text-2xl font-semibold text-gray-900">
            Úrovně potápěčských certifikátů
          </h3>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="p-4 bg-cyan-50 rounded-xl border border-cyan-100">
            <h4 className="font-semibold text-cyan-900 mb-3 text-center">
              Open Water Diver
            </h4>
            <ul className="text-sm text-cyan-700 space-y-2">
              <li>• Základní certifikace</li>
              <li>• Hloubka do 18 metrů</li>
              <li>• Pod dohledem instruktora</li>
              <li>• Vstupní úroveň</li>
            </ul>
          </div>
          
          <div className="p-4 bg-blue-50 rounded-xl border border-blue-100">
            <h4 className="font-semibold text-blue-900 mb-3 text-center">
              Advanced Open Water
            </h4>
            <ul className="text-sm text-blue-700 space-y-2">
              <li>• Pokročilá certifikace</li>
              <li>• Hloubka do 30 metrů</li>
              <li>• Specializované ponory</li>
              <li>• Noční potápění</li>
            </ul>
          </div>
          
          <div className="p-4 bg-indigo-50 rounded-xl border border-indigo-100">
            <h4 className="font-semibold text-indigo-900 mb-3 text-center">
              Rescue Diver
            </h4>
            <ul className="text-sm text-indigo-700 space-y-2">
              <li>• Záchranné techniky</li>
              <li>• Pomoc v nouzi</li>
              <li>• Prevence nehod</li>
              <li>• Řešení problémů</li>
            </ul>
          </div>
          
          <div className="p-4 bg-purple-50 rounded-xl border border-purple-100">
            <h4 className="font-semibold text-purple-900 mb-3 text-center">
              Divemaster
            </h4>
            <ul className="text-sm text-purple-700 space-y-2">
              <li>• Profesionální úroveň</li>
              <li>• Vedení skupin</li>
              <li>• Asistence instruktorům</li>
              <li>• Komerční potápění</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Struktura zkoušky */}
      <div className="grid lg:grid-cols-2 gap-8">
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <div className="flex items-center gap-3 mb-6">
            <FileText className="w-6 h-6 text-emerald-600" />
            <h3 className="text-xl font-semibold text-gray-900">
              Struktura teoretické zkoušky
            </h3>
          </div>
          <div className="space-y-4">
            <div className="flex items-center gap-3 p-3 bg-emerald-50 rounded-lg">
              <CheckCircle className="w-5 h-5 text-emerald-600" />
              <span className="text-gray-700">50 testových otázek</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
              <Clock className="w-5 h-5 text-blue-600" />
              <span className="text-gray-700">60 minut na vyřešení</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-amber-50 rounded-lg">
              <Target className="w-5 h-5 text-amber-600" />
              <span className="text-gray-700">Minimálně 75% úspěšnost</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <div className="flex items-center gap-3 mb-6">
            <GraduationCap className="w-6 h-6 text-blue-600" />
            <h3 className="text-xl font-semibold text-gray-900">
              Požadavky pro certifikaci
            </h3>
          </div>
          <div className="space-y-4">
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <Users className="w-5 h-5 text-gray-600" />
              <span className="text-gray-700">Minimálně 16 let (12+ s rodičem)</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <FileText className="w-5 h-5 text-gray-600" />
              <span className="text-gray-700">Lékařské osvědčení</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <Star className="w-5 h-5 text-gray-600" />
              <span className="text-gray-700">Praktické ponory v bazénu</span>
            </div>
          </div>
        </div>
      </div>

      {/* Tematické okruhy */}
      <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
        <div className="flex items-center gap-3 mb-8">
          <BookOpen className="w-6 h-6 text-indigo-600" />
          <h3 className="text-2xl font-semibold text-gray-900">
            Tematické okruhy výuky
          </h3>
        </div>
        
        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="p-4 bg-indigo-50 rounded-xl border border-indigo-100">
              <h4 className="font-semibold text-indigo-900 mb-2">
                1. Fyzika potápění
              </h4>
              <ul className="text-sm text-indigo-700 space-y-1">
                <li>• Tlak a jeho účinky</li>
                <li>• Vztlak a plovatelnost</li>
                <li>• Světlo a zvuk pod vodou</li>
                <li>• Tepelná vodivost</li>
              </ul>
            </div>
            
            <div className="p-4 bg-emerald-50 rounded-xl border border-emerald-100">
              <h4 className="font-semibold text-emerald-900 mb-2">
                2. Fyziologie potápění
              </h4>
              <ul className="text-sm text-emerald-700 space-y-1">
                <li>• Dýchací systém</li>
                <li>• Krevní oběh</li>
                <li>• Dekompresní nemoc</li>
                <li>• Dusíková narkóza</li>
              </ul>
            </div>
            
            <div className="p-4 bg-blue-50 rounded-xl border border-blue-100">
              <h4 className="font-semibold text-blue-900 mb-2">
                3. Potápěčské vybavení
              </h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Dýchací automaty</li>
                <li>• Kompenzátory vztlaku</li>
                <li>• Neopreny a masky</li>
                <li>• Údržba vybavení</li>
              </ul>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="p-4 bg-amber-50 rounded-xl border border-amber-100">
              <h4 className="font-semibold text-amber-900 mb-2">
                4. Bezpečnost potápění
              </h4>
              <ul className="text-sm text-amber-700 space-y-1">
                <li>• Plánování ponorů</li>
                <li>• Dekompresní tabulky</li>
                <li>• Potápěčské signály</li>
                <li>• Nouzové postupy</li>
              </ul>
            </div>
            
            <div className="p-4 bg-purple-50 rounded-xl border border-purple-100">
              <h4 className="font-semibold text-purple-900 mb-2">
                5. Mořská biologie
              </h4>
              <ul className="text-sm text-purple-700 space-y-1">
                <li>• Identifikace organismů</li>
                <li>• Chování pod vodou</li>
                <li>• Ochrana prostředí</li>
                <li>• Interakce s mořským životem</li>
              </ul>
            </div>
            
            <div className="p-4 bg-rose-50 rounded-xl border border-rose-100">
              <h4 className="font-semibold text-rose-900 mb-2">
                6. Navigace pod vodou
              </h4>
              <ul className="text-sm text-rose-700 space-y-1">
                <li>• Kompasová navigace</li>
                <li>• Přírodní orientační body</li>
                <li>• Plánování tras</li>
                <li>• Návrat na výchozí bod</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Doporučení pro přípravu */}
      <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-8 border border-green-100">
        <div className="flex items-center gap-3 mb-6">
          <AlertTriangle className="w-6 h-6 text-green-600" />
          <h3 className="text-2xl font-semibold text-gray-900">
            Doporučení pro přípravu
          </h3>
        </div>
        
        <div className="grid md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="p-4 bg-white rounded-xl shadow-sm mb-4">
              <BookOpen className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <h4 className="font-semibold text-gray-900 mb-2">Studuj teorii</h4>
              <p className="text-sm text-gray-600">
                Prostuduj fyziku a fyziologii potápění, bezpečnostní postupy
              </p>
            </div>
          </div>
          
          <div className="text-center">
            <div className="p-4 bg-white rounded-xl shadow-sm mb-4">
              <Gauge className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <h4 className="font-semibold text-gray-900 mb-2">Praktické ponory</h4>
              <p className="text-sm text-gray-600">
                Absolvuj povinné ponory v bazénu i v otevřené vodě
              </p>
            </div>
          </div>
          
          <div className="text-center">
            <div className="p-4 bg-white rounded-xl shadow-sm mb-4">
              <Target className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <h4 className="font-semibold text-gray-900 mb-2">Procvič dovednosti</h4>
              <p className="text-sm text-gray-600">
                Trénuj základní dovednosti jako vyrovnávání tlaku a navigaci
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
