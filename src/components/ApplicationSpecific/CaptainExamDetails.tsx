import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  BookOpen,
  Clock,
  CheckCircle,
  AlertTriangle,
  FileText,
  GraduationCap,
  Star,
  Target,
  Waves,
  Compass,
  Shield,
  MapPin,
  Radio,
  Thermometer,
  Award,
  Calendar,
  Info,
  ExternalLink,
  Heart,
  CreditCard
} from "lucide-react";

export const CaptainExamDetails = () => {
  return (
    <div className="space-y-12">
      {/* Hlavní <PERSON> */}
      <div className="bg-gradient-to-br from-blue-50 to-cyan-50 rounded-3xl p-8 border border-blue-100">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-3 bg-blue-100 rounded-xl">
            <Anchor className="w-8 h-8 text-blue-600" />
          </div>
          <div>
            <h2 className="text-3xl font-bold text-gray-900">
              Kapitánské zkoušky a prů<PERSON>zy způsobilosti
            </h2>
            <div className="flex items-center gap-2 mt-2">
              <div className="flex items-center gap-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <span className="text-sm text-gray-600">Podle zákona č. 114/1995 Sb.</span>
            </div>
          </div>
        </div>
        <p className="text-lg text-gray-700 leading-relaxed">
          Připravte se na získání průkazu způsobilosti vedení malého plavidla nebo velkého plavidla
          podle platné legislativy ČR. Všechny materiály jsou aktualizovány podle nejnovějších
          předpisů Státní plavební správy pro rok {new Date().getFullYear()}.
        </p>
      </div>

      {/* Vůdce malého a rekreačního plavidla */}
      <div className="bg-gradient-to-br from-slate-50 to-gray-50 rounded-2xl p-8 border border-slate-200">
        <div className="flex items-center gap-3 mb-8">
          <div className="p-3 bg-slate-100 rounded-xl">
            <Anchor className="w-6 h-6 text-slate-600" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-900">
            Vůdce malého a rekreačního plavidla
          </h3>
        </div>

        <div className="prose prose-slate max-w-none">
          <p className="text-slate-700 leading-relaxed mb-6">
            Vůdce malého plavidla je osoba způsobilá vést malé plavidlo nebo rekreační plavidlo.
            Způsobilost se prokazuje průkazem způsobilosti, který vydává Státní plavební správa
            na základě úspěšně vykonané zkoušky nebo uznání způsobilosti získané v jiném státě.
          </p>

          <div className="bg-white rounded-xl p-6 border border-slate-200">
            <h4 className="font-semibold text-slate-900 mb-4 flex items-center gap-2">
              <Info className="w-5 h-5 text-slate-600" />
              Klíčové informace
            </h4>
            <div className="space-y-3">
              {[
                "Průkaz způsobilosti je vyžadován pro vedení všech kategorií plavidel",
                "Platnost průkazu je časově neomezená",
                "Průkaz je uznáván v rámci České republiky",
                "Pro některé kategorie je nutné ověření praktických dovedností"
              ].map((item, index) => (
                <div key={index} className="flex items-start gap-3">
                  <CheckCircle className="w-4 h-4 text-slate-500 mt-0.5 flex-shrink-0" />
                  <span className="text-slate-700 text-sm">{item}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Předkládané doklady a náležitosti */}
      <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
        <div className="flex items-center gap-3 mb-8">
          <FileText className="w-6 h-6 text-indigo-600" />
          <h3 className="text-2xl font-semibold text-gray-900">
            Předkládané doklady a náležitosti
          </h3>
        </div>

        <div className="space-y-8">
          {/* Při podání žádosti */}
          <div className="space-y-4">
            <h4 className="font-semibold text-gray-900 text-lg flex items-center gap-2">
              <Calendar className="w-5 h-5 text-indigo-600" />
              Při podání žádosti o zkoušku:
            </h4>
            <div className="grid md:grid-cols-2 gap-4">
              {[
                "Vyplněná žádost o vydání plavebního dokladu",
                "Lékařský posudek o zdravotní způsobilosti (ne starší než 3 měsíce)",
                "Osvědčení o úspěšném složení praktické zkoušky (pro kat. M, S, M24) - platnost 2 roky",
                "Správní poplatek 500,- Kč (při elektronickém podání 400,- Kč)"
              ].map((item, index) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-indigo-50 rounded-lg">
                  <div className="w-2 h-2 bg-indigo-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-indigo-800 text-sm font-medium">{item}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Při zkoušce */}
          <div className="space-y-4">
            <h4 className="font-semibold text-gray-900 text-lg flex items-center gap-2">
              <GraduationCap className="w-5 h-5 text-purple-600" />
              Při zkoušce:
            </h4>
            <div className="grid md:grid-cols-2 gap-4">
              {[
                "Doklad totožnosti (občanský průkaz nebo pas)",
                "Potvrzení o přidělení termínu zkoušky"
              ].map((item, index) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-purple-50 rounded-lg">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-purple-800 text-sm font-medium">{item}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Při vydání průkazu */}
          <div className="space-y-4">
            <h4 className="font-semibold text-gray-900 text-lg flex items-center gap-2">
              <Award className="w-5 h-5 text-emerald-600" />
              Při vydání průkazu (po úspěšné zkoušce):
            </h4>
            <div className="bg-emerald-50 rounded-lg p-4">
              <p className="text-emerald-800 text-sm font-medium">
                Průkazy jsou vydávány v den vykonání zkoušky, co možná nejdříve po jejím dokončení.
                Z kapacitních důvodů počítejte s určitou prodlevou pro převzetí průkazu.
              </p>
            </div>
          </div>
        </div>

        <div className="mt-8 space-y-4">
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-start gap-3">
              <Info className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h5 className="font-semibold text-blue-800 mb-1">Elektronické podání</h5>
                <p className="text-blue-700 text-sm">
                  Žádost lze podat elektronicky přes Portál dopravy se slevou 20% na správním poplatku (400,- Kč místo 500,- Kč).
                </p>
              </div>
            </div>
          </div>

          <div className="p-4 bg-amber-50 rounded-lg border border-amber-200">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
              <div>
                <h5 className="font-semibold text-amber-800 mb-1">Důležité lhůty</h5>
                <p className="text-amber-700 text-sm">
                  Žádost musí být podána nejpozději 10 pracovních dnů před požadovaným termínem zkoušky.
                  Praktická zkouška musí být složena před dnem podání žádosti o udělení oprávnění.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Pro oprávnění vůdce malého a rekreačního plavidla jsou stanoveny následující kategorie */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-200">
        <div className="flex items-center gap-3 mb-8">
          <div className="p-3 bg-blue-100 rounded-xl">
            <Compass className="w-6 h-6 text-blue-600" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-900">
            Pro oprávnění vůdce malého a rekreačního plavidla jsou stanoveny následující kategorie
          </h3>
        </div>

        <div className="grid gap-6">
          <div className="bg-white rounded-xl p-6 border border-blue-200">
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-bold text-blue-900 text-lg mb-4">Kategorie pro malá plavidla:</h4>
                <div className="space-y-3">
                  {[
                    { code: "M20", desc: "Pro plavidla s motorem do 20 kW" },
                    { code: "M", desc: "Pro plavidla s motorem bez omezení výkonu" },
                    { code: "S20", desc: "Pro plachetnice s plachtami do 20 m²" },
                    { code: "S", desc: "Pro plachetnice bez omezení plochy plachet" }
                  ].map((item, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                      <div className="px-3 py-1 bg-blue-500 text-white rounded-md font-bold text-sm">
                        {item.code}
                      </div>
                      <span className="text-blue-800 text-sm font-medium">{item.desc}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-bold text-indigo-900 text-lg mb-4">Speciální kategorie:</h4>
                <div className="space-y-3">
                  <div className="p-4 bg-indigo-50 rounded-lg border border-indigo-200">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="px-3 py-1 bg-indigo-500 text-white rounded-md font-bold text-sm">
                        M24
                      </div>
                      <span className="font-semibold text-indigo-900">Rekreační plavidla</span>
                    </div>
                    <p className="text-indigo-700 text-sm">
                      Pro rekreační plavidla s délkou trupu do 24 metrů.
                      Vyžaduje předchozí vlastnictví průkazu kategorie M nebo M a S.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-blue-100 to-indigo-100 rounded-lg p-6">
            <h4 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Pravidla kombinace kategorií
            </h4>
            <div className="text-blue-800 text-sm space-y-2">
              <p>• Kategorie M a S lze získat současně v rámci jedné zkoušky</p>
              <p>• Kategorie M20 a S20 jsou zjednodušené varianty s omezeními</p>
              <p>• Pro kategorii M24 je nutné nejprve získat kategorii M nebo M a S</p>
              <p>• Každá kategorie má specifické požadavky na věk a praktické ověření</p>
            </div>
          </div>
        </div>
      </div>

      {/* Zkouška */}
      <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
        <div className="flex items-center gap-3 mb-8">
          <GraduationCap className="w-6 h-6 text-purple-600" />
          <h3 className="text-2xl font-semibold text-gray-900">
            Zkouška
          </h3>
        </div>

        <div className="space-y-6">
          <div className="prose prose-gray max-w-none">
            <p className="text-gray-700 leading-relaxed mb-6">
              Zkouška způsobilosti se skládá z teoretické části a u některých kategorií také
              z ověření praktických dovedností. Zkoušky se konají podle rozvrhu stanoveného
              Státní plavební správou.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-gradient-to-br from-purple-50 to-violet-50 rounded-xl p-6 border border-purple-200">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <BookOpen className="w-5 h-5 text-purple-600" />
                </div>
                <h4 className="font-semibold text-purple-900">Teoretická část</h4>
              </div>
              <div className="space-y-2 text-sm text-purple-800">
                <p>• Písemný test z předpisů</p>
                <p>• Navigace a mapy</p>
                <p>• Bezpečnost plavby</p>
                <p>• Meteorologie</p>
              </div>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 border border-green-200">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Waves className="w-5 h-5 text-green-600" />
                </div>
                <h4 className="font-semibold text-green-900">Praktická část</h4>
              </div>
              <div className="space-y-2 text-sm text-green-800">
                <p>• Manévrování s plavidlem</p>
                <p>• Používání záchranných prostředků</p>
                <p>• VHF radiokomunikace</p>
                <p>• Nouzové postupy</p>
              </div>
            </div>

            <div className="bg-gradient-to-br from-amber-50 to-orange-50 rounded-xl p-6 border border-amber-200">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-amber-100 rounded-lg">
                  <Clock className="w-5 h-5 text-amber-600" />
                </div>
                <h4 className="font-semibold text-amber-900">Časový harmonogram</h4>
              </div>
              <div className="space-y-2 text-sm text-amber-800">
                <p>• Přihlášení: 30 dní předem</p>
                <p>• Teoretická zkouška: dle kategorije</p>
                <p>• Praktické ověření: individuálně</p>
                <p>• Vydání průkazu: do 30 dní</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Písemný test - přesné informace podle SPS */}
      <div className="bg-gradient-to-br from-indigo-50 to-blue-50 rounded-2xl p-8 border border-indigo-200">
        <div className="flex items-center gap-3 mb-8">
          <div className="p-3 bg-indigo-100 rounded-xl">
            <FileText className="w-6 h-6 text-indigo-600" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-900">
            Písemný test
          </h3>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <div className="bg-white rounded-xl p-6 border border-indigo-200">
              <h4 className="font-semibold text-indigo-900 mb-4">Obsah testu podle kategorií (dle SPS):</h4>
              <div className="space-y-4">
                {[
                  {
                    category: "M a M20",
                    questions: "35 otázek",
                    time: "30 minut",
                    success: "nejméně 30 bodů",
                    topics: "Plavební provoz, základy techniky, základy první pomoci"
                  },
                  {
                    category: "S a S20",
                    questions: "14 otázek",
                    time: "10 minut",
                    success: "nejméně 11 bodů",
                    topics: "Konstrukce plachetnic, teorie plavby plachetnic, technika plachtění"
                  },
                  {
                    category: "C (příbřežní)",
                    questions: "28 otázek",
                    time: "25 minut",
                    success: "nejméně 24 bodů",
                    topics: "Mezinárodní námořní právo, navigace, meteorologie, bezpečnost na moři"
                  }
                ].map((item, index) => (
                  <div key={index} className="p-4 bg-indigo-50 rounded-lg">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="px-2 py-1 bg-indigo-500 text-white rounded text-sm font-bold">
                        {item.category}
                      </div>
                      <span className="font-medium text-indigo-900">{item.questions}</span>
                      <span className="text-indigo-600 text-sm">({item.time})</span>
                    </div>
                    <p className="text-indigo-700 text-sm mb-1">{item.topics}</p>
                    <p className="text-indigo-600 text-xs font-medium">Pro úspěch: {item.success}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="bg-white rounded-xl p-6 border border-indigo-200">
              <h4 className="font-semibold text-indigo-900 mb-4 flex items-center gap-2">
                <Target className="w-5 h-5" />
                Hodnocení a průběh:
              </h4>
              <div className="space-y-3">
                {[
                  "Každá otázka nabízí 3 odpovědi (a, b, c), jen jedna je správná",
                  "Za každou správnou odpověď je přidělen 1 bod",
                  "Test se provádí pomocí počítače",
                  "Výsledek je znám okamžitě po dokončení",
                  "Možnost opakování v následném zkušebním termínu"
                ].map((item, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <CheckCircle className="w-4 h-4 text-indigo-500 mt-0.5 flex-shrink-0" />
                    <span className="text-indigo-800 text-sm">{item}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-gradient-to-r from-indigo-100 to-blue-100 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Thermometer className="w-4 h-4 text-indigo-600" />
                <span className="font-semibold text-indigo-900 text-sm">Tip pro úspěch</span>
              </div>
              <p className="text-indigo-800 text-sm">
                Zkouška musí být úspěšně složena do 1 roku od podání žádosti.
                Doporučujeme důkladnou přípravu ze všech oblastí podle aktuálních předpisů SPS.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Postup v případě neúspěchu */}
      <div className="bg-gradient-to-br from-red-50 to-rose-50 rounded-2xl p-8 border border-red-200">
        <div className="flex items-center gap-3 mb-8">
          <div className="p-3 bg-red-100 rounded-xl">
            <AlertTriangle className="w-6 h-6 text-red-600" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-900">
            Postup v případě neúspěchu
          </h3>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <div className="bg-white rounded-xl p-6 border border-red-200">
              <h4 className="font-semibold text-red-900 mb-4 flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Časové lhůty pro opakování:
              </h4>
              <div className="space-y-3">
                {[
                  { title: "Teoretická zkouška", desc: "Opakování možné nejdříve za 7 dní", icon: BookOpen },
                  { title: "Praktické ověření", desc: "Opakování možné nejdříve za 14 dní", icon: Waves },
                  { title: "Celková zkouška", desc: "Bez časového omezení počtu opakování", icon: Target }
                ].map((item, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-red-50 rounded-lg">
                    <div className="p-2 bg-red-100 rounded-lg">
                      <item.icon className="w-4 h-4 text-red-600" />
                    </div>
                    <div>
                      <h5 className="font-medium text-red-900 text-sm">{item.title}</h5>
                      <p className="text-red-700 text-xs mt-1">{item.desc}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="bg-white rounded-xl p-6 border border-red-200">
              <h4 className="font-semibold text-red-900 mb-4 flex items-center gap-2">
                <Info className="w-5 h-5" />
                Doporučený postup:
              </h4>
              <div className="space-y-3">
                {[
                  "Analyzujte oblasti, ve kterých jste neuspěli",
                  "Doplňte si znalosti podle chybných odpovědí",
                  "Procvičte si problematické okruhy otázek",
                  "Využijte konzultace s odborníky",
                  "Přihlaste se k novému termínu zkoušky"
                ].map((item, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-red-600 text-xs font-bold">{index + 1}</span>
                    </div>
                    <span className="text-red-800 text-sm">{item}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-gradient-to-r from-red-100 to-rose-100 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Star className="w-4 h-4 text-red-600" />
                <span className="font-semibold text-red-900 text-sm">Motivace</span>
              </div>
              <p className="text-red-800 text-sm">
                Neúspěch při první zkoušce je normální! Většina úspěšných kapitánů
                potřebovala více pokusů. Důležité je nevzdávat se a vytrvat.
              </p>
            </div>
          </div>
        </div>

        <div className="mt-6 p-4 bg-amber-50 rounded-lg border border-amber-200">
          <div className="flex items-start gap-3">
            <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
            <div>
              <h5 className="font-semibold text-amber-800 mb-1">Finanční náklady</h5>
              <p className="text-amber-700 text-sm">
                Za každé opakování zkoušky se platí nový správní poplatek.
                Teoretická zkouška a praktické ověření se hradí zvlášť.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Věkové požadavky a platnost průkazů */}
      <div className="bg-gradient-to-br from-teal-50 to-cyan-50 rounded-2xl p-8 border border-teal-200">
        <div className="flex items-center gap-3 mb-8">
          <div className="p-3 bg-teal-100 rounded-xl">
            <Calendar className="w-6 h-6 text-teal-600" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-900">
            Věkové požadavky a platnost průkazů
          </h3>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <div className="bg-white rounded-xl p-6 border border-teal-200">
              <h4 className="font-semibold text-teal-900 mb-4">Minimální věk pro jednotlivé kategorie:</h4>
              <div className="space-y-3">
                {[
                  { category: "M20, M, S20, S", age: "16 let", note: "Základní kategorie" },
                  { category: "M24", age: "18 let", note: "Rekreační plavidla do 24 m" }
                ].map((item, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 bg-teal-50 rounded-lg">
                    <div className="px-3 py-1 bg-teal-500 text-white rounded-md font-bold text-sm">
                      {item.category}
                    </div>
                    <div className="flex-1">
                      <span className="font-medium text-teal-900">{item.age}</span>
                      <p className="text-teal-700 text-xs">{item.note}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="bg-white rounded-xl p-6 border border-teal-200">
              <h4 className="font-semibold text-teal-900 mb-4">Platnost průkazů (od 1.3.2023):</h4>
              <div className="space-y-3">
                {[
                  "Do 65 let věku - bez omezení",
                  "65-70 let - nutný lékařský posudek",
                  "Nad 70 let - posudek platný 2 roky"
                ].map((item, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <CheckCircle className="w-4 h-4 text-teal-500 mt-0.5 flex-shrink-0" />
                    <span className="text-teal-800 text-sm">{item}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-gradient-to-r from-teal-100 to-cyan-100 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Info className="w-4 h-4 text-teal-600" />
                <span className="font-semibold text-teal-900 text-sm">Starší průkazy</span>
              </div>
              <p className="text-teal-800 text-sm">
                Průkazy vydané do 28.2.2023 zůstávají v platnosti bez věkového omezení,
                ale po dosažení věkové hranice je nutné dokládat zdravotní způsobilost.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Průvodce přípravou */}
      <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-8 border border-green-100">
        <div className="flex items-center gap-3 mb-8">
          <div className="p-2 bg-green-100 rounded-xl">
            <BookOpen className="w-6 h-6 text-green-600" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-900">
            Doporučený postup přípravy
          </h3>
        </div>

        <div className="grid md:grid-cols-3 gap-6">
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-green-100 rounded-xl">
                <BookOpen className="w-6 h-6 text-green-600" />
              </div>
              <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                4-6 týdnů
              </div>
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">Studium předpisů</h4>
            <p className="text-sm text-gray-600">
              Prostuduj plavební předpisy, navigaci a bezpečnostní postupy podle SPS
            </p>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-green-100 rounded-xl">
                <Target className="w-6 h-6 text-green-600" />
              </div>
              <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                2-3 týdny
              </div>
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">Testování znalostí</h4>
            <p className="text-sm text-gray-600">
              Procvič si testové otázky pro svou kategorii průkazu
            </p>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-green-100 rounded-xl">
                <Waves className="w-6 h-6 text-green-600" />
              </div>
              <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                Průběžně
              </div>
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">Praktické zkušenosti</h4>
            <p className="text-sm text-gray-600">
              Získávaj praxi na vodě pod dohledem zkušeného vodáka
            </p>
          </div>
        </div>
      </div>

      {/* Užitečné odkazy a zdroje */}
      <div className="bg-gradient-to-br from-slate-50 to-gray-50 rounded-2xl p-8 border border-slate-200">
        <div className="flex items-center gap-3 mb-8">
          <div className="p-3 bg-slate-100 rounded-xl">
            <ExternalLink className="w-6 h-6 text-slate-600" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-900">
            Oficiální zdroje a užitečné odkazy
          </h3>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h4 className="font-semibold text-slate-900 text-lg">Státní plavební správa:</h4>
            <div className="space-y-3">
              {[
                {
                  title: "Termíny zkoušek",
                  url: "https://terminy-zkousek.plavebniurad.cz/index.asp",
                  desc: "Vyhledání a rezervace termínů zkoušek"
                },
                {
                  title: "Zkoušková aplikace",
                  url: "http://www.spspraha.cz/zkousky/zkousky.aspx",
                  desc: "Procvičování testových otázek online"
                },
                {
                  title: "Elektronické žádosti",
                  url: "https://sps.gov.cz/dok-os/el-podani-prehled",
                  desc: "Podání žádosti přes Portál dopravy"
                },
                {
                  title: "Průkazy k převzetí",
                  url: "https://vydane-prukazy.plavebniurad.cz",
                  desc: "Kontrola stavu připravených průkazů"
                }
              ].map((item, index) => (
                <div key={index} className="p-3 bg-white rounded-lg border border-slate-200 hover:border-slate-300 transition-colors">
                  <div className="flex items-start gap-3">
                    <ExternalLink className="w-4 h-4 text-slate-500 mt-1 flex-shrink-0" />
                    <div>
                      <h5 className="font-medium text-slate-900 text-sm">{item.title}</h5>
                      <p className="text-slate-600 text-xs mt-1">{item.desc}</p>
                      <p className="text-slate-400 text-xs mt-1 font-mono">{item.url}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="font-semibold text-slate-900 text-lg">Formuláře a dokumenty:</h4>
            <div className="space-y-3">
              {[
                {
                  title: "Žádost o vydání průkazu",
                  desc: "Formulář pro podání žádosti (PDF)"
                },
                {
                  title: "Lékařský posudek",
                  desc: "Formulář zdravotní způsobilosti"
                },
                {
                  title: "Seznam pověřených osob",
                  desc: "Pro praktické zkoušky"
                },
                {
                  title: "Správní poplatky",
                  desc: "Aktuální sazby poplatků"
                }
              ].map((item, index) => (
                <div key={index} className="p-3 bg-white rounded-lg border border-slate-200">
                  <div className="flex items-start gap-3">
                    <FileText className="w-4 h-4 text-slate-500 mt-1 flex-shrink-0" />
                    <div>
                      <h5 className="font-medium text-slate-900 text-sm">{item.title}</h5>
                      <p className="text-slate-600 text-xs mt-1">{item.desc}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-start gap-3">
            <Info className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div>
              <h5 className="font-semibold text-blue-800 mb-1">Aktuální informace</h5>
              <p className="text-blue-700 text-sm">
                Všechny informace jsou aktualizovány podle platných předpisů Státní plavební správy.
                Pro nejnovější změny vždy navštivte oficiální stránky sps.gov.cz.
              </p>
            </div>
          </div>
        </div>
      </div>

    </div>
  );
};
