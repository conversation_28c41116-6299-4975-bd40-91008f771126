"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  CheckCircle,
  AlertTriangle,
  FileText,
  GraduationCap,
  Star,
  Target,
  Waves,
  Compass,
  Shield,
  Award,
  Calendar,
  Info,
  ExternalLink,
  MapPin,
  Users,
  CreditCard
} from "lucide-react";

// Import sub-components
import { CaptainExamOverview } from "./Captain/CaptainExamOverview";
import { CaptainExamProcess } from "./Captain/CaptainExamProcess";
import { CaptainExamCategories } from "./Captain/CaptainExamCategories";
import { CaptainExamTest } from "./Captain/CaptainExamTest";
import { CaptainExamDocuments } from "./Captain/CaptainExamDocuments";
import { CaptainExamResources } from "./Captain/CaptainExamResources";

type TabType = 'overview' | 'process' | 'categories' | 'test' | 'documents' | 'resources';

const tabs = [
  { id: 'overview' as TabType, label: 'Přehled', icon: Info },
  { id: 'process' as TabType, label: 'Proces', icon: MapPin },
  { id: 'categories' as TabType, label: 'Kate<PERSON>ie', icon: Compass },
  { id: 'test' as TabType, label: 'Zkouška', icon: GraduationCap },
  { id: 'documents' as TabType, label: 'Doklady', icon: FileText },
  { id: 'resources' as TabType, label: 'Zdroje', icon: ExternalLink },
];

export const CaptainExamDetails = () => {
  const [activeTab, setActiveTab] = useState<TabType>('overview');

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <CaptainExamOverview />;
      case 'process':
        return <CaptainExamProcess />;
      case 'categories':
        return <CaptainExamCategories />;
      case 'test':
        return <CaptainExamTest />;
      case 'documents':
        return <CaptainExamDocuments />;
      case 'resources':
        return <CaptainExamResources />;
      default:
        return <CaptainExamOverview />;
    }
  };

  return (
    <div className="space-y-8">
      {/* Hlavní hero sekce */}
      <div className="bg-gradient-to-br from-blue-50 to-cyan-50 rounded-3xl p-8 border border-blue-100">
        <div className="flex items-center gap-4 mb-6">
          <div className="p-4 bg-blue-100 rounded-2xl">
            <Anchor className="w-10 h-10 text-blue-600" />
          </div>
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              Kapitánské zkoušky
            </h1>
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <span className="text-sm text-gray-600">Podle zákona č. 114/1995 Sb.</span>
            </div>
          </div>
        </div>

        <p className="text-xl text-gray-700 leading-relaxed mb-6">
          Kompletní průvodce získáním průkazu způsobilosti vedení malého plavidla nebo rekreačního plavidla
          podle platné legislativy ČR.
        </p>

        {/* Quick stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">6</div>
            <div className="text-sm text-gray-600">Kategorií průkazů</div>
          </div>
          <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">16+</div>
            <div className="text-sm text-gray-600">Minimální věk</div>
          </div>
          <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">500 Kč</div>
            <div className="text-sm text-gray-600">Správní poplatek</div>
          </div>
          <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">35</div>
            <div className="text-sm text-gray-600">Otázek v testu</div>
          </div>
        </div>
      </div>

      {/* Navigation tabs */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="flex overflow-x-auto">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-6 py-4 text-sm font-medium whitespace-nowrap transition-colors ${
                    activeTab === tab.id
                      ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab content */}
        <div className="p-8">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

      <div className="bg-gradient-to-br from-red-50 to-rose-50 rounded-2xl p-8 border border-red-200">
        <div className="flex items-center gap-3 mb-8">
          <div className="p-3 bg-red-100 rounded-xl">
            <AlertTriangle className="w-6 h-6 text-red-600" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-900">
            Postup v případě neúspěchu
          </h3>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <div className="bg-white rounded-xl p-6 border border-red-200">
              <h4 className="font-semibold text-red-900 mb-4 flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Časové lhůty pro opakování:
              </h4>
              <div className="space-y-3">
                {[
                  { title: "Teoretická zkouška", desc: "Opakování možné nejdříve za 7 dní", icon: BookOpen },
                  { title: "Praktické ověření", desc: "Opakování možné nejdříve za 14 dní", icon: Waves },
                  { title: "Celková zkouška", desc: "Bez časového omezení počtu opakování", icon: Target }
                ].map((item, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-red-50 rounded-lg">
                    <div className="p-2 bg-red-100 rounded-lg">
                      <item.icon className="w-4 h-4 text-red-600" />
                    </div>
                    <div>
                      <h5 className="font-medium text-red-900 text-sm">{item.title}</h5>
                      <p className="text-red-700 text-xs mt-1">{item.desc}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="bg-white rounded-xl p-6 border border-red-200">
              <h4 className="font-semibold text-red-900 mb-4 flex items-center gap-2">
                <Info className="w-5 h-5" />
                Doporučený postup:
              </h4>
              <div className="space-y-3">
                {[
                  "Analyzujte oblasti, ve kterých jste neuspěli",
                  "Doplňte si znalosti podle chybných odpovědí",
                  "Procvičte si problematické okruhy otázek",
                  "Využijte konzultace s odborníky",
                  "Přihlaste se k novému termínu zkoušky"
                ].map((item, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-red-600 text-xs font-bold">{index + 1}</span>
                    </div>
                    <span className="text-red-800 text-sm">{item}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-gradient-to-r from-red-100 to-rose-100 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Star className="w-4 h-4 text-red-600" />
                <span className="font-semibold text-red-900 text-sm">Motivace</span>
              </div>
              <p className="text-red-800 text-sm">
                Neúspěch při první zkoušce je normální! Většina úspěšných kapitánů
                potřebovala více pokusů. Důležité je nevzdávat se a vytrvat.
              </p>
            </div>
          </div>
        </div>

        <div className="mt-6 p-4 bg-amber-50 rounded-lg border border-amber-200">
          <div className="flex items-start gap-3">
            <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
            <div>
              <h5 className="font-semibold text-amber-800 mb-1">Finanční náklady</h5>
              <p className="text-amber-700 text-sm">
                Za každé opakování zkoušky se platí nový správní poplatek.
                Teoretická zkouška a praktické ověření se hradí zvlášť.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Věkové požadavky a platnost průkazů */}
      <div className="bg-gradient-to-br from-teal-50 to-cyan-50 rounded-2xl p-8 border border-teal-200">
        <div className="flex items-center gap-3 mb-8">
          <div className="p-3 bg-teal-100 rounded-xl">
            <Calendar className="w-6 h-6 text-teal-600" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-900">
            Věkové požadavky a platnost průkazů
          </h3>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <div className="bg-white rounded-xl p-6 border border-teal-200">
              <h4 className="font-semibold text-teal-900 mb-4">Minimální věk pro jednotlivé kategorie:</h4>
              <div className="space-y-3">
                {[
                  { category: "M20, M, S20, S", age: "16 let", note: "Základní kategorie" },
                  { category: "M24", age: "18 let", note: "Rekreační plavidla do 24 m" }
                ].map((item, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 bg-teal-50 rounded-lg">
                    <div className="px-3 py-1 bg-teal-500 text-white rounded-md font-bold text-sm">
                      {item.category}
                    </div>
                    <div className="flex-1">
                      <span className="font-medium text-teal-900">{item.age}</span>
                      <p className="text-teal-700 text-xs">{item.note}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="bg-white rounded-xl p-6 border border-teal-200">
              <h4 className="font-semibold text-teal-900 mb-4">Platnost průkazů (od 1.3.2023):</h4>
              <div className="space-y-3">
                {[
                  "Do 65 let věku - bez omezení",
                  "65-70 let - nutný lékařský posudek",
                  "Nad 70 let - posudek platný 2 roky"
                ].map((item, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <CheckCircle className="w-4 h-4 text-teal-500 mt-0.5 flex-shrink-0" />
                    <span className="text-teal-800 text-sm">{item}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-gradient-to-r from-teal-100 to-cyan-100 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Info className="w-4 h-4 text-teal-600" />
                <span className="font-semibold text-teal-900 text-sm">Starší průkazy</span>
              </div>
              <p className="text-teal-800 text-sm">
                Průkazy vydané do 28.2.2023 zůstávají v platnosti bez věkového omezení,
                ale po dosažení věkové hranice je nutné dokládat zdravotní způsobilost.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Průvodce přípravou */}
      <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-8 border border-green-100">
        <div className="flex items-center gap-3 mb-8">
          <div className="p-2 bg-green-100 rounded-xl">
            <BookOpen className="w-6 h-6 text-green-600" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-900">
            Doporučený postup přípravy
          </h3>
        </div>

        <div className="grid md:grid-cols-3 gap-6">
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-green-100 rounded-xl">
                <BookOpen className="w-6 h-6 text-green-600" />
              </div>
              <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                4-6 týdnů
              </div>
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">Studium předpisů</h4>
            <p className="text-sm text-gray-600">
              Prostuduj plavební předpisy, navigaci a bezpečnostní postupy podle SPS
            </p>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-green-100 rounded-xl">
                <Target className="w-6 h-6 text-green-600" />
              </div>
              <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                2-3 týdny
              </div>
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">Testování znalostí</h4>
            <p className="text-sm text-gray-600">
              Procvič si testové otázky pro svou kategorii průkazu
            </p>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-green-100 rounded-xl">
                <Waves className="w-6 h-6 text-green-600" />
              </div>
              <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                Průběžně
              </div>
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">Praktické zkušenosti</h4>
            <p className="text-sm text-gray-600">
              Získávaj praxi na vodě pod dohledem zkušeného vodáka
            </p>
          </div>
        </div>
      </div>

      {/* Užitečné odkazy a zdroje */}
      <div className="bg-gradient-to-br from-slate-50 to-gray-50 rounded-2xl p-8 border border-slate-200">
        <div className="flex items-center gap-3 mb-8">
          <div className="p-3 bg-slate-100 rounded-xl">
            <ExternalLink className="w-6 h-6 text-slate-600" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-900">
            Oficiální zdroje a užitečné odkazy
          </h3>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h4 className="font-semibold text-slate-900 text-lg">Státní plavební správa:</h4>
            <div className="space-y-3">
              {[
                {
                  title: "Termíny zkoušek",
                  url: "https://terminy-zkousek.plavebniurad.cz/index.asp",
                  desc: "Vyhledání a rezervace termínů zkoušek"
                },
                {
                  title: "Zkoušková aplikace",
                  url: "http://www.spspraha.cz/zkousky/zkousky.aspx",
                  desc: "Procvičování testových otázek online"
                },
                {
                  title: "Elektronické žádosti",
                  url: "https://sps.gov.cz/dok-os/el-podani-prehled",
                  desc: "Podání žádosti přes Portál dopravy"
                },
                {
                  title: "Průkazy k převzetí",
                  url: "https://vydane-prukazy.plavebniurad.cz",
                  desc: "Kontrola stavu připravených průkazů"
                }
              ].map((item, index) => (
                <div key={index} className="p-3 bg-white rounded-lg border border-slate-200 hover:border-slate-300 transition-colors">
                  <div className="flex items-start gap-3">
                    <ExternalLink className="w-4 h-4 text-slate-500 mt-1 flex-shrink-0" />
                    <div>
                      <h5 className="font-medium text-slate-900 text-sm">{item.title}</h5>
                      <p className="text-slate-600 text-xs mt-1">{item.desc}</p>
                      <p className="text-slate-400 text-xs mt-1 font-mono">{item.url}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="font-semibold text-slate-900 text-lg">Formuláře a dokumenty:</h4>
            <div className="space-y-3">
              {[
                {
                  title: "Žádost o vydání průkazu",
                  desc: "Formulář pro podání žádosti (PDF)"
                },
                {
                  title: "Lékařský posudek",
                  desc: "Formulář zdravotní způsobilosti"
                },
                {
                  title: "Seznam pověřených osob",
                  desc: "Pro praktické zkoušky"
                },
                {
                  title: "Správní poplatky",
                  desc: "Aktuální sazby poplatků"
                }
              ].map((item, index) => (
                <div key={index} className="p-3 bg-white rounded-lg border border-slate-200">
                  <div className="flex items-start gap-3">
                    <FileText className="w-4 h-4 text-slate-500 mt-1 flex-shrink-0" />
                    <div>
                      <h5 className="font-medium text-slate-900 text-sm">{item.title}</h5>
                      <p className="text-slate-600 text-xs mt-1">{item.desc}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-start gap-3">
            <Info className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div>
              <h5 className="font-semibold text-blue-800 mb-1">Aktuální informace</h5>
              <p className="text-blue-700 text-sm">
                Všechny informace jsou aktualizovány podle platných předpisů Státní plavební správy.
                Pro nejnovější změny vždy navštivte oficiální stránky sps.gov.cz.
              </p>
            </div>
          </div>
        </div>
      </div>

    </div>
  );
};
