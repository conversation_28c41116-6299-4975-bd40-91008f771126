"use client";
import React, { useState, useEffect } from "react";
import {
  Car,
  Users,
  BookOpen,
  Clock,
  CheckCircle,
  AlertTriangle,
  FileText,
  GraduationCap,
  Star,
  Target,
  Navigation,
  Truck,
  Shield,
  ChevronLeft,
  ChevronRight,
  ArrowRight,
  Award,
  Sparkles,
  BarChart,
  Lightbulb
} from "lucide-react";

export const DrivingSchoolDetails = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [activeTab, setActiveTab] = useState(0);

  // Automatické přepínání slidů
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Slider data
  const slides = [
    {
      image: "/autoskola/screens/driving-test.jpg",
      title: "Aktuální testové otázky 2025",
      description: "Přes 1500 otázek podle nejnověj<PERSON><PERSON><PERSON> před<PERSON> s podrobnými vysvětleními a obrázky"
    },
    {
      image: "/autoskola/screens/practical-lessons.jpg",
      title: "Interaktivní simulace jízdy",
      description: "Realistické dopravní situace - připrav se na praktickou zkoušku efektivně"
    },
    {
      image: "/autoskola/screens/success-rate.jpg",
      title: "Úspěšnost 94% na první pokus",
      description: "Díky našemu systému složí většina studentů zkoušku hned napoprvé"
    }
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Hero sekce s hlavním bannerem */}
      <div className="bg-white rounded-2xl shadow-sm overflow-hidden mb-16">
        <div className="relative h-[400px] md:h-[500px]">
          {/* Hlavní obrázek */}
          <div
            className="absolute inset-0 bg-cover bg-center transition-all duration-700"
            style={{
              backgroundImage: `url('https://images.unsplash.com/photo-${
                currentSlide === 0 ? '1449824913935-594fb77bd1c2' : // driving instructor
                currentSlide === 1 ? '1544725176-6c5b008b1b4b' : // car interior
                '1533473359331-0135ef1b58bf' // success celebration
              }?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80')`
            }}
          >
            {/* Překrytí obrázku */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-900/80 to-slate-800/70"></div>

            {/* Obsah na obrázku */}
            <div className="absolute inset-0 flex flex-col justify-center px-8 md:px-16 max-w-3xl">
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">
                {slides[currentSlide].title}
              </h1>
              <p className="text-lg md:text-xl text-white/90 mb-8">
                {slides[currentSlide].description}
              </p>
              <div className="flex flex-wrap gap-4">
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-blue-500/30">
                  Začít přípravu
                </button>
                <button className="bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white px-6 py-3 rounded-lg font-medium transition-all duration-200">
                  Více informací
                </button>
              </div>
            </div>
          </div>

          {/* Navigační šipky */}
          <div className="absolute bottom-6 right-6 flex space-x-2 z-10">
            <button
              onClick={prevSlide}
              className="bg-white/20 hover:bg-white/40 backdrop-blur-sm rounded-full p-2 transition-all duration-200"
            >
              <ChevronLeft className="w-5 h-5 text-white" />
            </button>
            <button
              onClick={nextSlide}
              className="bg-white/20 hover:bg-white/40 backdrop-blur-sm rounded-full p-2 transition-all duration-200"
            >
              <ChevronRight className="w-5 h-5 text-white" />
            </button>
          </div>

          {/* Indikátor slidů */}
          <div className="absolute bottom-6 left-6 flex space-x-2 z-10">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  index === currentSlide ? 'bg-white' : 'bg-white/40'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Informace pod sliderem */}
        <div className="p-8 md:p-10 bg-white">
          <div className="flex flex-col md:flex-row md:items-center gap-6 md:gap-10">
            <div className="flex items-center gap-4">
              <div className="p-4 bg-blue-100 rounded-xl">
                <Car className="w-8 h-8 text-blue-600" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">
                  Autoškola - Řidičský průkaz
                </h2>
                <div className="flex items-center gap-2 mt-1">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">4.9/5 hodnocení</span>
                </div>
              </div>
            </div>
            <p className="text-gray-700 md:text-lg md:ml-4 md:border-l md:border-gray-200 md:pl-10 flex-1">
              Získejte řidičský průkaz s nejmodernější výukovou platformou v České republice.
              Připravte se na teoretickou i praktickou zkoušku s aktuálními otázkami
              podle platných předpisů pro rok {new Date().getFullYear()}.
            </p>
          </div>
        </div>
      </div>

      {/* Statistiky */}
      <div className="grid md:grid-cols-3 gap-6 mb-16">
        {[
          { icon: Target, color: "blue", title: "94%", subtitle: "Úspěšnost", description: "Našich studentů úspěšně složí zkoušku na první pokus" },
          { icon: Users, color: "blue", title: "15,420+", subtitle: "Absolventů", description: "Studentů již získalo řidičský průkaz s naší pomocí" },
          { icon: BookOpen, color: "blue", title: "2,850", subtitle: "Testů denně", description: "Průměrný počet testů vyplněných každý den" }
        ].map((stat, index) => (
          <div
            key={index}
            className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
          >
            <div className="flex items-center gap-4 mb-4">
              <div className={`p-3 bg-${stat.color}-100 rounded-xl`}>
                <stat.icon className={`w-6 h-6 text-${stat.color}-600`} />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">{stat.title}</div>
                <div className="text-sm text-gray-600">{stat.subtitle}</div>
              </div>
            </div>
            <p className="text-gray-600 text-sm">{stat.description}</p>
          </div>
        ))}
      </div>

      {/* Kategorie průkazů */}
      <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100 mb-16">
        <div className="flex items-center gap-3 mb-8">
          <Navigation className="w-6 h-6 text-blue-600" />
          <h3 className="text-2xl font-semibold text-gray-900">
            Kategorie řidičských průkazů
          </h3>
        </div>

        {/* Záložky pro kategorie */}
        <div className="mb-6 border-b border-gray-200">
          <div className="flex flex-wrap -mb-px">
            {["Skupina A", "Skupina B", "Skupina C", "Skupina D"].map((tab, index) => (
              <button
                key={index}
                onClick={() => setActiveTab(index)}
                className={`inline-block py-3 px-4 border-b-2 font-medium text-sm ${
                  activeTab === index
                    ? 'border-blue-600 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab}
              </button>
            ))}
          </div>
        </div>

        {/* Obsah záložek */}
        <div className="grid md:grid-cols-2 gap-6">
          {[
            {
              title: "Skupina A",
              icon: "🏍️",
              items: [
                { name: "A1", desc: "Motocykly do 125 ccm", age: "16 let" },
                { name: "A2", desc: "Motocykly do 35 kW", age: "18 let" },
                { name: "A", desc: "Motocykly bez omezení výkonu", age: "24 let" },
                { name: "AM", desc: "Mopedy do 45 km/h", age: "15 let" }
              ],
              active: activeTab === 0
            },
            {
              title: "Skupina B",
              icon: "🚗",
              items: [
                { name: "B", desc: "Osobní automobily do 3,5t", age: "18 let" },
                { name: "B1", desc: "Čtyřkolky", age: "17 let" },
                { name: "BE", desc: "Osobní automobil s přívěsem", age: "18 let" },
                { name: "B96", desc: "Souprava do 4250 kg", age: "18 let" }
              ],
              active: activeTab === 1
            },
            {
              title: "Skupina C",
              icon: "�",
              items: [
                { name: "C1", desc: "Nákladní automobily do 7,5t", age: "18 let" },
                { name: "C1E", desc: "Nákladní automobil s přívěsem", age: "18 let" },
                { name: "C", desc: "Nákladní automobily nad 3,5t", age: "21 let" },
                { name: "CE", desc: "Nákladní automobil s přívěsem", age: "21 let" }
              ],
              active: activeTab === 2
            },
            {
              title: "Skupina D",
              icon: "🚌",
              items: [
                { name: "D1", desc: "Autobusy do 16 míst", age: "21 let" },
                { name: "D1E", desc: "Autobus s přívěsem", age: "21 let" },
                { name: "D", desc: "Autobusy nad 8 míst", age: "24 let" },
                { name: "DE", desc: "Autobus s přívěsem", age: "24 let" }
              ],
              active: activeTab === 3
            }
          ].filter(category => category.active).map((category, index) => (
            <div key={index} className="space-y-4">
              <div className="flex items-center gap-3 mb-2">
                <div className="text-3xl">{category.icon}</div>
                <h4 className="text-xl font-semibold text-gray-900">
                  {category.title}
                </h4>
              </div>

              <div className="space-y-3">
                {category.items.map((item, itemIndex) => (
                  <div key={itemIndex} className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div className="w-12 h-12 flex items-center justify-center bg-blue-100 rounded-lg text-blue-700 font-bold mr-4">
                      {item.name}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{item.desc}</div>
                      <div className="text-sm text-gray-600">Minimální věk: {item.age}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Struktura zkoušky - zjednodušeno */}
      <div className="grid lg:grid-cols-2 gap-8">
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-emerald-100 rounded-xl">
              <FileText className="w-6 h-6 text-emerald-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900">
              Teoretická zkouška
            </h3>
          </div>

          <div className="space-y-4">
            {[
              { icon: CheckCircle, label: "25 testových otázek", color: "emerald" },
              { icon: Clock, label: "30 minut na vyřešení", color: "blue" },
              { icon: Target, label: "Max. 2 chybné odpovědi", color: "amber" }
            ].map((item, index) => (
              <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <div className={`p-2 bg-${item.color}-100 rounded-lg`}>
                  <item.icon className={`w-5 h-5 text-${item.color}-600`} />
                </div>
                <span className="text-gray-700">{item.label}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-blue-100 rounded-xl">
              <GraduationCap className="w-6 h-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900">
              Požadavky pro získání
            </h3>
          </div>

          <div className="space-y-4">
            {[
              { icon: Users, label: "Minimálně 17 let (15 pro AM)" },
              { icon: Shield, label: "Lékařské vyšetření" },
              { icon: Star, label: "Kurz první pomoci" }
            ].map((item, index) => (
              <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <div className="p-2 bg-green-100 rounded-lg">
                  <item.icon className="w-5 h-5 text-green-600" />
                </div>
                <span className="text-gray-700">{item.label}</span>
                <div className="ml-auto">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Tematické okruhy - zjednodušeno */}
      <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
        <div className="flex items-center gap-3 mb-8">
          <BookOpen className="w-6 h-6 text-indigo-600" />
          <h3 className="text-2xl font-semibold text-gray-900">
            Tematické okruhy teoretické zkoušky
          </h3>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {[
            [
              { title: "1. Pravidla provozu", items: ["Zákon o silničním provozu", "Dopravní značky", "Světelná signalizace", "Přednost v jízdě"], color: "indigo", icon: "⚖️" },
              { title: "2. Technický stav vozidla", items: ["Kontrola před jízdou", "Provozní kapaliny", "Brzdy a řízení", "Pneumatiky a osvětlení"], color: "emerald", icon: "🔧" },
              { title: "3. Bezpečnost jízdy", items: ["Brzdná dráha", "Bezpečná vzdálenost", "Rychlost jízdy", "Obranná jízda"], color: "blue", icon: "🛡️" }
            ],
            [
              { title: "4. Dopravní nehody", items: ["Postup při nehodě", "První pomoc", "Pojištění vozidla", "Evropský protokol"], color: "amber", icon: "🚨" },
              { title: "5. Ekologie a úspora", items: ["Úsporné řízení", "Emise a životní prostředí", "Alternativní paliva", "Ekonomická jízda"], color: "purple", icon: "🌱" },
              { title: "6. Psychologie řidiče", items: ["Alkohol a drogy", "Únava za volantem", "Agresivita v provozu", "Stres a pozornost"], color: "rose", icon: "🧠" }
            ]
          ].map((column, columnIndex) => (
            <div key={columnIndex} className="space-y-6">
              {column.map((topic, index) => (
                <div key={index} className={`p-6 bg-${topic.color}-50 rounded-xl border border-${topic.color}-100`}>
                  <div className="flex items-center gap-3 mb-4">
                    <div className="text-2xl">{topic.icon}</div>
                    <h4 className={`font-semibold text-${topic.color}-900`}>
                      {topic.title}
                    </h4>
                  </div>
                  <ul className={`text-sm text-${topic.color}-700 space-y-2`}>
                    {topic.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-center gap-2">
                        <div className={`w-1.5 h-1.5 bg-${topic.color}-500 rounded-full`}></div>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>

      {/* Průvodce přípravou - zjednodušeno */}
      <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-8 border border-green-100">
        <div className="flex items-center gap-3 mb-8">
          <div className="p-2 bg-green-100 rounded-xl">
            <AlertTriangle className="w-6 h-6 text-green-600" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-900">
            Průvodce přípravou
          </h3>
        </div>

        <div className="grid md:grid-cols-3 gap-6">
          {[
            { icon: BookOpen, title: "Studuj předpisy", desc: "Prostuduj zákon o silničním provozu a dopravní značky", time: "2-3 týdny" },
            { icon: Target, title: "Procvič testy", desc: "Absolvuj minimálně 20 testů s úspěšností 95%+", time: "1-2 týdny" },
            { icon: Truck, title: "Praktické hodiny", desc: "Absolvuj povinný počet hodin praktické výuky", time: "3-4 týdny" }
          ].map((step, index) => (
            <div key={index} className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-green-100 rounded-xl">
                  <step.icon className="w-6 h-6 text-green-600" />
                </div>
                <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                  {step.time}
                </div>
              </div>

              <h4 className="font-semibold text-gray-900 mb-2">{step.title}</h4>
              <p className="text-sm text-gray-600">{step.desc}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
