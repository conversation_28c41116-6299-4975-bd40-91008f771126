import React from "react";
import {
  MapPin,
  CheckCircle,
  Clock,
  AlertTriangle,
  FileText,
  GraduationCap,
  Award,
  Calendar,
  CreditCard,
  Users
} from "lucide-react";

export const CaptainExamProcess = () => {
  const processSteps = [
    {
      id: 1,
      title: "Příprava dokumentů",
      duration: "1-2 dny",
      description: "Sběr a příprava všech potřebných dokladů",
      tasks: [
        "Lékařsk<PERSON> posudek (max. 3 měsíce starý)",
        "<PERSON>raktick<PERSON> zkouška (pro kategorie M, S, M24)",
        "Vyplnění žádosti o vydání průkazu",
        "Příprava správního poplatku"
      ],
      icon: FileText,
      color: "blue"
    },
    {
      id: 2,
      title: "Podání žádosti",
      duration: "1 den",
      description: "Podání žádosti nejpozději 10 pracovních dnů před zkouškou",
      tasks: [
        "Elektronické podání přes Portál dopravy (sleva 20%)",
        "Osobní podání na pobočce SPS",
        "Zaplacení správního poplatku 500 Kč (400 Kč online)",
        "Výběr termínu zkoušky"
      ],
      icon: Calendar,
      color: "green"
    },
    {
      id: 3,
      title: "Příprava na zkoušku",
      duration: "2-6 týdnů",
      description: "Studium předpisů a procvičování testových otázek",
      tasks: [
        "Studium plavebních předpisů",
        "Procvičování online testů",
        "Navigace a bezpečnost plavby",
        "Meteorologie a první pomoc"
      ],
      icon: GraduationCap,
      color: "purple"
    },
    {
      id: 4,
      title: "Teoretická zkouška",
      duration: "30-60 minut",
      description: "Elektronický test na pobočce Státní plavební správy",
      tasks: [
        "Dostavení se s dokladem totožnosti",
        "Test na počítači (35 otázek pro M/M20)",
        "Maximálně 1 chybná odpověď pro úspěch",
        "Okamžité vyhodnocení výsledku"
      ],
      icon: Users,
      color: "orange"
    },
    {
      id: 5,
      title: "Vydání průkazu",
      duration: "Tentýž den",
      description: "Převzetí průkazu po úspěšné zkoušce",
      tasks: [
        "Průkaz vydán v den zkoušky",
        "Možnost vyzvednutí později",
        "Vyzvednutí třetí osobou s plnou mocí",
        "Platnost podle věkových kategorií"
      ],
      icon: Award,
      color: "emerald"
    }
  ];

  return (
    <div className="space-y-8">
      {/* Úvod */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Proces získání kapitánského průkazu
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Detailní průvodce celým procesem od přípravy dokumentů až po vydání průkazu. 
          Celý proces obvykle trvá 3-8 týdnů v závislosti na vaší přípravě.
        </p>
      </div>

      {/* Timeline */}
      <div className="relative">
        {/* Vertical line */}
        <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-200"></div>
        
        <div className="space-y-8">
          {processSteps.map((step, index) => (
            <div key={step.id} className="relative flex items-start gap-6">
              {/* Step number circle */}
              <div className={`relative z-10 w-16 h-16 bg-${step.color}-100 rounded-full flex items-center justify-center border-4 border-white shadow-lg`}>
                <step.icon className={`w-6 h-6 text-${step.color}-600`} />
              </div>
              
              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-semibold text-gray-900">
                      {step.title}
                    </h3>
                    <span className={`px-3 py-1 bg-${step.color}-100 text-${step.color}-700 text-sm font-medium rounded-full`}>
                      {step.duration}
                    </span>
                  </div>
                  
                  <p className="text-gray-600 mb-4">
                    {step.description}
                  </p>
                  
                  <div className="grid md:grid-cols-2 gap-3">
                    {step.tasks.map((task, taskIndex) => (
                      <div key={taskIndex} className="flex items-start gap-2">
                        <CheckCircle className={`w-4 h-4 text-${step.color}-500 mt-0.5 flex-shrink-0`} />
                        <span className="text-sm text-gray-700">{task}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Důležité lhůty */}
      <div className="bg-amber-50 rounded-xl p-6 border border-amber-200">
        <div className="flex items-start gap-3">
          <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-semibold text-amber-800 mb-3">Důležité lhůty a termíny</h4>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-amber-600" />
                  <span className="text-sm font-medium text-amber-800">Podání žádosti:</span>
                </div>
                <p className="text-sm text-amber-700 ml-6">
                  Nejpozději 10 pracovních dnů před požadovaným termínem zkoušky
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-amber-600" />
                  <span className="text-sm font-medium text-amber-800">Praktická zkouška:</span>
                </div>
                <p className="text-sm text-amber-700 ml-6">
                  Musí být složena před dnem podání žádosti, platnost 2 roky
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-amber-600" />
                  <span className="text-sm font-medium text-amber-800">Lékařský posudek:</span>
                </div>
                <p className="text-sm text-amber-700 ml-6">
                  Nesmí být starší než 3 měsíce v době podání žádosti
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-amber-600" />
                  <span className="text-sm font-medium text-amber-800">Zkouška:</span>
                </div>
                <p className="text-sm text-amber-700 ml-6">
                  Musí být úspěšně složena do 1 roku od podání žádosti
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tipy pro úspěch */}
      <div className="bg-green-50 rounded-xl p-6 border border-green-200">
        <h4 className="font-semibold text-green-800 mb-4">💡 Tipy pro hladký průběh procesu</h4>
        <div className="grid md:grid-cols-2 gap-4">
          <ul className="space-y-2">
            <li className="flex items-start gap-2">
              <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-green-700">Začněte s přípravou dokumentů co nejdříve</span>
            </li>
            <li className="flex items-start gap-2">
              <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-green-700">Využijte elektronické podání pro slevu 20%</span>
            </li>
          </ul>
          <ul className="space-y-2">
            <li className="flex items-start gap-2">
              <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-green-700">Procvičujte testy pravidelně, ne najednou</span>
            </li>
            <li className="flex items-start gap-2">
              <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-green-700">Rezervujte si čas na převzetí průkazu</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};
