import React from "react";
import {
  GraduationCap,
  Clock,
  Target,
  CheckCircle,
  AlertTriangle,
  BookOpen,
  FileText,
  Waves,
  RotateCcw
} from "lucide-react";

export const CaptainExamTest = () => {
  const testCategories = [
    {
      category: "M a M20",
      questions: 35,
      time: 30,
      successPoints: 30,
      maxErrors: 5,
      topics: "Plavební provoz, základy techniky, základy první pomoci",
      color: "blue"
    },
    {
      category: "S a S20",
      questions: 14,
      time: 10,
      successPoints: 11,
      maxErrors: 3,
      topics: "<PERSON><PERSON>ruk<PERSON> plachetnic, teorie plavby plachetnic, technika plachtění",
      color: "green"
    },
    {
      category: "C (příbřežní)",
      questions: 28,
      time: 25,
      successPoints: 24,
      maxErrors: 4,
      topics: "Mezinárodní námořní právo, navigace, meteorologie, bezpečnost na moři",
      color: "cyan"
    }
  ];

  const examTopics = [
    {
      title: "<PERSON>lavebn<PERSON> předpisy",
      icon: BookOpen,
      items: [
        "Zákon o vnitrozemské plavbě",
        "Pravidla plavebního provozu",
        "Značení vodních cest",
        "Povinnosti vůdce plavidla"
      ],
      color: "blue"
    },
    {
      title: "Navigace a mapy",
      icon: Target,
      items: [
        "Čtení plavebních map",
        "Základy navigace",
        "Určování polohy",
        "Plavební značky"
      ],
      color: "green"
    },
    {
      title: "Bezpečnost plavby",
      icon: Waves,
      items: [
        "Záchranné prostředky",
        "Nouzové postupy",
        "VHF radiokomunikace",
        "Signalizace nouze"
      ],
      color: "orange"
    },
    {
      title: "Meteorologie",
      icon: Clock,
      items: [
        "Předpověď počasí",
        "Nebezpečné jevy",
        "Vliv větru na plavbu",
        "Viditelnost"
      ],
      color: "purple"
    }
  ];

  return (
    <div className="space-y-8">
      {/* Úvod */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Teoretická zkouška
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Elektronický test probíhá na počítači na pobočkách Státní plavební správy. 
          Každá kategorie má specifické požadavky na počet otázek a úspěšnost.
        </p>
      </div>

      {/* Přehled testů podle kategorií */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-6">Struktura testů podle kategorií</h3>
        <div className="grid gap-6">
          {testCategories.map((test, index) => (
            <div key={test.category} className={`bg-gradient-to-r from-${test.color}-50 to-${test.color}-100 rounded-xl p-6 border border-${test.color}-200`}>
              <div className="grid lg:grid-cols-3 gap-6">
                <div>
                  <div className={`inline-block px-4 py-2 bg-${test.color}-500 text-white rounded-lg font-bold text-lg mb-3`}>
                    {test.category}
                  </div>
                  <p className={`text-${test.color}-800 text-sm`}>
                    {test.topics}
                  </p>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className={`text-2xl font-bold text-${test.color}-700`}>{test.questions}</div>
                    <div className={`text-sm text-${test.color}-600`}>otázek</div>
                  </div>
                  <div className="text-center">
                    <div className={`text-2xl font-bold text-${test.color}-700`}>{test.time}</div>
                    <div className={`text-sm text-${test.color}-600`}>minut</div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className={`w-4 h-4 text-${test.color}-600`} />
                    <span className={`text-sm text-${test.color}-800`}>
                      Pro úspěch: nejméně {test.successPoints} bodů
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <AlertTriangle className={`w-4 h-4 text-${test.color}-600`} />
                    <span className={`text-sm text-${test.color}-800`}>
                      Maximálně {test.maxErrors} chybných odpovědí
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Témata zkoušky */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-6">Hlavní témata zkoušky</h3>
        <div className="grid md:grid-cols-2 gap-6">
          {examTopics.map((topic, index) => (
            <div key={topic.title} className={`bg-${topic.color}-50 rounded-xl p-6 border border-${topic.color}-200`}>
              <div className="flex items-center gap-3 mb-4">
                <div className={`p-2 bg-${topic.color}-100 rounded-lg`}>
                  <topic.icon className={`w-5 h-5 text-${topic.color}-600`} />
                </div>
                <h4 className={`font-semibold text-${topic.color}-900`}>
                  {topic.title}
                </h4>
              </div>
              <div className="space-y-2">
                {topic.items.map((item, itemIndex) => (
                  <div key={itemIndex} className="flex items-start gap-2">
                    <div className={`w-1.5 h-1.5 bg-${topic.color}-500 rounded-full mt-2`}></div>
                    <span className={`text-${topic.color}-700 text-sm`}>{item}</span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Průběh zkoušky */}
      <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
        <h3 className="text-xl font-semibold text-gray-900 mb-6">Průběh zkoušky</h3>
        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Před zkouškou:</h4>
            <div className="space-y-2">
              {[
                "Dostavte se s dokladem totožnosti",
                "Zkušební komisař ověří vaši totožnost",
                "Budete přiděleni k počítači",
                "Pročtěte si pokyny k obsluze"
              ].map((step, index) => (
                <div key={index} className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700 text-sm">{step}</span>
                </div>
              ))}
            </div>
          </div>
          
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Během zkoušky:</h4>
            <div className="space-y-2">
              {[
                "Test probíhá elektronickou formou",
                "Každá otázka má 3 možnosti (a, b, c)",
                "Pouze jedna odpověď je správná",
                "Výsledek je znám okamžitě po dokončení"
              ].map((step, index) => (
                <div key={index} className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700 text-sm">{step}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Opakování zkoušky */}
      <div className="bg-red-50 rounded-xl p-6 border border-red-200">
        <div className="flex items-center gap-3 mb-4">
          <RotateCcw className="w-6 h-6 text-red-600" />
          <h3 className="text-xl font-semibold text-gray-900">
            Opakování při neúspěchu
          </h3>
        </div>
        
        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <h4 className="font-medium text-red-900">Možnosti opakování:</h4>
            <div className="space-y-2">
              {[
                "Opakování v následném zkušebním termínu",
                "Bez časového omezení počtu pokusů",
                "Zkouška musí být složena do 1 roku",
                "Za každé opakování se platí nový poplatek"
              ].map((option, index) => (
                <div key={index} className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                  <span className="text-red-700 text-sm">{option}</span>
                </div>
              ))}
            </div>
          </div>
          
          <div className="space-y-3">
            <h4 className="font-medium text-red-900">Doporučený postup:</h4>
            <div className="space-y-2">
              {[
                "Analyzujte chybné odpovědi",
                "Doplňte si znalosti v problematických oblastech",
                "Procvičte si více testových otázek",
                "Využijte konzultace s odborníky"
              ].map((tip, index) => (
                <div key={index} className="flex items-start gap-2">
                  <Target className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                  <span className="text-red-700 text-sm">{tip}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Tipy pro úspěch */}
      <div className="bg-green-50 rounded-xl p-6 border border-green-200">
        <h4 className="font-semibold text-green-800 mb-4">💡 Tipy pro úspěšnou zkoušku</h4>
        <div className="grid md:grid-cols-2 gap-4">
          <ul className="space-y-2">
            <li className="flex items-start gap-2">
              <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-green-700">Procvičujte testy pravidelně, ne najednou</span>
            </li>
            <li className="flex items-start gap-2">
              <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-green-700">Zaměřte se na slabší oblasti</span>
            </li>
          </ul>
          <ul className="space-y-2">
            <li className="flex items-start gap-2">
              <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-green-700">Čtěte otázky pozorně a pomalu</span>
            </li>
            <li className="flex items-start gap-2">
              <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-green-700">Využijte celý čas na kontrolu</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};
