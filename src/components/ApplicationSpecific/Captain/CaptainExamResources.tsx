import React from "react";
import {
  ExternalLink,
  FileText,
  Calendar,
  Users,
  BookOpen,
  Info,
  Globe,
  Phone,
  MapPin,
  Clock
} from "lucide-react";

export const CaptainExamResources = () => {
  const officialLinks = [
    {
      title: "Term<PERSON><PERSON> zkou<PERSON>",
      url: "https://terminy-zkousek.plavebniurad.cz/index.asp",
      description: "Vyhledání a rezervace termínů zkoušek způsobilosti",
      icon: Calendar,
      category: "Zkouš<PERSON>"
    },
    {
      title: "Zkoušková aplikace",
      url: "http://www.spspraha.cz/zkousky/zkousky.aspx",
      description: "Procvičování testových otázek online",
      icon: BookOpen,
      category: "Příprava"
    },
    {
      title: "Elektronické žádosti",
      url: "https://sps.gov.cz/dok-os/el-podani-prehled",
      description: "Podání ž<PERSON>dosti přes Portál dopravy se slevou 20%",
      icon: FileText,
      category: "<PERSON><PERSON><PERSON><PERSON>"
    },
    {
      title: "Prů<PERSON>zy k převzetí",
      url: "https://vydane-prukazy.plavebniurad.cz",
      description: "Kontrola stavu připravených průkazů k vyzvednutí",
      icon: Users,
      category: "Průkazy"
    },
    {
      title: "Seznam pověřených osob",
      url: "https://sps.gov.cz/dok-os/vmp/seznam-poverenych-osob",
      description: "Pro praktické zkoušky kategorií M, S, M24",
      icon: Users,
      category: "Praktické zkoušky"
    },
    {
      title: "Správní poplatky",
      url: "https://sps.gov.cz/dok-os/spravni-poplatky",
      description: "Aktuální sazby správních poplatků",
      icon: FileText,
      category: "Poplatky"
    }
  ];

  const documents = [
    {
      title: "Žádost o vydání průkazu",
      description: "Formulář pro podání žádosti (PDF)",
      note: "Při tisku zvolte měřítko 'Žádné'"
    },
    {
      title: "Lékařský posudek",
      description: "Formulář zdravotní způsobilosti",
      note: "Vyplňuje praktický lékař"
    },
    {
      title: "Osnova praktických dovedností",
      description: "Co se ověřuje při praktické zkoušce",
      note: "Pro kategorie M, S, M24"
    },
    {
      title: "Vzor průkazu VMP",
      description: "Jak vypadá průkaz vůdce malého plavidla",
      note: "Pro plavbu v ČR i zahraničí"
    }
  ];

  const branches = [
    {
      city: "Praha",
      address: "Jankovcova 4, 170 00 Praha 7",
      phone: "+420 266 710 411",
      email: "<EMAIL>",
      hours: "Po-Pá: 8:00-15:00"
    },
    {
      city: "Děčín",
      address: "Labská 7, 405 02 Děčín",
      phone: "+420 412 704 111",
      email: "<EMAIL>",
      hours: "Po-Pá: 8:00-15:00"
    },
    {
      city: "Přerov",
      address: "Tovární 37, 750 02 Přerov",
      phone: "+420 581 252 811",
      email: "<EMAIL>",
      hours: "Po-Pá: 8:00-15:00"
    }
  ];

  const studyTips = [
    {
      title: "Studijní plán",
      tips: [
        "Rozdělte si látku na týdenní bloky",
        "Věnujte každý den 30-60 minut studiu",
        "Začněte s plavebními předpisy",
        "Postupně přidávejte další témata"
      ]
    },
    {
      title: "Procvičování testů",
      tips: [
        "Využívajte oficiální zkoušková aplikace SPS",
        "Procvičujte pravidelně, ne najednou",
        "Zaměřte se na slabší oblasti",
        "Dosáhněte úspěšnosti 95%+ před zkouškou"
      ]
    },
    {
      title: "Praktická příprava",
      tips: [
        "Získejte praxi na vodě před praktickou zkouškou",
        "Seznamte se s různými typy plavidel",
        "Procvičte manévrování v přístavu",
        "Naučte se používat záchranné prostředky"
      ]
    }
  ];

  return (
    <div className="space-y-8">
      {/* Úvod */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Oficiální zdroje a užitečné odkazy
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Kompletní přehled oficiálních zdrojů Státní plavební správy, 
          formulářů a tipů pro úspěšnou přípravu na kapitánské zkoušky.
        </p>
      </div>

      {/* Oficiální odkazy SPS */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-6">Oficiální odkazy Státní plavební správy</h3>
        <div className="grid md:grid-cols-2 gap-4">
          {officialLinks.map((link, index) => (
            <div key={index} className="bg-white rounded-lg p-4 border border-gray-200 hover:border-blue-300 transition-colors group">
              <div className="flex items-start gap-3">
                <div className="p-2 bg-blue-50 rounded-lg group-hover:bg-blue-100 transition-colors">
                  <link.icon className="w-5 h-5 text-blue-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium text-gray-900">{link.title}</h4>
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                      {link.category}
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm mb-2">{link.description}</p>
                  <div className="flex items-center gap-1 text-blue-600 text-xs">
                    <ExternalLink className="w-3 h-3" />
                    <span className="font-mono">{link.url}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Formuláře a dokumenty */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-6">Formuláře a dokumenty</h3>
        <div className="grid md:grid-cols-2 gap-4">
          {documents.map((doc, index) => (
            <div key={index} className="bg-slate-50 rounded-lg p-4 border border-slate-200">
              <div className="flex items-start gap-3">
                <FileText className="w-5 h-5 text-slate-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-slate-900 mb-1">{doc.title}</h4>
                  <p className="text-slate-600 text-sm mb-2">{doc.description}</p>
                  <p className="text-slate-500 text-xs bg-slate-100 p-2 rounded">
                    💡 {doc.note}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Pobočky SPS */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-6">Pobočky Státní plavební správy</h3>
        <div className="grid lg:grid-cols-3 gap-6">
          {branches.map((branch, index) => (
            <div key={index} className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
              <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <MapPin className="w-5 h-5 text-blue-600" />
                {branch.city}
              </h4>
              
              <div className="space-y-3 text-sm">
                <div className="flex items-start gap-2">
                  <MapPin className="w-4 h-4 text-gray-500 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">{branch.address}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-gray-500 flex-shrink-0" />
                  <span className="text-gray-700">{branch.phone}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Globe className="w-4 h-4 text-gray-500 flex-shrink-0" />
                  <span className="text-gray-700">{branch.email}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-gray-500 flex-shrink-0" />
                  <span className="text-gray-700">{branch.hours}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Tipy pro studium */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-6">Tipy pro efektivní přípravu</h3>
        <div className="grid lg:grid-cols-3 gap-6">
          {studyTips.map((section, index) => (
            <div key={index} className="bg-green-50 rounded-lg p-6 border border-green-200">
              <h4 className="font-semibold text-green-900 mb-4">{section.title}</h4>
              <ul className="space-y-2">
                {section.tips.map((tip, tipIndex) => (
                  <li key={tipIndex} className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-green-700 text-sm">{tip}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>

      {/* Důležité upozornění */}
      <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
        <div className="flex items-start gap-3">
          <Info className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-semibold text-blue-800 mb-2">Aktuální informace</h4>
            <p className="text-blue-700 text-sm leading-relaxed">
              Všechny informace jsou aktualizovány podle platných předpisů Státní plavební správy. 
              Pro nejnovější změny a aktuální termíny zkoušek vždy navštivte oficiální stránky 
              <strong> sps.gov.cz</strong>. Doporučujeme si před podáním žádosti ověřit aktuální 
              požadavky a lhůty.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
