import React from "react";
import {
  FileText,
  Calendar,
  CreditCard,
  GraduationCap,
  Award,
  AlertTriangle,
  Info,
  CheckCircle,
  Clock,
  Heart
} from "lucide-react";

export const CaptainExamDocuments = () => {
  const documentPhases = [
    {
      phase: "<PERSON><PERSON>i podán<PERSON> ž<PERSON>dosti",
      icon: Calendar,
      color: "blue",
      documents: [
        {
          name: "Žádost o vydání plavebního dokladu",
          description: "Formulář dostupný na pobočkách SPS nebo ke stažení",
          required: true,
          note: "Lze podat elektronicky přes Portál dopravy"
        },
        {
          name: "Lékařský posudek o zdravotní způsobilosti",
          description: "Vystavuje praktický lékař, u kterého jste registrováni",
          required: true,
          note: "Nesmí být starší než 3 měsíce"
        },
        {
          name: "Osvědčení o praktické zkou<PERSON>",
          description: "Pro kategorie M, S, M24 - vyd<PERSON>v<PERSON> pověřená osoba",
          required: false,
          note: "Platnost 2 roky, pouze pro některé kategorie"
        },
        {
          name: "Správní poplatek",
          description: "500 Kč (400 Kč při elektronickém podání)",
          required: true,
          note: "Sleva 20% při podání přes Portál dopravy"
        }
      ]
    },
    {
      phase: "Při zkoušce",
      icon: GraduationCap,
      color: "purple",
      documents: [
        {
          name: "Doklad totožnosti",
          description: "Občanský průkaz nebo pas",
          required: true,
          note: "Musí být platný"
        },
        {
          name: "Potvrzení o přidělení termínu",
          description: "Dostanete po podání žádosti",
          required: true,
          note: "Obsahuje datum, čas a místo zkoušky"
        }
      ]
    },
    {
      phase: "Při vydání průkazu",
      icon: Award,
      color: "emerald",
      documents: [
        {
          name: "Průkaz vydán v den zkoušky",
          description: "Po úspěšném složení teoretické zkoušky",
          required: false,
          note: "Možnost vyzvednutí později nebo třetí osobou s plnou mocí"
        }
      ]
    }
  ];

  const costs = [
    {
      item: "Správní poplatek za zkoušku",
      standard: "500 Kč",
      electronic: "400 Kč",
      note: "Sleva 20% při elektronickém podání"
    },
    {
      item: "Správní poplatek za vydání průkazu",
      standard: "400 Kč",
      electronic: "320 Kč",
      note: "Po dosažení věkové hranice"
    },
    {
      item: "Lékařský posudek",
      standard: "200-500 Kč",
      electronic: "-",
      note: "Cena se liší podle lékaře"
    },
    {
      item: "Praktická zkouška",
      standard: "1000-2000 Kč",
      electronic: "-",
      note: "Cena stanovena pověřenou osobou"
    }
  ];

  return (
    <div className="space-y-8">
      {/* Úvod */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Potřebné doklady a náležitosti
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Přehled všech dokumentů a poplatků potřebných v jednotlivých fázích 
          procesu získání kapitánského průkazu.
        </p>
      </div>

      {/* Fáze dokumentů */}
      <div className="space-y-8">
        {documentPhases.map((phase, phaseIndex) => (
          <div key={phaseIndex} className={`bg-gradient-to-r from-${phase.color}-50 to-${phase.color}-100 rounded-xl p-6 border border-${phase.color}-200`}>
            <div className="flex items-center gap-3 mb-6">
              <div className={`p-3 bg-${phase.color}-500 rounded-xl`}>
                <phase.icon className="w-6 h-6 text-white" />
              </div>
              <h3 className={`text-xl font-semibold text-${phase.color}-900`}>
                {phase.phase}
              </h3>
            </div>
            
            <div className="grid gap-4">
              {phase.documents.map((doc, docIndex) => (
                <div key={docIndex} className="bg-white rounded-lg p-4 border border-gray-200">
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-1">
                      {doc.required ? (
                        <CheckCircle className={`w-5 h-5 text-${phase.color}-600`} />
                      ) : (
                        <Info className={`w-5 h-5 text-${phase.color}-600`} />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-gray-900">{doc.name}</h4>
                        {doc.required && (
                          <span className={`text-xs bg-${phase.color}-100 text-${phase.color}-700 px-2 py-1 rounded-full`}>
                            Povinné
                          </span>
                        )}
                      </div>
                      <p className="text-gray-600 text-sm mb-2">{doc.description}</p>
                      {doc.note && (
                        <p className={`text-${phase.color}-700 text-xs bg-${phase.color}-50 p-2 rounded`}>
                          💡 {doc.note}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Přehled nákladů */}
      <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
        <div className="flex items-center gap-3 mb-6">
          <CreditCard className="w-6 h-6 text-gray-600" />
          <h3 className="text-xl font-semibold text-gray-900">
            Přehled nákladů
          </h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-900">Položka</th>
                <th className="text-center py-3 px-4 font-medium text-gray-900">Standardní podání</th>
                <th className="text-center py-3 px-4 font-medium text-gray-900">Elektronické podání</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Poznámka</th>
              </tr>
            </thead>
            <tbody>
              {costs.map((cost, index) => (
                <tr key={index} className="border-b border-gray-100">
                  <td className="py-3 px-4 text-gray-900">{cost.item}</td>
                  <td className="py-3 px-4 text-center font-medium text-gray-900">{cost.standard}</td>
                  <td className="py-3 px-4 text-center font-medium text-green-600">{cost.electronic}</td>
                  <td className="py-3 px-4 text-sm text-gray-600">{cost.note}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Důležité lhůty */}
      <div className="bg-amber-50 rounded-xl p-6 border border-amber-200">
        <div className="flex items-start gap-3">
          <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-semibold text-amber-800 mb-3">Důležité lhůty</h4>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-3">
                {[
                  {
                    title: "Podání žádosti",
                    desc: "Nejpozději 10 pracovních dnů před zkouškou"
                  },
                  {
                    title: "Lékařský posudek",
                    desc: "Nesmí být starší než 3 měsíce"
                  }
                ].map((item, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <Clock className="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <span className="font-medium text-amber-800 text-sm">{item.title}:</span>
                      <p className="text-amber-700 text-sm">{item.desc}</p>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="space-y-3">
                {[
                  {
                    title: "Praktická zkouška",
                    desc: "Platnost 2 roky, musí být před podáním žádosti"
                  },
                  {
                    title: "Teoretická zkouška",
                    desc: "Musí být složena do 1 roku od podání žádosti"
                  }
                ].map((item, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <Clock className="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <span className="font-medium text-amber-800 text-sm">{item.title}:</span>
                      <p className="text-amber-700 text-sm">{item.desc}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Elektronické podání */}
      <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
        <div className="flex items-start gap-3">
          <Info className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-semibold text-blue-800 mb-3">Výhody elektronického podání</h4>
            <div className="grid md:grid-cols-2 gap-4">
              <ul className="space-y-2">
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-blue-700">Sleva 20% na správním poplatku</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-blue-700">Pohodlné podání z domova</span>
                </li>
              </ul>
              <ul className="space-y-2">
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-blue-700">Rychlejší vyřízení</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-blue-700">Sledování stavu žádosti online</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
