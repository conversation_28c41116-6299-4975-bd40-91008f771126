import React from "react";
import {
  An<PERSON>,
  CheckCircle,
  Info,
  Star,
  Users,
  Target,
  Clock,
  Award
} from "lucide-react";

export const CaptainExamOverview = () => {
  return (
    <div className="space-y-8">
      {/* Co je to kapitánský průkaz */}
      <div className="grid lg:grid-cols-2 gap-8">
        <div className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Co je to kapitánský průkaz?
            </h2>
            <p className="text-gray-700 leading-relaxed">
              Průkaz způsobilosti vůdce malého plavidla je oficiální doklad, který vám umožňuje 
              legálně řídit motorové čluny, plachetnice a rekreační plavidla na českých vodních cestách 
              i v zahraničí.
            </p>
          </div>

          <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
            <h3 className="font-semibold text-blue-900 mb-4 flex items-center gap-2">
              <Info className="w-5 h-5" />
              Klíčové informace
            </h3>
            <div className="space-y-3">
              {[
                "Vydává Státní plavební správa",
                "Platnost podle věku (nová legislativa od 2023)",
                "Uznáván v rámci ČR i v zahraničí",
                "Různé kategorie podle typu plavidla"
              ].map((item, index) => (
                <div key={index} className="flex items-start gap-3">
                  <CheckCircle className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span className="text-blue-800 text-sm">{item}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <div className="bg-gradient-to-br from-cyan-50 to-blue-50 rounded-xl p-6 border border-cyan-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-cyan-100 rounded-lg">
                <Anchor className="w-6 h-6 text-cyan-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                Proč získat kapitánský průkaz?
              </h3>
            </div>
            <ul className="space-y-3 text-sm text-gray-700">
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-cyan-500 rounded-full mt-2"></div>
                <span>Legální řízení motorových člunů a plachetnic</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-cyan-500 rounded-full mt-2"></div>
                <span>Možnost půjčování plavidel v ČR i zahraničí</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-cyan-500 rounded-full mt-2"></div>
                <span>Bezpečná plavba s rodinou a přáteli</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-cyan-500 rounded-full mt-2"></div>
                <span>Přístup k exkluzivním marínám a přístavům</span>
              </li>
            </ul>
          </div>

          {/* Statistiky */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-white rounded-lg p-4 border border-gray-200 text-center">
              <div className="text-2xl font-bold text-blue-600 mb-1">85%</div>
              <div className="text-xs text-gray-600">Úspěšnost na první pokus</div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-gray-200 text-center">
              <div className="text-2xl font-bold text-blue-600 mb-1">2-4</div>
              <div className="text-xs text-gray-600">Týdny přípravy</div>
            </div>
          </div>
        </div>
      </div>

      {/* Rychlý přehled procesu */}
      <div className="bg-gradient-to-r from-slate-50 to-gray-50 rounded-xl p-6 border border-slate-200">
        <h3 className="text-xl font-semibold text-gray-900 mb-6 text-center">
          Jak získat kapitánský průkaz - rychlý přehled
        </h3>
        
        <div className="grid md:grid-cols-4 gap-6">
          {[
            {
              step: "1",
              title: "Výběr kategorie",
              desc: "Rozhodněte se, jaký typ plavidla chcete řídit",
              icon: Target,
              color: "blue"
            },
            {
              step: "2", 
              title: "Příprava",
              desc: "Studium předpisů a procvičování testů",
              icon: Users,
              color: "green"
            },
            {
              step: "3",
              title: "Zkouška",
              desc: "Teoretický test a praktické ověření",
              icon: Clock,
              color: "orange"
            },
            {
              step: "4",
              title: "Průkaz",
              desc: "Vydání průkazu v den úspěšné zkoušky",
              icon: Award,
              color: "purple"
            }
          ].map((item, index) => (
            <div key={index} className="text-center">
              <div className={`w-16 h-16 bg-${item.color}-100 rounded-full flex items-center justify-center mx-auto mb-4`}>
                <item.icon className={`w-8 h-8 text-${item.color}-600`} />
              </div>
              <div className={`text-sm font-bold text-${item.color}-600 mb-2`}>
                Krok {item.step}
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">{item.title}</h4>
              <p className="text-sm text-gray-600">{item.desc}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Důležité upozornění */}
      <div className="bg-amber-50 rounded-xl p-6 border border-amber-200">
        <div className="flex items-start gap-3">
          <Info className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-semibold text-amber-800 mb-2">Před začátkem přípravy</h4>
            <p className="text-amber-700 text-sm leading-relaxed">
              Doporučujeme si nejdříve prostudovat všechny kategorie průkazů a vybrat tu správnou 
              podle vašich potřeb. Každá kategorie má specifické požadavky na věk, praktické ověření 
              a rozsah oprávnění.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
