import React from "react";
import {
  Comp<PERSON>,
  Anchor,
  Waves,
  Shield,
  CheckCircle,
  AlertTriangle,
  Info
} from "lucide-react";

export const CaptainExamCategories = () => {
  const categories = [
    {
      code: "M20",
      name: "Malá plavidla do 20 kW",
      age: "16 let",
      practical: false,
      description: "Pro plavidla s motorem do 20 kW výkonu",
      includes: [
        "Motorové čluny do 20 kW",
        "Malá rekreační plavidla",
        "Plavidla bez vlastního pohonu"
      ],
      color: "blue"
    },
    {
      code: "M",
      name: "Malá plavidla bez omezení",
      age: "16 let",
      practical: true,
      description: "Pro plavidla s motorem bez omezení výkonu",
      includes: [
        "Motorové čluny bez omezení výkonu",
        "Všechna malá plavidla",
        "Plavidla bez vlastního pohonu"
      ],
      color: "indigo"
    },
    {
      code: "S20",
      name: "Plachetnice do 20 m²",
      age: "16 let",
      practical: false,
      description: "Pro plachetnice s plachtami do 20 m² plochy",
      includes: [
        "Plachetnice do 20 m² plachet",
        "Malé jachty",
        "Plavidla bez vlastního pohonu"
      ],
      color: "green"
    },
    {
      code: "S",
      name: "Plachetnice bez omezení",
      age: "16 let",
      practical: true,
      description: "Pro plachetnice bez omezení plochy plachet",
      includes: [
        "Plachetnice bez omezení",
        "Velké jachty",
        "Plavidla bez vlastního pohonu"
      ],
      color: "emerald"
    },
    {
      code: "M24",
      name: "Rekreační plavidla do 24 m",
      age: "18 let",
      practical: true,
      description: "Pro rekreační plavidla s délkou trupu do 24 metrů",
      includes: [
        "Rekreační plavidla do 24 m",
        "Větší motorové jachty",
        "Vyžaduje předchozí kategorii M nebo M+S"
      ],
      color: "purple",
      special: true
    },
    {
      code: "C",
      name: "Příbřežní plavba na moři",
      age: "16 let",
      practical: false,
      description: "Pro plavbu v pobřežních mořských vodách do 1 námořní míle",
      includes: [
        "Plavba do 1 nm od břehu",
        "Do 4° Beaufortovy stupnice",
        "Vyžaduje kategorii M nebo M20"
      ],
      color: "cyan",
      special: true
    }
  ];

  return (
    <div className="space-y-8">
      {/* Úvod */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Kategorie průkazů způsobilosti
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Vyberte si kategorii podle typu plavidla, které chcete řídit. 
          Kategorie lze kombinovat a získat v rámci jedné zkoušky.
        </p>
      </div>

      {/* Základní kategorie */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-6">Základní kategorie</h3>
        <div className="grid lg:grid-cols-2 gap-6">
          {categories.filter(cat => !cat.special).map((category, index) => (
            <div key={category.code} className={`bg-gradient-to-br from-${category.color}-50 to-${category.color}-100 rounded-xl p-6 border border-${category.color}-200`}>
              <div className="flex items-center gap-3 mb-4">
                <div className={`px-4 py-2 bg-${category.color}-500 text-white rounded-lg font-bold text-lg`}>
                  {category.code}
                </div>
                <div>
                  <h4 className={`font-semibold text-${category.color}-900 text-lg`}>
                    {category.name}
                  </h4>
                  <div className="flex items-center gap-4 mt-1">
                    <span className={`text-sm text-${category.color}-700`}>
                      Věk: {category.age}
                    </span>
                    {category.practical && (
                      <span className={`text-xs bg-${category.color}-200 text-${category.color}-800 px-2 py-1 rounded-full`}>
                        Praktická zkouška
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              <p className={`text-${category.color}-800 text-sm mb-4`}>
                {category.description}
              </p>
              
              <div className="space-y-2">
                <h5 className={`font-medium text-${category.color}-900 text-sm`}>Oprávnění:</h5>
                {category.includes.map((item, itemIndex) => (
                  <div key={itemIndex} className="flex items-start gap-2">
                    <CheckCircle className={`w-4 h-4 text-${category.color}-600 mt-0.5 flex-shrink-0`} />
                    <span className={`text-${category.color}-700 text-sm`}>{item}</span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Speciální kategorie */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-6">Speciální kategorie</h3>
        <div className="grid lg:grid-cols-2 gap-6">
          {categories.filter(cat => cat.special).map((category, index) => (
            <div key={category.code} className={`bg-gradient-to-br from-${category.color}-50 to-${category.color}-100 rounded-xl p-6 border border-${category.color}-200`}>
              <div className="flex items-center gap-3 mb-4">
                <div className={`px-4 py-2 bg-${category.color}-500 text-white rounded-lg font-bold text-lg`}>
                  {category.code}
                </div>
                <div>
                  <h4 className={`font-semibold text-${category.color}-900 text-lg`}>
                    {category.name}
                  </h4>
                  <div className="flex items-center gap-4 mt-1">
                    <span className={`text-sm text-${category.color}-700`}>
                      Věk: {category.age}
                    </span>
                    {category.practical && (
                      <span className={`text-xs bg-${category.color}-200 text-${category.color}-800 px-2 py-1 rounded-full`}>
                        Praktická zkouška
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              <p className={`text-${category.color}-800 text-sm mb-4`}>
                {category.description}
              </p>
              
              <div className="space-y-2">
                <h5 className={`font-medium text-${category.color}-900 text-sm`}>Oprávnění:</h5>
                {category.includes.map((item, itemIndex) => (
                  <div key={itemIndex} className="flex items-start gap-2">
                    <CheckCircle className={`w-4 h-4 text-${category.color}-600 mt-0.5 flex-shrink-0`} />
                    <span className={`text-${category.color}-700 text-sm`}>{item}</span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Pravidla kombinace */}
      <div className="bg-slate-50 rounded-xl p-6 border border-slate-200">
        <div className="flex items-center gap-3 mb-4">
          <Shield className="w-6 h-6 text-slate-600" />
          <h3 className="text-xl font-semibold text-gray-900">
            Pravidla kombinace kategorií
          </h3>
        </div>
        
        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <h4 className="font-medium text-slate-900">Možné kombinace:</h4>
            <div className="space-y-2">
              {[
                "M a S lze získat současně v jedné zkoušce",
                "M20 a S20 jsou zjednodušené varianty",
                "C vyžaduje předchozí kategorii M nebo M20",
                "M24 vyžaduje kategorii M nebo M+S"
              ].map((rule, index) => (
                <div key={index} className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-slate-500 mt-0.5 flex-shrink-0" />
                  <span className="text-slate-700 text-sm">{rule}</span>
                </div>
              ))}
            </div>
          </div>
          
          <div className="space-y-3">
            <h4 className="font-medium text-slate-900">Doporučení:</h4>
            <div className="space-y-2">
              {[
                "Začněte s kategorií M nebo M20",
                "Přidejte S pokud chcete plachtit",
                "M24 až po získání zkušeností",
                "C pouze pro mořskou plavbu"
              ].map((tip, index) => (
                <div key={index} className="flex items-start gap-2">
                  <Info className="w-4 h-4 text-slate-500 mt-0.5 flex-shrink-0" />
                  <span className="text-slate-700 text-sm">{tip}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Upozornění */}
      <div className="bg-amber-50 rounded-xl p-6 border border-amber-200">
        <div className="flex items-start gap-3">
          <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-semibold text-amber-800 mb-2">Důležité upozornění</h4>
            <p className="text-amber-700 text-sm">
              Kategorie M24 má zvláštní postavení a její uznání lze zaručit pouze v České republice. 
              Pro mezinárodní plavbu doporučujeme kombinaci kategorií M+S.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
