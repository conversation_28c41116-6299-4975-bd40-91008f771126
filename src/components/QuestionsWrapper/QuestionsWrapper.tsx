import React, { ReactNode } from "react";

interface QuestionsWrapperProps {
  title: string | ReactNode;
  description: string;
  children: ReactNode;
}

export const QuestionsWrapper: React.FC<QuestionsWrapperProps> = ({
  title,
  description,
  children,
}) => {
  return (
    <div className="container mx-auto px-0 sm:px-4 pt-[72px] sm:pt-24 pb-16 sm:pb-20">
      <div className="max-w-4xl mx-auto">
        {/* Hlavi<PERSON>ka sekce */}
        <div className="bg-white sm:rounded-t-xl px-0 sm:px-8 pt-5 sm:pt-8 pb-4 sm:pb-6 shadow-sm border border-b-0 border-gray-200">
          <div className="px-4 sm:px-0">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-1 sm:mb-2">
              {title}
            </h1>
            <p className="text-sm sm:text-base text-gray-600">{description}</p>
          </div>
        </div>

        {/* Questions komponenta */}
        <div className="bg-white sm:rounded-b-xl px-0 sm:px-8 pb-6 sm:pb-8 shadow-lg border border-t-0 border-gray-200">
          <div className="px-2 sm:px-0">{children}</div>
        </div>

        {/* Stín papíru */}
        <div className="hidden sm:block h-4 mx-8 bg-gradient-to-b from-gray-200/50 to-transparent rounded-b-xl"></div>
      </div>
    </div>
  );
};
