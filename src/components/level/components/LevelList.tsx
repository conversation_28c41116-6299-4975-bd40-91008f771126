import React from "react";
import LevelItem from "./LevelItem";

interface LevelListProps {
  levels: number[];
}

const LevelList: React.FC<LevelListProps> = ({ levels }) => {
  return (
    <div className={`flex justify-between`}>
      {levels && levels.length > 0 ? (
        levels.map((level) => <LevelItem key={level} level={level} />)
      ) : (
        <p>No levels available</p>
      )}
    </div>
  );
};

export default LevelList;
