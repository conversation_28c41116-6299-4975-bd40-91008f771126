"use client";

import React, { useState, useEffect } from "react";
import { useAnimation } from "@/context/AnimationContext";

interface TypewriterTextProps {
  staticText: string;
  words: string[];
  additionalText?: string;
  typingSpeed?: number;
  deletingSpeed?: number;
  delayBetweenWords?: number;
}

const TypewriterText: React.FC<TypewriterTextProps> = ({
  staticText,
  words,
  additionalText,
  typingSpeed = 100,
  deletingSpeed = 50,
  delayBetweenWords = 2000,
}) => {
  // Získáme sdílený stav z kontextu
  const { currentWordIndex, setCurrentWordIndex } = useAnimation();
  const activeWords = words;

  const [currentText, setCurrentText] = useState(
    activeWords.length > 0 ? activeWords[0] : "",
  );
  const [isDeleting, setIsDeleting] = useState(false);
  // Začínáme ve stavu čekání, aby první slovo z<PERSON>o chv<PERSON><PERSON> zobrazené
  const [isWaiting, setIsWaiting] = useState(true);
  // State for cursor blinking
  const [cursorVisible, setCursorVisible] = useState(true);

  // Hlavní efekt pro animaci psaní
  useEffect(() => {
    let timeout: NodeJS.Timeout;

    if (isWaiting) {
      timeout = setTimeout(() => {
        setIsWaiting(false);
        setIsDeleting(true);
      }, delayBetweenWords);
      return () => clearTimeout(timeout);
    }

    const currentWord = activeWords[currentWordIndex];

    if (isDeleting) {
      if (currentText === "") {
        setIsDeleting(false);
        setCurrentWordIndex((prev) => (prev + 1) % activeWords.length);
      } else {
        timeout = setTimeout(() => {
          setCurrentText(currentText.slice(0, -1));
        }, deletingSpeed);
      }
    } else {
      if (currentText === currentWord) {
        setIsWaiting(true);
      } else {
        timeout = setTimeout(() => {
          setCurrentText(currentWord.slice(0, currentText.length + 1));
        }, typingSpeed);
      }
    }

    return () => clearTimeout(timeout);
  }, [
    currentText,
    currentWordIndex,
    isDeleting,
    isWaiting,
    activeWords,
    typingSpeed,
    deletingSpeed,
    delayBetweenWords,
    setCurrentWordIndex,
  ]);

  // Effect to handle cursor blinking
  useEffect(() => {
    const cursorInterval = setInterval(() => {
      setCursorVisible((prev) => !prev);
    }, 500);

    return () => clearInterval(cursorInterval);
  }, []);

  return (
    <div className="flex flex-col">
      <div className="text-3xl sm:text-4xl lg:text-5xl font-extrabold text-gray-900 leading-tight">
        {staticText}
      </div>
      <div className="text-3xl sm:text-4xl lg:text-5xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 via-blue-600 to-emerald-600 mt-2 min-h-[60px] sm:min-h-[60px] lg:min-h-[64px]">
        {currentText}
        <span style={{ opacity: cursorVisible ? 1 : 0 }}>|</span>
      </div>
      {additionalText && (
        <div className="mt-4 inline-flex items-center px-4 py-1.5 rounded-full bg-emerald-50 border border-emerald-200">
          <span className="text-lg sm:text-xl font-medium text-emerald-600">
            {additionalText}
          </span>
        </div>
      )}
    </div>
  );
};

export default TypewriterText;
