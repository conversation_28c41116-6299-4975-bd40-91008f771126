import React from "react";
import { useRouter } from "next/navigation";

interface LeaveTestModalProps {
  isOpen: boolean;
  onClose: () => void;
  href: string;
}

export const LeaveTestModal: React.FC<LeaveTestModalProps> = ({
  isOpen,
  onClose,
  href,
}) => {
  const router = useRouter();

  if (!isOpen) return null;

  const handleConfirm = () => {
    onClose();
    router.push(href);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="fixed inset-0 bg-black/50" onClick={onClose} />
      <div className="relative bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Opustit test?
        </h3>
        <p className="text-gray-600 mb-6">
          Opravdu chcete opustit test? <PERSON><PERSON><PERSON> dosavadní postup nebude uložen.
        </p>
        <div className="flex gap-3 justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Zrušit
          </button>
          <button
            onClick={handleConfirm}
            className="px-4 py-2 text-white bg-rose-600 rounded-lg hover:bg-rose-700 transition-colors"
          >
            Opustit test
          </button>
        </div>
      </div>
    </div>
  );
};
