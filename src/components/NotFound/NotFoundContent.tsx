"use client";

import React from "react";
import Link from "next/link";
import { ArrowLeft, Home } from "lucide-react";

export const NotFoundContent = () => {
  return (
    <div className="text-center max-w-2xl mx-auto">
      {/* 404 text */}
      <div className="relative">
        <h1 className="text-[100px] sm:text-[150px] font-bold text-gray-900 animate-float select-none drop-shadow-[0_15px_15px_rgba(0,0,0,0.15)] transition-all duration-300 hover:drop-shadow-[0_20px_20px_rgba(0,0,0,0.20)]">
          404
        </h1>
      </div>

      {/* Obsah */}
      <div className="mt-8 space-y-6">
        <h2 className="text-3xl font-semibold text-gray-700">
          Ups! <PERSON>y nic není
        </h2>
        <p className="text-gray-600 max-w-md mx-auto">
          <PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> hled<PERSON>, boh<PERSON><PERSON><PERSON> neexistuje nebo byla p<PERSON>.
        </p>

        {/* Dekorativní te<PERSON> */}
        <div className="flex justify-center gap-1.5 my-8">
          <div className="w-2 h-2 rounded-full bg-emerald-300 animate-bounce"></div>
          <div className="w-2 h-2 rounded-full bg-emerald-500 animate-bounce [animation-delay:0.2s]"></div>
          <div className="w-2 h-2 rounded-full bg-emerald-700 animate-bounce [animation-delay:0.4s]"></div>
        </div>

        {/* Tlačítka */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
          <button
            onClick={() => window.history.back()}
            className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-all duration-200 shadow-sm hover:shadow group"
          >
            <ArrowLeft className="mr-2 h-5 w-5 group-hover:-translate-x-1 transition-transform duration-200" />
            Zpět
          </button>

          <Link
            href="/"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-all duration-200 shadow-md hover:shadow-lg group"
          >
            <Home className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform duration-200" />
            Zpět na hlavní stránku
          </Link>
        </div>
      </div>
    </div>
  );
};
