import React from "react";
import Menu from "@/components/Menu/Menu";
import { Application_application } from "@/graphql/types/Application";

interface NotFoundPageProps {
  application?: Application_application | undefined;
  message: string;
}

const NotFound = ({ application = undefined, message }: NotFoundPageProps) => {
  return (
    <>
      <Menu application={application} />
      <div className="app-layout">
        <div className="container mx-auto pt-28 lg:pt-32 pb-20">
          <h1 className="text-3xl font-bold mb-6">{message}</h1>
        </div>
      </div>
    </>
  );
};

export default NotFound;
