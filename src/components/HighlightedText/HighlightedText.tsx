"use client";

import React from "react";
import { Car, Shield, Anchor, Ship, Siren } from "lucide-react";
import { useAnimation } from "@/context/AnimationContext";
import Link from "next/link";

const HighlightedText: React.FC = () => {
  // Získáme sdílený stav z kontextu
  const { currentWordIndex } = useAnimation();

  // Definice ikon a jejich pořadí podle AnimatedHeading
  const icons = [
    { name: "none", icon: null, color: "", slug: "" }, // Pro "jednoduše online"
    {
      name: "autoškola",
      icon: Car,
      color: "text-emerald-600",
      slug: "autoskola",
    },
    {
      name: "zbro<PERSON><PERSON> průkaz",
      icon: Shield,
      color: "text-blue-600",
      slug: "zbrojni-prukaz",
    },
    {
      name: "potápěčský průkaz",
      icon: Anchor,
      color: "text-cyan-600",
      slug: "potapecsky-prukaz",
    },
    {
      name: "kapit<PERSON> lodi",
      icon: Ship,
      color: "text-indigo-600",
      slug: "kapitanske-zkousky",
    },
    {
      name: "str<PERSON><PERSON><PERSON><PERSON>",
      icon: Siren,
      color: "text-amber-600",
      slug: "straznik",
    },
  ];

  return (
    <div className="flex items-center justify-center lg:justify-start gap-3 sm:gap-4 md:gap-6">
      {icons.slice(1).map((item, index) => {
        const Icon = item.icon;
        if (!Icon) return null;

        // Používáme sdílený stav z kontextu
        // Pro "jednoduše online" (index 0) nechceme zvýraznit žádnou ikonu
        const isActive = currentWordIndex > 0 && index === currentWordIndex - 1;

        return (
          <Link
            key={item.name}
            href={`/${item.slug}/uvod`}
            className={`transition-all duration-300 ease-in-out ${isActive ? "scale-125" : "scale-100"} group`}
          >
            <div
              className={`
                p-2 rounded-lg bg-gray-50
                ${item.color}
                ${isActive ? "ring-2 ring-offset-2 ring-gray-200 shadow-lg" : "opacity-70 hover:opacity-100"}
                transition-all duration-300
                hover:shadow-md hover:-translate-y-0.5
                relative
              `}
            >
              <Icon className="w-5 h-5" />
              <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500/10 to-blue-500/10 rounded-lg blur opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
            </div>
          </Link>
        );
      })}
    </div>
  );
};

export default HighlightedText;
