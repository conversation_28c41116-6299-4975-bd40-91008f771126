import Link from "next/link";
import dynamic from "next/dynamic";
import { getApplications } from "@/lib/services/applications";

const PWAInstallPrompt = dynamic(
  () => import("@/components/PWAInstallPrompt/PWAInstallPrompt"),
);

const Footer: React.FC = async () => {
  const currentYear = new Date().getFullYear();
  const applications = await getApplications({ withAuth: false });

  return (
    <>
      <PWAInstallPrompt />
      <footer className="relative w-full bg-gray-50 border-t border-gray-200 z-10">
        <div className="container mx-auto px-4 py-8 md:py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Logo a popis */}
            <div className="col-span-1 md:col-span-2">
              <Link
                href="/"
                className="flex items-center space-x-2 text-gray-900 hover:text-gray-600 transition-colors"
              >
                <span className="text-xl font-semibold">Aktu<PERSON>ln<PERSON> testy</span>
                <div className="flex items-center space-x-1">
                  <div className="h-3 w-3 rounded-full bg-blue-400/80"></div>
                  <div className="h-5 w-1.5 bg-blue-500"></div>
                </div>
              </Link>
              {/* Kratší verze pro mobily, delší pro větší obrazovky */}
              <p className="mt-4 text-gray-600 text-sm block md:hidden">
                Komplexní příprava na zkoušky s okamžitou zpětnou vazbou.
              </p>
              <p className="mt-4 text-gray-600 text-sm hidden md:block max-w-sm">
                Připravte se na zkoušky s našimi aktuálními testy. Nabízíme
                komplexní přípravu pro různé typy zkoušek s okamžitou zpětnou
                vazbou a vysvětlením.
              </p>
            </div>

            {/* Rychlé odkazy */}
            <div>
              <h3 className="font-semibold text-gray-900 mb-3 md:mb-4">
                Rychlé odkazy
              </h3>
              {/* Všechny odkazy v jednom sloupci */}
              <ul className="space-y-2 md:space-y-3">
                {applications.map((app) => (
                  <li key={app.slug}>
                    <Link
                      href={`/${app.slug}/uvod`}
                      className="text-gray-600 hover:text-emerald-600 transition-colors text-sm"
                    >
                      {app.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Kontakt */}
            <div>
              <h3 className="font-semibold text-gray-900 mb-3 md:mb-4">
                Kontakt
              </h3>
              <ul className="space-y-2 md:space-y-3">
                <li>
                  <Link
                    href="/kontakt"
                    className="text-gray-600 hover:text-emerald-600 transition-colors text-sm"
                  >
                    Provozovatel
                  </Link>
                </li>
                <li>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-gray-600 hover:text-emerald-600 transition-colors text-sm flex items-center gap-2"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      />
                    </svg>
                    <EMAIL>
                  </a>
                </li>
              </ul>
            </div>
          </div>

          {/* Copyright */}
          <div className="mt-8 md:mt-12 pt-6 md:pt-8 border-t border-gray-200">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-500 text-sm">
                © {currentYear} Aktuální testy
              </p>
              <div className="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-6 mt-4 md:mt-0 text-center">
                <Link
                  href="/gdpr"
                  className="text-gray-500 hover:text-emerald-600 transition-colors text-sm"
                >
                  Ochrana osobních údajů
                </Link>
                <Link
                  href="/cookies"
                  className="text-gray-500 hover:text-emerald-600 transition-colors text-sm"
                >
                  Cookies
                </Link>
                <Link
                  href="/vop"
                  className="text-gray-500 hover:text-emerald-600 transition-colors text-sm"
                >
                  Obchodní podmínky
                </Link>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </>
  );
};

export default Footer;
