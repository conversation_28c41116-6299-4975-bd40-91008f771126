"use client";

import React from "react";
import { AnimationProvider } from "@/context/AnimationContext";
import { AnimatedHeading } from "@/components/AnimatedHeading/AnimatedHeading";
import HighlightedText from "@/components/HighlightedText/HighlightedText";

interface AnimatedSectionProps {
  staticText: string;
  words: string[];
  typingSpeed?: number;
  deletingSpeed?: number;
  delayBetweenWords?: number;
  description?: string;
}

export const AnimatedSection: React.FC<AnimatedSectionProps> = ({
  staticText,
  words,
  typingSpeed = 100,
  deletingSpeed = 50,
  delayBetweenWords = 2000,
  description,
}) => {
  return (
    <AnimationProvider>
      <AnimatedHeading
        staticText={staticText}
        words={words}
        typingSpeed={typingSpeed}
        deletingSpeed={deletingSpeed}
        delayBetweenWords={delayBetweenWords}
      />

      {description && (
        <div className="text-base sm:text-xl text-gray-600 max-w-2xl mx-auto lg:mx-0 space-y-4">
          <p>{description}</p>
        </div>
      )}

      {/* Animated Icons */}
      <div className="mt-6">
        <HighlightedText />
      </div>
    </AnimationProvider>
  );
};

export default AnimatedSection;
