interface StudentCountProps {
  count: number;
  showLabel?: boolean;
}

export const getStudentLabel = (count: number): string => {
  if (count === 0) return "Studentů";
  if (count === 1) return "Student";
  if (count >= 2 && count <= 4) return "<PERSON>i";
  return "Studentů";
};

export const StudentCount: React.FC<StudentCountProps> = ({ count }) => {
  const formatCount = (count: number): string => {
    if (count < 10) return count.toString();
    if (count < 100) return `${Math.floor(count / 10) * 10}+`;
    if (count < 1000) return `${Math.floor(count / 100) * 100}+`;
    if (count < 10000) return `${Math.floor(count / 1000)}k+`;
    return "10k+";
  };

  return <>{formatCount(count)}</>;
};
