import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  Timer,
  Notebook<PERSON>en,
  CheckCircle,
  Clock,
  ArrowRight,
  Rocket,
  Flag,
} from "lucide-react";
import Image from "next/image";
import Styles from "./styles.module.css";

// Typové definice
interface AnswerProps {
  letter: string;
  text: string;
  isSelected?: boolean;
  isCorrect?: boolean;
  isIncorrect?: boolean;
}

interface AnswerData extends AnswerProps {
  key?: number | string;
}

interface FeedbackProps {
  bgColor: string;
  borderColor: string;
  textColor: string;
  title: string;
  message: string;
  icon: React.ReactNode;
}

// Pomocná funkce pro vytvoření odpovědi
const Answer: React.FC<AnswerProps> = ({
  letter,
  text,
  isSelected = false,
  isCorrect = false,
  isIncorrect = false,
}) => {
  // Určení stylů pro odpověď
  let styles = {
    letter: "bg-gray-100 text-gray-700 border-gray-200",
    text: "text-gray-700",
    container: "border-gray-200 bg-white",
  };

  if (isCorrect) {
    styles = {
      letter: "bg-green-600 text-white border-green-600",
      text: "text-green-700",
      container: "border-green-200 bg-green-50",
    };
  } else if (isIncorrect) {
    styles = {
      letter: "bg-red-600 text-white border-red-600",
      text: "text-red-700",
      container: "border-red-200 bg-red-50",
    };
  }

  // Přidání modrého okraje pro vybrané odpovědi
  if (isSelected) {
    styles.container += " ring-2 ring-blue-500";
  }

  return (
    <div
      className={`
        flex items-stretch overflow-hidden
        border rounded-xl
        transition-all duration-200
        ${styles.container}
      `}
    >
      <div
        className={`
          flex items-center justify-center
          w-10 sm:w-14
          border-r
          transition-colors duration-200
          ${styles.letter}
        `}
      >
        <span className="font-semibold text-lg sm:text-xl">{letter}</span>
      </div>

      <div className="flex-1 p-3.5 sm:p-4 text-left">
        <span
          className={`
            block text-[15px] sm:text-base
            leading-relaxed
            ${styles.text}
          `}
        >
          {text}
        </span>
      </div>
    </div>
  );
};

// Typové definice pro QuestionCard
interface QuestionCardProps {
  title?: string;
  questionText: string;
  answers: AnswerData[];
  showTimer?: boolean;
  showQuestionNumber?: boolean;
  showSection?: boolean;
  sectionName?: string;
  imageUrl?: string | null;
  feedback?: FeedbackProps | null;
}

const QuestionCard: React.FC<QuestionCardProps> = ({
  questionText,
  answers,
  showTimer = false,
  showQuestionNumber = false,
  showSection = false,
  sectionName = "",
  imageUrl = null,
  feedback = null,
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-2 sm:p-6 mb-6">
      {/* Hlavička s informacemi */}
      {(showTimer || showQuestionNumber || showSection) && (
        <div className="flex justify-between items-center mb-4">
          {showQuestionNumber && (
            <div className="text-sm text-gray-500">Otázka 3/25</div>
          )}
          {showTimer && (
            <div className="flex items-center text-sm text-gray-500">
              <Clock className="w-4 h-4 mr-1" />
              <span>19:45</span>
            </div>
          )}
          {showSection && (
            <div className="text-sm font-medium text-amber-700">
              Sekce: {sectionName}
            </div>
          )}
        </div>
      )}

      {/* Obrázek otázky */}
      {imageUrl && (
        <div className={Styles.containerImage}>
          <Image
            width={200}
            height={200}
            className={Styles.imageExamList}
            src={imageUrl}
            alt="Obrázek k otázce"
            priority={true}
            style={{
              width: "auto",
              height: "auto",
              maxWidth: "100%",
              maxHeight: "200px",
            }}
          />
        </div>
      )}

      {/* Text otázky */}
      <div className={Styles.questionTextContainer}>
        <p className="text-sm sm:text-base">{questionText}</p>
      </div>

      {/* Odpovědi */}
      <div className="space-y-3 mt-4">
        {answers.map((answer, index) => (
          <Answer
            key={index}
            letter={answer.letter}
            text={answer.text}
            isSelected={answer.isSelected}
            isCorrect={answer.isCorrect}
            isIncorrect={answer.isIncorrect}
          />
        ))}
      </div>

      {/* Zpětná vazba */}
      {feedback && (
        <div
          className={`mt-4 p-4 rounded-lg border ${feedback.bgColor} ${feedback.borderColor}`}
        >
          <div className="flex items-center mb-2">
            {feedback.icon}
            <div className={`text-sm font-medium ${feedback.textColor}`}>
              {feedback.title}
            </div>
          </div>
          <div className={`text-sm ${feedback.textColor}`}>
            {feedback.message}
          </div>
        </div>
      )}
    </div>
  );
};

export const TestModeExplanation = () => {
  return (
    <div className="py-8 sm:py-12">
      <div className="text-center mb-8 sm:mb-12">
        <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-3">
          Tři režimy testování pro efektivní přípravu
        </h2>
        <p className="text-base sm:text-lg text-gray-600 max-w-3xl mx-auto">
          Nabízíme tři různé režimy testování, které vám pomohou efektivně se
          připravit na zkoušku. Každý režim má své specifické výhody a je vhodný
          pro různé fáze přípravy.
        </p>
      </div>

      <div className="grid grid-cols-1 gap-12 max-w-4xl mx-auto">
        {/* Cvičení */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="relative p-6 pb-8">
            <div className="absolute top-0 left-0 right-0 h-1 bg-emerald-500"></div>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-5">
              <div className="flex items-center">
                <div className="p-2.5 bg-emerald-50 rounded-full">
                  <NotebookPen className="w-6 h-6 text-emerald-600" />
                </div>
                <h3 className="ml-3 text-2xl font-bold text-gray-900">
                  Cvičení
                </h3>
              </div>
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-emerald-100 text-emerald-800 font-medium text-sm mt-2 sm:mt-0 self-start">
                <Rocket className="w-4 h-4 mr-1" />
                <span>Začátek přípravy</span>
              </div>
            </div>
            <p className="text-gray-700">
              Ideální pro první seznámení s testy. Po každé odpovědi se ihned
              dozvíte, zda byla správná, a můžete se poučit z chyb.
            </p>
            <div className="mt-5 flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto">
              <div className="bg-white rounded-lg p-4 flex-1 border-l-4 border-emerald-500 shadow-sm hover:shadow transition-shadow duration-200">
                <div className="flex items-start mb-2">
                  <CheckCircle className="w-5 h-5 text-emerald-600 flex-shrink-0 mt-0.5 mr-3" />
                  <div>
                    <span className="font-semibold text-gray-900 block">
                      Okamžitá zpětná vazba
                    </span>
                    <p className="text-sm text-gray-600 mt-1">
                      Ihned víte, zda jste odpověděli správně
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 flex-1 border-l-4 border-emerald-500 shadow-sm hover:shadow transition-shadow duration-200">
                <div className="flex items-start mb-2">
                  <CheckCircle className="w-5 h-5 text-emerald-600 flex-shrink-0 mt-0.5 mr-3" />
                  <div>
                    <span className="font-semibold text-gray-900 block">
                      Pro začátečníky
                    </span>
                    <p className="text-sm text-gray-600 mt-1">
                      Ideální pro první seznámení s otázkami
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="p-3 sm:p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">
              Příklady otázek
            </h4>

            {/* Ukázka správné odpovědi */}
            <div className="mb-8">
              <h5 className="text-md font-medium text-gray-700 mb-2">
                Příklad 1: Správná odpověď
              </h5>
              <QuestionCard
                questionText="Jaká je maximální povolená rychlost v obci?"
                answers={[
                  {
                    letter: "A",
                    text: "50 km/h",
                    isCorrect: true,
                    isSelected: true,
                  },
                  { letter: "B", text: "60 km/h" },
                  { letter: "C", text: "70 km/h" },
                ]}
                feedback={{
                  bgColor: "bg-green-50",
                  borderColor: "border-green-200",
                  textColor: "text-green-700",
                  title: "Správně!",
                  message: "Maximální povolená rychlost v obci je 50 km/h.",
                  icon: <CheckCircle className="w-5 h-5 text-green-600 mr-2" />,
                }}
              />
            </div>

            {/* Ukázka chybné odpovědi */}
            <div>
              <h5 className="text-md font-medium text-gray-700 mb-2">
                Příklad 2: Chybná odpověď
              </h5>
              <QuestionCard
                questionText="Jaká je maximální povolená rychlost na dálnici?"
                answers={[
                  { letter: "A", text: "120 km/h" },
                  {
                    letter: "B",
                    text: "150 km/h",
                    isSelected: true,
                    isIncorrect: true,
                  },
                  { letter: "C", text: "130 km/h", isCorrect: true },
                ]}
                feedback={{
                  bgColor: "bg-red-50",
                  borderColor: "border-red-200",
                  textColor: "text-red-700",
                  title: "Špatně!",
                  message: "Správná odpověď je C - 130 km/h.",
                  icon: <CheckCircle className="w-5 h-5 text-red-600 mr-2" />,
                }}
              />
            </div>
          </div>
        </div>

        {/* Výuka */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="relative p-6 pb-8">
            <div className="absolute top-0 left-0 right-0 h-1 bg-blue-500"></div>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-5">
              <div className="flex items-center">
                <div className="p-2.5 bg-blue-50 rounded-full">
                  <BookOpen className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="ml-3 text-2xl font-bold text-gray-900">Výuka</h3>
              </div>
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-blue-100 text-blue-800 font-medium text-sm mt-2 sm:mt-0 self-start">
                <ArrowRight className="w-4 h-4 mr-1" />
                <span>Průběh učení</span>
              </div>
            </div>
            <p className="text-gray-700">
              Zaměřuje se na jednotlivé sekce, které si můžete procházet zvlášť.
              Ideální pro problematické části, které bývají za hodně bodů.
            </p>
            <div className="mt-5 flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto">
              <div className="bg-white rounded-lg p-4 flex-1 border-l-4 border-blue-500 shadow-sm hover:shadow transition-shadow duration-200">
                <div className="flex items-start mb-2">
                  <CheckCircle className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5 mr-3" />
                  <div>
                    <span className="font-semibold text-gray-900 block">
                      Tematické sekce
                    </span>
                    <p className="text-sm text-gray-600 mt-1">
                      Procházení otázek podle jednotlivých témat
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 flex-1 border-l-4 border-blue-500 shadow-sm hover:shadow transition-shadow duration-200">
                <div className="flex items-start mb-2">
                  <CheckCircle className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5 mr-3" />
                  <div>
                    <span className="font-semibold text-gray-900 block">
                      Důležité oblasti
                    </span>
                    <p className="text-sm text-gray-600 mt-1">
                      Zaměření na části, které bývají za více bodů
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="p-3 sm:p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">
              Sekce: Pozemní komunikace
            </h4>
            <QuestionCard
              questionText="Jaký je rozdíl mezi dálnicí a silnicí pro motorová vozidla?"
              showSection={false}
              answers={[
                {
                  letter: "A",
                  text: "Žádný, jedná se o stejný typ komunikace",
                },
                {
                  letter: "B",
                  text: "Na silnici pro motorová vozidla je nižší maximální povolená rychlost",
                  isCorrect: true,
                },
                {
                  letter: "C",
                  text: "Na dálnici se platí dálniční známka, na silnici pro motorová vozidla ne",
                },
              ]}
              feedback={null}
            />
          </div>
        </div>

        {/* Zkouška */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="relative p-6 pb-8">
            <div className="absolute top-0 left-0 right-0 h-1 bg-emerald-500"></div>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-5">
              <div className="flex items-center">
                <div className="p-2.5 bg-emerald-50 rounded-full">
                  <Timer className="w-6 h-6 text-emerald-600" />
                </div>
                <h3 className="ml-3 text-2xl font-bold text-gray-900">
                  Zkouška
                </h3>
              </div>
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-emerald-100 text-emerald-800 font-medium text-sm mt-2 sm:mt-0 self-start">
                <Flag className="w-4 h-4 mr-1" />
                <span>Závěr přípravy</span>
              </div>
            </div>
            <p className="text-gray-700">
              Cvičení na ostrou zkoušku. Odpovědi jsou vyhodnoceny až na konci
              testu, stejně jako při reálné zkoušce.
            </p>
            <div className="mt-5 flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto">
              <div className="bg-white rounded-lg p-4 flex-1 border-l-4 border-emerald-500 shadow-sm hover:shadow transition-shadow duration-200">
                <div className="flex items-start mb-2">
                  <CheckCircle className="w-5 h-5 text-emerald-600 flex-shrink-0 mt-0.5 mr-3" />
                  <div>
                    <span className="font-semibold text-gray-900 block">
                      Vyhodnocení na konci
                    </span>
                    <p className="text-sm text-gray-600 mt-1">
                      Výsledky uvidíte až po dokončení testu
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 flex-1 border-l-4 border-emerald-500 shadow-sm hover:shadow transition-shadow duration-200">
                <div className="flex items-start mb-2">
                  <CheckCircle className="w-5 h-5 text-emerald-600 flex-shrink-0 mt-0.5 mr-3" />
                  <div>
                    <span className="font-semibold text-gray-900 block">
                      Finální příprava
                    </span>
                    <p className="text-sm text-gray-600 mt-1">
                      Ideální před skutečnou zkouškou
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="p-3 sm:p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">
              Příklad otázky
            </h4>
            <QuestionCard
              questionText="Jaká je maximální povolená rychlost mimo obec?"
              showTimer={false}
              showQuestionNumber={true}
              answers={[
                { letter: "A", text: "70 km/h" },
                { letter: "B", text: "90 km/h", isSelected: true },
                { letter: "C", text: "110 km/h" },
              ]}
              feedback={null}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestModeExplanation;
