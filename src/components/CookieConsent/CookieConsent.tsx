"use client";

import React, { useEffect, useState } from "react";
import Link from "next/link";

const COOKIE_CONSENT_KEY = "cookie-consent-accepted";

export const CookieConsent: React.FC = () => {
  const [showConsent, setShowConsent] = useState(false);

  useEffect(() => {
    // Kontrola, zda uživatel již vyj<PERSON>d<PERSON><PERSON> souhlas s cookies
    const hasConsent = localStorage.getItem(COOKIE_CONSENT_KEY);

    // Pokud souhlas ještě nebyl vyjádřen, zobrazíme lištu
    if (!hasConsent) {
      setShowConsent(true);
    }
  }, []);

  const handleAccept = () => {
    // Uložení souhlasu do localStorage
    localStorage.setItem(COOKIE_CONSENT_KEY, "true");
    setShowConsent(false);
  };

  const handleDecline = () => {
    // Uložení nesouhlasu do localStorage
    localStorage.setItem(COOKIE_CONSENT_KEY, "false");
    setShowConsent(false);

    // Zde by mohla být implementace pro vypnutí cookies, které nejsou nezbytné
    // Například vypnutí Google Analytics apod.
  };

  if (!showConsent) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white shadow-lg border-t border-gray-200 p-4">
      <div className="container mx-auto max-w-6xl">
        <div className="flex flex-col md:flex-row items-center justify-between gap-4">
          <div className="flex items-start gap-3">
            <svg
              className="w-5 h-5 text-gray-600 mt-0.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <div>
              <p className="text-sm text-gray-700">
                Tato stránka používá cookies pro zlepšení uživatelského zážitku
                a analýzu návštěvnosti. Kliknutím na &#34;Přijmout&#34;
                souhlasíte s používáním cookies. Více informací najdete v našich{" "}
                <Link
                  href="/cookies"
                  className="text-blue-600 hover:text-blue-800 underline hover:no-underline"
                >
                  zásadách používání cookies
                </Link>
                .
              </p>
            </div>
          </div>
          <div className="flex gap-3 w-full md:w-auto">
            <button
              onClick={handleDecline}
              className="flex-1 md:flex-initial bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors"
            >
              Odmítnout
            </button>
            <button
              onClick={handleAccept}
              className="flex-1 md:flex-initial bg-emerald-600 hover:bg-emerald-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              Přijmout
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CookieConsent;
