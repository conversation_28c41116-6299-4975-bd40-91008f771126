import React from "react";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  goToPage: (page: number) => void;
}

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  goToPage,
}) => {
  const baseButtonClass =
    "flex items-center justify-center transition-all duration-200";
  const pageButtonClass = `
    ${baseButtonClass}
    w-10 h-10 sm:w-12 sm:h-12
    rounded-lg
    text-base sm:text-lg
    border border-transparent
    font-medium
  `;
  const activePageClass =
    "bg-emerald-600 text-white font-medium border-emerald-600";
  const inactivePageClass =
    "text-gray-700 hover:bg-gray-50 hover:border-gray-200";

  const createPageButton = (pageNum: number) => (
    <button
      key={pageNum}
      onClick={() => {
        goToPage(pageNum);
        window.scrollTo(0, 0);
      }}
      className={`
        ${pageButtonClass}
        ${currentPage === pageNum ? activePageClass : inactivePageClass}
      `}
      aria-current={currentPage === pageNum ? "page" : undefined}
      aria-label={`Stránka ${pageNum}`}
    >
      {pageNum}
    </button>
  );

  const createDots = (key: string) => (
    <span
      key={key}
      className="w-10 sm:w-12 flex items-center justify-center text-gray-400 text-base sm:text-lg"
      aria-hidden="true"
    >
      &hellip;
    </span>
  );

  const pages = [];

  if (totalPages <= 5) {
    for (let i = 1; i <= totalPages; i++) {
      pages.push(createPageButton(i));
    }
  } else {
    pages.push(createPageButton(1));

    if (currentPage <= 3) {
      pages.push(createPageButton(2));
      pages.push(createPageButton(3));
      pages.push(createDots("endDots"));
      pages.push(createPageButton(totalPages));
    } else if (currentPage >= totalPages - 2) {
      pages.push(createDots("startDots"));
      pages.push(createPageButton(totalPages - 2));
      pages.push(createPageButton(totalPages - 1));
      pages.push(createPageButton(totalPages));
    } else {
      pages.push(createDots("startDots"));
      pages.push(createPageButton(currentPage - 1));
      pages.push(createPageButton(currentPage));
      pages.push(createPageButton(currentPage + 1));
      pages.push(createDots("endDots"));
      pages.push(createPageButton(totalPages));
    }
  }

  return (
    <nav
      aria-label="Stránkování"
      className="flex items-center justify-center gap-1 sm:gap-2"
    >
      <div className="flex items-center">{pages}</div>
    </nav>
  );
};
