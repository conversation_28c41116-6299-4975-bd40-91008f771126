"use client";

import React from "react";
import { Question } from "@/app/[application]/kategorie/zkouska/[category]/Questions/Question/Question";
import { AllQuestionsCollection_allQuestionsCollection_edges_node } from "@/graphql/types/AllQuestionsCollection";

interface QuestionWrapperProps {
  question: AllQuestionsCollection_allQuestionsCollection_edges_node;
  applicationId: number;
  questionNumber?: number;
}

export const QuestionWrapper: React.FC<QuestionWrapperProps> = ({
  question,
  applicationId,
  questionNumber,
}) => {
  return (
    <Question
      question={question}
      showCorrectAnswer={true}
      applicationId={applicationId}
      isFavouriteQuestionDefault={false}
      indexQuestion={0}
      setIndexQuestion={() => {}}
      userAnswers={[]}
      setUserAnswers={() => {}}
      showCorrectAnswerAndUserAnswerByFinished={true}
      favouriteButtonPosition="bottom"
      questionNumber={questionNumber}
      autoplay={false}
    />
  );
};
