"use client";

import React, { memo, useRef, useEffect, useState } from "react";
import { Chevron<PERSON>eft, ChevronRight, Loader2 } from "lucide-react";
import { PAGE_LIMIT } from "@/constants/pagination";
import { Pagination } from "@/components/Pagination/Pagination";
import { ApolloError } from "@apollo/client";
import { Application_application } from "@/graphql/types/Application";
import { Category_category } from "@/graphql/types/Category";
import { Section_section } from "@/graphql/types/Section";
import {
  AllQuestionsCollection,
  AllQuestionsCollection_allQuestionsCollection_edges_node,
} from "@/graphql/types/AllQuestionsCollection";
import { useGetQuestions } from "@/hooks/useGetQuestions";
import { QuestionWrapper } from "./QuestionWrapper";

const MemoizedQuestionWrapper = memo(QuestionWrapper);

interface QuestionsListProps {
  applicationData: Application_application | { id: number };
  categoryData?: Category_category;
  sectionData?: Section_section;
  initialData: AllQuestionsCollection;
}

export const QuestionsList: React.FC<QuestionsListProps> = ({
  applicationData,
  categoryData,
  sectionData,
  initialData,
}) => {
  const [showBottomPagination, setShowBottomPagination] = useState(true);
  const listEndRef = useRef<HTMLDivElement>(null);

  const appData: Application_application =
    "slug" in applicationData
      ? (applicationData as Application_application)
      : {
          __typename: "ApplicationPayload",
          id: (applicationData as { id: number }).id,
          name: "",
          slug: "",
          token: "",
          price: 0,
          questionsTotalCount: 0,
          isPaid: false,
          studentsCount: 0,
          averageSuccessRate: 0,
        };

  const {
    questions,
    error,
    pageInfo,
    loading,
    currentPage,
    totalPages,
    totalCount,
    goToPage,
  } = useGetQuestions({
    applicationData: appData,
    categoryData,
    sectionId: sectionData?.id?.toString(),
    initialData,
  });

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setShowBottomPagination(!entry.isIntersecting);
      },
      {
        threshold: 0,
        rootMargin: "-200px",
      },
    );

    if (listEndRef.current) {
      observer.observe(listEndRef.current);
    }

    return () => observer.disconnect();
  }, []);

  if (error) {
    return (
      <div className="p-4 text-red-600">
        Error: {(error as Error | ApolloError).message}
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      {/* Pagination Controls - Top - Fixed */}
      <div className="fixed top-0 left-0 right-0 z-20 bg-white/80 backdrop-blur-sm border-b">
        <div className="py-2 sm:py-3 px-3 sm:px-4">
          <div className="flex items-center justify-between text-xs sm:text-sm">
            <div className="text-gray-600">
              {totalCount > 0 ? (
                <>
                  {(currentPage - 1) * PAGE_LIMIT + 1}-
                  {Math.min(currentPage * PAGE_LIMIT, totalCount)}{" "}
                  <span className="hidden sm:inline">
                    z {totalCount} otázek
                  </span>
                  <span className="inline sm:hidden">/ {totalCount}</span>
                </>
              ) : (
                "Žádné otázky"
              )}
            </div>

            {loading && (
              <div className="flex items-center gap-1 sm:gap-2 text-gray-600">
                <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 animate-spin" />
                <span className="hidden sm:inline">Načítám...</span>
              </div>
            )}
          </div>
        </div>
      </div>

      <main className="space-y-4">
        {questions.map(
          (
            question: AllQuestionsCollection_allQuestionsCollection_edges_node,
            index: number,
          ) => {
            const questionNumber = (currentPage - 1) * PAGE_LIMIT + index + 1;
            return (
              <div
                key={question.id}
                className="bg-white rounded-lg shadow-md border border-gray-200"
              >
                <MemoizedQuestionWrapper
                  question={question}
                  applicationId={appData.id}
                  questionNumber={questionNumber}
                />
              </div>
            );
          },
        )}
      </main>

      {/* Sentinel element pro Intersection Observer */}
      <div
        ref={listEndRef}
        className="h-[200px] pointer-events-none"
        style={{ marginBottom: "-200px" }}
      />

      {/* Bottom Pagination */}
      <div
        className={`
          ${showBottomPagination ? "fixed bottom-0 left-0 right-0" : "relative"}
          z-20 bg-white/80 backdrop-blur-sm border-t
          transition-all duration-300
        `}
      >
        <div className="max-w-4xl mx-auto">
          <div className="py-2 sm:py-3">
            <div className="flex justify-center items-center gap-1 sm:gap-2">
              <button
                onClick={() => {
                  goToPage(currentPage - 1);
                  window.scrollTo(0, 0);
                }}
                disabled={currentPage === 1 || loading}
                className="p-1.5 sm:p-2 rounded-lg bg-white hover:bg-gray-50 text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                <ChevronLeft className="w-4 h-4 sm:w-5 sm:h-5" />
              </button>

              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                goToPage={goToPage}
              />

              <button
                onClick={() => {
                  goToPage(currentPage + 1);
                  window.scrollTo(0, 0);
                }}
                disabled={!pageInfo.hasNextPage || loading}
                className="p-1.5 sm:p-2 rounded-lg bg-white hover:bg-gray-50 text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
