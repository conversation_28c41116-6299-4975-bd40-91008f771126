"use server";

import type { ForgottenPasswordFormData } from "@/types/forgottenPassword";
import { FORM_GLOBAL_ERROR_MESSAGE } from "@/components/ForgottenPasswordForm/constants/translations";

export const mappedValidationError = async (
  message: string,
  rawData: ForgottenPasswordFormData,
) => {
  const validationMap: { [key: string]: string } = {
    "Email validation": "email",
  };

  for (const [key, value] of Object.entries(validationMap)) {
    if (message.startsWith(key)) {
      return {
        success: false,
        message: FORM_GLOBAL_ERROR_MESSAGE,
        errors: { [value]: [message.replace(`${key}: `, "")] },
        inputs: rawData,
      };
    }
  }

  return {
    success: false,
    message,
    inputs: rawData,
  };
};
