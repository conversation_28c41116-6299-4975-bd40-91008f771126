"use server";

import { z } from "zod";
import type {
  ForgottenPasswordFormData,
  ActionResponse,
} from "@/types/forgottenPassword";
import { getClient } from "@/lib/client";
import { ApolloError } from "@apollo/client";
import { FORGOTTEN_PASSWORD_MUTATION } from "@/components/ForgottenPasswordForm/constants/getRegistrationMutation";
import { FORM_GLOBAL_ERROR_MESSAGE } from "@/components/ForgottenPasswordForm/constants/translations";
import { mappedValidationError } from "@/components/ForgottenPasswordForm/validationApiErrorMapper";

const addressSchema = z.object({
  email: z.string().trim().email("Email neni validní"),
});

const sendForgottenPassword = async (
  email: string,
  rawData: ForgottenPasswordFormData,
): Promise<ActionResponse> => {
  try {
    const client = getClient();
    await client.mutate({
      mutation: FORGOTTEN_PASSWORD_MUTATION,
      variables: {
        email: email,
      },
    });

    return {
      success: true,
      message: "Email byl odeslán. Nyní postupujte podle instrukcí v emailu.",
    };
  } catch (e: unknown) {
    if (e instanceof ApolloError) {
      return mappedValidationError(e.message, rawData);
    }
  }

  return {
    success: true,
    message: "Send email successfully.",
  };
};

export const validateAndSendForgottenPasswordData = async (
  prevState: ActionResponse | null,
  formData: FormData,
): Promise<ActionResponse> => {
  try {
    const rawData: ForgottenPasswordFormData = {
      email: formData.get("email") as string,
    };

    // Validate the form data
    const validatedData = addressSchema.safeParse(rawData);

    if (!validatedData.success) {
      return {
        success: false,
        message: FORM_GLOBAL_ERROR_MESSAGE,
        errors: validatedData.error.flatten().fieldErrors,
        inputs: rawData,
      };
    }

    return sendForgottenPassword(validatedData.data.email, rawData);

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error: unknown) {
    return {
      success: false,
      message: "An unexpected error occurred",
    };
  }
};
