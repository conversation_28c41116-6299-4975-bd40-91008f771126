"use client";

import { useActionState, useState, ChangeEvent } from "react";
import Link from "next/link";
import type { ActionResponse } from "@/types/forgottenPassword";
import { handleSubmitForm } from "@/components/ForgottenPasswordForm/handleSubmitForm";
import useSuc<PERSON><PERSON>oginHandler from "@/components/ForgottenPasswordForm/hooks/useSuccessLoginHandler";
import { handleFormStateChange } from "@/utils/handleFormStateChange";

const initialState: ActionResponse = {
  success: false,
  message: "",
};

export default function ForgottenPasswordForm() {
  const [state, action, isPending] = useActionState(
    handleSubmitForm,
    initialState,
  );
  const [, forceUpdate] = useState(0);

  useSuccessLoginHandler(state?.success || false);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name } = e.target;
    handleFormStateChange(name, state);
    forceUpdate((prev) => prev + 1);
  };

  return (
    <form className="space-y-6" action={action}>
      <div>
        <label
          htmlFor="email"
          className="block text-sm font-medium leading-6 text-gray-900"
        >
          Emailová adresa
        </label>
        <div className="mt-2">
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            defaultValue={state.inputs?.email}
            onChange={handleChange}
            className={`block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ${
              state?.errors?.email
                ? "ring-red-300 placeholder:text-red-300 focus:ring-red-500"
                : "ring-gray-300 placeholder:text-gray-400 focus:ring-emerald-600"
            } sm:text-sm sm:leading-6`}
          />
          {state?.errors?.email && (
            <p className="mt-2 text-sm text-red-600" id="email-error">
              {state.errors.email[0]}
            </p>
          )}
        </div>
      </div>

      <div>
        <button
          type="submit"
          disabled={isPending}
          className="group/button flex w-full items-center justify-center gap-2 px-6 py-2.5 bg-gradient-to-br from-emerald-600 to-emerald-700 text-white font-medium rounded-xl hover:from-emerald-500 hover:to-emerald-600 transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-70 disabled:cursor-not-allowed"
        >
          {isPending ? (
            <>
              <svg
                className="animate-spin h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
              <span>Odesílám...</span>
            </>
          ) : (
            <>
              <svg
                className="w-5 h-5 text-emerald-200"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
              <span className="group-hover/button:translate-x-0.5 transition-transform duration-150">
                Odeslat instrukce
              </span>
            </>
          )}
        </button>
      </div>

      {!state?.success && state?.message && (
        <div
          className="rounded-md bg-red-50 p-4 mt-4"
          aria-live="polite"
          aria-atomic="true"
        >
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                {state.message}
              </h3>
            </div>
          </div>
        </div>
      )}

      <div className="mt-10">
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-200" />
          </div>
          <div className="relative flex justify-center text-sm font-medium leading-6">
            <span className="bg-white px-6 text-gray-900">nebo se můžete</span>
          </div>
        </div>

        <div className="mt-6 grid grid-cols-2 gap-4">
          <Link
            href="/registrace"
            className="flex w-full items-center justify-center gap-3 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:ring-2 focus-visible:ring-emerald-600"
          >
            Registrovat
          </Link>
          <Link
            href="/prihlaseni"
            className="flex w-full items-center justify-center gap-3 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:ring-2 focus-visible:ring-emerald-600"
          >
            Přihlásit
          </Link>
        </div>
      </div>
    </form>
  );
}
