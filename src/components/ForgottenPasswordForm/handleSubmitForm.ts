"use client";

import { ActionResponse } from "@/types/forgottenPassword";
import { validateAndSendForgottenPasswordData } from "@/components/ForgottenPasswordForm/validateAndSendForgottenPasswordData";

export const handleSubmitForm = async (
  prevState: ActionResponse | null,
  formData: FormData,
): Promise<ActionResponse> => {
  const result = await validateAndSendForgottenPasswordData(
    prevState,
    formData,
  );

  if (result.success) {
    return result;
  }

  return result;
};
