import { X, Check, UserPlus, LogIn } from "lucide-react";
import Link from "next/link";

interface Props {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  features?: string[];
}

export const RegisterPromptModal = ({
  isOpen,
  onClose,
  title = "Oblíbené otázky",
  features = [
    "Ukládat si otázky mezi oblíbené",
    "Efektivnější organizace vašeho studia",
    "Sledovat svůj pokrok v učení",
  ],
}: Props) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center px-6">
      <div
        className="fixed inset-0 bg-black/50"
        onClick={onClose}
        aria-hidden="true"
      />
      <div className="relative bg-white shadow-xl rounded-2xl p-8 max-w-md w-full">
        <button
          onClick={onClose}
          className="absolute right-4 top-4 text-gray-400 hover:text-gray-600"
        >
          <X className="w-5 h-5" />
        </button>

        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
          <p className="mt-2 text-sm text-gray-600">
            Vytvořte si účet zdarma a získejte možnost:
          </p>
        </div>

        <ul className="space-y-3 mb-8">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center text-gray-700">
              <Check className="w-5 h-5 mr-3 text-emerald-500 flex-shrink-0" />
              {feature}
            </li>
          ))}
        </ul>

        <div className="space-y-3">
          <Link
            href="/prihlaseni"
            className="group/button flex w-full items-center justify-center gap-3 px-6 py-2.5 bg-gradient-to-br from-emerald-600 to-emerald-700 text-white font-medium rounded-xl hover:from-emerald-500 hover:to-emerald-600 transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <LogIn className="w-5 h-5" />
            <span className="group-hover/button:translate-x-0.5 transition-transform duration-150">
              Přihlásit se
            </span>
          </Link>
          <Link
            href="/registrace"
            className="flex items-center justify-center w-full gap-2 px-4 py-2.5 text-emerald-600 bg-white border border-emerald-600 rounded-lg hover:bg-emerald-50 transition-colors"
          >
            <UserPlus className="w-5 h-5" />
            <span>Vytvořit účet zdarma</span>
          </Link>
        </div>
      </div>
    </div>
  );
};
