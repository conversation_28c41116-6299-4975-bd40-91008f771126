"use client";

import { useActionState, useState, ChangeEvent } from "react";
import Link from "next/link";
import type { ActionResponse } from "@/types/login";
import { handleSubmitForm } from "@/components/LoginForm/handleSubmitForm";
import useSuccessLoginHandler from "@/components/LoginForm/hooks/useSuccessLoginHandler";
import { handleFormStateChange } from "@/utils/handleFormStateChange";

const initialState: ActionResponse = {
  success: false,
  message: "",
};

export default function LoginForm() {
  const [state, action, isPending] = useActionState(
    (
      previousState: ActionResponse,
      formData: FormData,
    ): Promise<ActionResponse> => handleSubmitForm(previousState, formData),
    initialState,
  );
  const [, forceUpdate] = useState(0);

  useSuccessLoginHandler(state?.success || false);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name } = e.target;
    handleFormStateChange(name, state);
    forceUpdate((prev) => prev + 1);
  };

  return (
    <form className="space-y-5" action={action}>
      <div>
        <label
          htmlFor="email"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Email
        </label>
        <input
          id="email"
          name="email"
          type="email"
          defaultValue={state.inputs?.email}
          onChange={handleChange}
          aria-describedby="email-error"
          required
          className={`${
            state?.errors?.email
              ? "border-red-500 ring-red-500"
              : "border-gray-300 ring-gray-300"
          } block w-full rounded-lg py-2.5 px-3 text-gray-900 placeholder:text-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 border-[1px] transition-colors duration-200`}
          placeholder="<EMAIL>"
        />
        {state?.errors?.email && (
          <p className="mt-1 text-sm text-red-600" id="email-error">
            {state.errors.email[0]}
          </p>
        )}
      </div>

      <div>
        <label
          htmlFor="password"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Heslo
        </label>
        <input
          id="password"
          name="password"
          type="password"
          defaultValue={state.inputs?.password}
          onChange={handleChange}
          aria-describedby="password-error"
          required
          className={`${
            state?.errors?.password
              ? "border-red-500 ring-red-500"
              : "border-gray-300 ring-gray-300"
          } block w-full rounded-lg py-2.5 px-3 text-gray-900 placeholder:text-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 border-[1px] transition-colors duration-200`}
          placeholder="••••••••"
        />
        {state?.errors?.password && (
          <p className="mt-1 text-sm text-red-600" id="password-error">
            {state.errors.password[0]}
          </p>
        )}
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <input
            id="remember"
            name="remember"
            type="checkbox"
            className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <label
            htmlFor="remember"
            className="ml-2 block text-sm text-gray-700"
          >
            Zapamatovat si mě
          </label>
        </div>
        <Link
          href="/zapomenute-heslo"
          className="text-sm font-medium text-blue-600 hover:text-blue-500 transition-colors duration-200"
        >
          Zapomenuté heslo?
        </Link>
      </div>

      <div className="space-y-3">
        {!state?.success && state?.message && (
          <div
            className="rounded-lg bg-red-50 p-3"
            role="alert"
            aria-live="polite"
          >
            <p className="text-sm text-red-600">{state.message}</p>
          </div>
        )}

        <button
          type="submit"
          disabled={isPending}
          className="group/button flex w-full items-center justify-center gap-2 px-6 py-2.5 bg-gradient-to-br from-emerald-600 to-emerald-700 text-white font-medium rounded-xl hover:from-emerald-500 hover:to-emerald-600 transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-70 disabled:cursor-not-allowed"
        >
          {isPending ? (
            <>
              <svg
                className="animate-spin h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              <span>Přihlašování...</span>
            </>
          ) : (
            <>
              <svg
                className="w-5 h-5 text-emerald-200"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"
                />
              </svg>
              <span className="group-hover/button:translate-x-0.5 transition-transform duration-150">
                Přihlásit se
              </span>
            </>
          )}
        </button>

        <div className="text-center">
          <Link
            href="/registrace"
            className="text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors duration-200"
          >
            Nemáte účet? Zaregistrujte se
          </Link>
        </div>
      </div>
    </form>
  );
}
