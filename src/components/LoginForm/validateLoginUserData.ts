"use server";

import { z } from "zod";
import { FORM_GLOBAL_ERROR_MESSAGE } from "@/app/registrace/constants/translations";
import type { LoginFormData, ActionResponse } from "@/types/login";

const addressSchema = z.object({
  email: z.string().trim().email("Email neni vallidní"),
  password: z.string().min(1, "<PERSON><PERSON><PERSON> b<PERSON>t prázdné"),
});

export const validateLoginUserData = async (
  prevState: ActionResponse | null,
  formData: FormData,
): Promise<ActionResponse> => {
  try {
    const rawData: LoginFormData = {
      email: formData.get("email") as string,
      password: formData.get("password") as string,
    };

    // Validate the form data
    const validatedData = addressSchema.safeParse(rawData);

    if (!validatedData.success) {
      return {
        success: false,
        message: FORM_GLOBAL_ERROR_MESSAGE,
        errors: validatedData.error.flatten().fieldErrors,
        inputs: rawData,
      };
    }

    return {
      success: true,
      message: "User validated successfully!",
    };

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error: unknown) {
    return {
      success: false,
      message: "An unexpected error occurred",
    };
  }
};
