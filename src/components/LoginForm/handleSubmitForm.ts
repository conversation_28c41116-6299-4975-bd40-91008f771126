"use client";

import { ActionResponse, type LoginFormData } from "@/types/login";
import { signIn } from "next-auth/react";
import { validateLoginUserData } from "@/components/LoginForm/validateLoginUserData";

export const handleSubmitForm = async (
  prevState: ActionResponse | null,
  formData: FormData,
): Promise<ActionResponse> => {
  const result = await validateLoginUserData(prevState, formData);

  if (result.success) {
    return await handleSignIn(result, formData);
  }

  return result;
};

const handleSignIn = async (
  result: ActionResponse,
  formData: FormData,
): Promise<ActionResponse> => {
  const signInResult = await signIn("credentials", {
    redirect: false,
    email: formData.get("email") as string,
    password: formData.get("password") as string,
  });

  const rawData: LoginFormData = {
    email: formData.get("email") as string,
    password: formData.get("password") as string,
  };

  if (signInResult?.error) {
    result.success = false;
    result.message = "Přihlášení se nezdařilo. Zkontrolujte zadané údaje.";
    result.inputs = rawData;
  }

  return result;
};
