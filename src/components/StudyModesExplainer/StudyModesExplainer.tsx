"use client";

import React, { useState } from "react";
import { Book<PERSON><PERSON>, NotebookPen, Timer, X, ArrowRight } from "lucide-react";

interface StudyModesExplainerProps {
  title: string;
}

export const StudyModesExplainer: React.FC<StudyModesExplainerProps> = ({
  title,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  return (
    <div className="mb-12 text-center">
      <h1 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent pb-3">
        {title}
      </h1>

      {/* Tip s vysvětlením */}
      <div
        className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg text-blue-700 text-sm flex items-center justify-center gap-2 cursor-pointer hover:bg-blue-100 hover:border-blue-300 transition-all duration-200 shadow-sm hover:shadow-md"
        onClick={openModal}
      >
        <span>
          <strong>TIP:</strong> Klikněte zde pro vysvětlení režimů studia
        </span>
        <ArrowRight className="w-4 h-4 text-blue-600 ml-1" />
      </div>

      {/* Modální okno s vysvětlením */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center px-6">
          <div
            className="fixed inset-0 bg-black/50"
            onClick={closeModal}
            aria-hidden="true"
          />
          <div className="relative bg-white shadow-xl rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            {/* Tlačítko pro zavření */}
            <button
              onClick={closeModal}
              className="absolute right-4 top-4 text-gray-400 hover:text-gray-600 transition-colors"
              aria-label="Zavřít"
            >
              <X className="w-5 h-5" />
            </button>

            <div className="text-left sm:text-center mb-4 sm:mb-6 pt-6 sm:pt-8 px-4 sm:px-8">
              <h2 className="text-2xl font-bold text-gray-900">
                Režimy studia
              </h2>
            </div>

            {/* Obsah modálu */}
            <div className="px-4 sm:px-8 pb-6 space-y-4">
              {/* Výuka */}
              <div className="bg-gray-50 border-l-4 border-emerald-500 rounded-lg p-3 sm:p-4 transition-all hover:shadow-md">
                <div className="flex items-center gap-2 sm:gap-3 mb-2">
                  <div className="flex-shrink-0 bg-gray-100 p-2 sm:p-3 rounded-full shadow-sm">
                    <BookOpen className="w-5 h-5 sm:w-6 sm:h-6 text-emerald-600" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-800">Výuka</h3>
                </div>
                <ul className="text-gray-700 space-y-1 sm:space-y-2 pl-3 sm:pl-4">
                  <li className="flex items-start gap-2">
                    <span className="text-emerald-500 flex-shrink-0 text-lg leading-none mt-0.5">
                      •
                    </span>
                    <span className="break-words text-left">
                      Obsahuje všechny otázky
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-emerald-500 flex-shrink-0 text-lg leading-none mt-0.5">
                      •
                    </span>
                    <span className="break-words text-left">
                      Umožňuje systematické procházení materiálů
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-emerald-500 flex-shrink-0 text-lg leading-none mt-0.5">
                      •
                    </span>
                    <span className="break-words text-left">
                      Vhodné pro důkladné nastudování
                    </span>
                  </li>
                </ul>
              </div>

              {/* Cvičení */}
              <div className="bg-gray-50 border-l-4 border-emerald-500 rounded-lg p-3 sm:p-4 transition-all hover:shadow-md">
                <div className="flex items-center gap-2 sm:gap-3 mb-2">
                  <div className="flex-shrink-0 bg-gray-100 p-2 sm:p-3 rounded-full shadow-sm">
                    <NotebookPen className="w-5 h-5 sm:w-6 sm:h-6 text-emerald-600" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-800">Cvičení</h3>
                </div>
                <ul className="text-gray-700 space-y-1 sm:space-y-2 pl-3 sm:pl-4">
                  <li className="flex items-start gap-2">
                    <span className="text-emerald-500 flex-shrink-0 text-lg leading-none mt-0.5">
                      •
                    </span>
                    <span className="break-words text-left">
                      Zobrazí ihned správnou odpověď po každé otázce
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-emerald-500 flex-shrink-0 text-lg leading-none mt-0.5">
                      •
                    </span>
                    <span className="break-words text-left">
                      Ideální pro aktivní učení a procvičování
                    </span>
                  </li>
                </ul>
              </div>

              {/* Zkouška */}
              <div className="bg-gray-50 border-l-4 border-emerald-500 rounded-lg p-3 sm:p-4 transition-all hover:shadow-md">
                <div className="flex items-center gap-2 sm:gap-3 mb-2">
                  <div className="flex-shrink-0 bg-gray-100 p-2 sm:p-3 rounded-full shadow-sm">
                    <Timer className="w-5 h-5 sm:w-6 sm:h-6 text-emerald-600" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-800">Zkouška</h3>
                </div>
                <ul className="text-gray-700 space-y-1 sm:space-y-2 pl-3 sm:pl-4">
                  <li className="flex items-start gap-2">
                    <span className="text-emerald-500 flex-shrink-0 text-lg leading-none mt-0.5">
                      •
                    </span>
                    <span className="break-words text-left">
                      Simuluje skutečnou zkoušku s vyhodnocením na konci
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-emerald-500 flex-shrink-0 text-lg leading-none mt-0.5">
                      •
                    </span>
                    <span className="break-words text-left">
                      Nejlepší způsob, jak si ověřit své znalosti
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-emerald-500 flex-shrink-0 text-lg leading-none mt-0.5">
                      •
                    </span>
                    <span className="break-words text-left">
                      Připraví vás na reálnou testovou situaci
                    </span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Patička modálu */}
            <div className="px-4 sm:px-8 pb-6 sm:pb-8 pt-2 text-left sm:text-center">
              <button
                onClick={closeModal}
                className="group/button flex w-full items-center justify-center gap-3 px-4 sm:px-6 py-2.5 bg-gradient-to-br from-emerald-600 to-emerald-700 text-white font-medium rounded-xl hover:from-emerald-500 hover:to-emerald-600 transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <span className="group-hover/button:translate-x-0.5 transition-transform duration-150">
                  Rozumím
                </span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StudyModesExplainer;
