"use server";

import { z } from "zod";
import { getClient } from "@/lib/client";
import { ApolloError } from "@apollo/client";
import { CHANGE_FORGOTTEN_PASSWORD_MUTATION } from "@/components/ChangeForgottenPasswordForm/constants/getRegistrationMutation";
import { FORM_GLOBAL_ERROR_MESSAGE } from "@/components/ChangeForgottenPasswordForm/constants/translations";
import { mappedValidationError } from "@/components/ChangeForgottenPasswordForm/validationApiErrorMapper";
import type {
  ChangeForgottenPasswordFormData,
  ActionResponse,
} from "@/types/changeForgottenPassword";

const addressSchema = z.object({
  password: z.string().min(4, "Heslo musí být minimálně 4 znaky dlouhé"),
});

const changeForgottenPassword = async (
  password: string,
  rawData: ChangeForgottenPasswordFormData,
  forgottenPasswordToken: string,
): Promise<ActionResponse> => {
  try {
    const client = getClient();
    await client.mutate({
      mutation: CHANGE_FORGOTTEN_PASSWORD_MUTATION,
      variables: {
        forgottenPasswordToken: forgottenPasswordToken,
        password: password,
      },
    });

    return {
      success: true,
      message: "Heslo bylo změněno. Nyní se můžete přihlásit.",
    };
  } catch (e: unknown) {
    if (e instanceof ApolloError) {
      return mappedValidationError(e.message, rawData);
    }
  }

  return {
    success: true,
    message: "Change password successfully.",
  };
};

export const validateAndChangeForgottenPasswordData = async (
  prevState: ActionResponse | null,
  formData: FormData,
  forgottenPasswordToken: string,
): Promise<ActionResponse> => {
  try {
    const rawData: ChangeForgottenPasswordFormData = {
      password: formData.get("password") as string,
    };

    // Validate the form data
    const validatedData = addressSchema.safeParse(rawData);

    if (!validatedData.success) {
      return {
        success: false,
        message: FORM_GLOBAL_ERROR_MESSAGE,
        errors: validatedData.error.flatten().fieldErrors,
        inputs: rawData,
      };
    }

    return changeForgottenPassword(
      validatedData.data.password,
      rawData,
      forgottenPasswordToken,
    );

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error: unknown) {
    return {
      success: false,
      message: "An unexpected error occurred",
    };
  }
};
