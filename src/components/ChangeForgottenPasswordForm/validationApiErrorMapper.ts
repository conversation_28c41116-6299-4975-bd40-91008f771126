"use server";

import { FORM_GLOBAL_ERROR_MESSAGE } from "@/components/ChangeForgottenPasswordForm/constants/translations";
import { ChangeForgottenPasswordFormData } from "@/types/changeForgottenPassword";

export const mappedValidationError = async (
  message: string,
  rawData: ChangeForgottenPasswordFormData,
) => {
  const validationMap: { [key: string]: string } = {
    "Password validation": "password",
  };

  for (const [key, value] of Object.entries(validationMap)) {
    if (message.startsWith(key)) {
      return {
        success: false,
        message: FORM_GLOBAL_ERROR_MESSAGE,
        errors: { [value]: [message.replace(`${key}: `, "")] },
        inputs: rawData,
      };
    }
  }

  return {
    success: false,
    message,
    inputs: rawData,
  };
};
