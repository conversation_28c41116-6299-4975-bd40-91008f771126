"use client";

import React from "react";
import { useActionState, useState, ChangeEvent } from "react";
import type { ActionResponse } from "@/types/changeForgottenPassword";
import { handleSubmitForm } from "@/components/ChangeForgottenPasswordForm/handleSubmitForm";
import useSuccessLoginHandler from "@/components/ChangeForgottenPasswordForm/hooks/useSuccessLoginHandler";
import { handleFormStateChange } from "@/utils/handleFormStateChange";

const initialState: ActionResponse = {
  success: false,
  message: "",
};

interface ChangeForgottenPasswordFormProps {
  forgottenPasswordToken: string;
}

const ChangeForgottenPasswordForm: React.FC<
  ChangeForgottenPasswordFormProps
> = ({ forgottenPasswordToken }) => {
  const [state, action, isPending] = useActionState(
    (
      previousState: ActionResponse,
      formData: FormData,
    ): Promise<ActionResponse> =>
      handleSubmitForm(previousState, formData, forgottenPasswordToken),
    initialState,
  );
  const [, forceUpdate] = useState(0);

  useSuccessLoginHandler(state?.success || false);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name } = e.target;
    handleFormStateChange(name, state);
    forceUpdate((prev) => prev + 1);
  };

  return (
    <form className="space-y-6" action={action}>
      <div>
        <label
          htmlFor="password"
          className="block text-sm font-medium leading-6 text-gray-900"
        >
          Zadejte nové heslo
        </label>
        <div className="mt-2">
          <input
            id="password"
            name="password"
            type="password"
            defaultValue={state.inputs?.password}
            onChange={handleChange}
            aria-describedby="password-error"
            required
            className={`block w-full rounded-md border-0 px-3 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ${
              state?.errors?.password
                ? "ring-red-300 placeholder:text-red-300 focus:ring-red-500"
                : "ring-gray-300 placeholder:text-gray-400 focus:ring-emerald-600"
            } sm:text-sm sm:leading-6`}
          />
          {state?.errors?.password && (
            <p className="mt-2 text-sm text-red-600" id="password-error">
              {state.errors.password[0]}
            </p>
          )}
        </div>
      </div>

      <div>
        <button
          type="submit"
          disabled={isPending}
          className="group/button flex w-full items-center justify-center gap-2 px-6 py-2.5 bg-gradient-to-br from-emerald-600 to-emerald-700 text-white font-medium rounded-xl hover:from-emerald-500 hover:to-emerald-600 transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-70 disabled:cursor-not-allowed"
        >
          {isPending ? (
            <>
              <svg
                className="animate-spin h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
              <span>Zpracováváme žádost...</span>
            </>
          ) : (
            <>
              <svg
                className="w-5 h-5 text-emerald-200"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                />
              </svg>
              <span className="group-hover/button:translate-x-0.5 transition-transform duration-150">
                Potvrdit žádost
              </span>
            </>
          )}
        </button>
      </div>

      {!state?.success && state?.message && (
        <div
          className="rounded-md bg-red-50 p-4"
          aria-live="polite"
          aria-atomic="true"
        >
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                {state.message}
              </h3>
            </div>
          </div>
        </div>
      )}
    </form>
  );
};

export default ChangeForgottenPasswordForm;
