"use client";

import { ActionResponse } from "@/types/changeForgottenPassword";
import { validateAndChangeForgottenPasswordData } from "@/components/ChangeForgottenPasswordForm/validateAndChangeForgottenPasswordData";

export const handleSubmitForm = async (
  prevState: ActionResponse | null,
  formData: FormData,
  forgottenPasswordToken: string,
): Promise<ActionResponse> => {
  const result = await validateAndChangeForgottenPasswordData(
    prevState,
    formData,
    forgottenPasswordToken,
  );

  if (result.success) {
    return result;
  }

  return result;
};
