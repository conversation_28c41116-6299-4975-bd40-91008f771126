import React, { ReactNode } from "react";
import BoxView from "../BoxView/BoxView";
import Styles from "./styles.module.css";

interface Props {
  onPress: () => void;
  text: string;
  type: "A" | "B";

  children?: ReactNode;
  isLoading?: boolean;
  isClient?: boolean;
  className?: string; // Přidáme možnost přidat vlastní třídy
}

export const Buttons = (props: Props) => {
  if (props.isClient) {
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    ("use client");
  }

  if (props.type === "A") {
    if (props.isLoading) {
      return (
        <div className={`${Styles.newExamButton} ${props.className || ""}`}>
          <span>načítám</span>
        </div>
      );
    }
    return (
      <div
        onClick={props.onPress}
        className={`${Styles.newExamButton} ${props.className || ""}`}
      >
        {props.text !== "" ? (
          <span className={Styles.newExamText}>{props.text}</span>
        ) : (
          props.children
        )}
      </div>
    );
  }

  if (props.type === "B") {
    if (props.isLoading) {
      return (
        <BoxView isButton>
          <div
            className={`${Styles.buttonBTouchable} ${props.className || ""}`}
          >
            <span>Načítám</span>
          </div>
        </BoxView>
      );
    }
    return (
      <BoxView isButton>
        <div
          onClick={props.onPress}
          className={`${Styles.buttonBTouchable} ${props.className || ""}`}
        >
          {props.text !== "" ? (
            <span className={Styles.titleVariantB}>{props.text}</span>
          ) : (
            props.children
          )}
        </div>
      </BoxView>
    );
  }

  return null;
};
