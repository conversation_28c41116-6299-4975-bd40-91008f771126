/* New exam Button Styles */
.newExamButton {
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 12px;
  background-color: #4b9346;
  border-radius: 15px;
  border-bottom: 2px solid #999;
  cursor: pointer;
  width: 100%;
  margin-bottom: 8px;
  text-align: center;
}

@media (min-width: 640px) {
  .newExamButton {
    padding: 15px;
    margin-bottom: 10px;
    max-height: 60px;
  }
}

.newExamButton:hover {
  background-color: #418c3b;
}

/* New exam Text Styles */
.newExamText {
  color: white;
  font-size: 16px;
  text-align: center;
}

@media (min-width: 640px) {
  .newExamText {
    font-size: 20px;
  }
}

/* Title Variant B Styles */
.titleVariantB {
  font-size: 16px;
  color: #888;
}

@media (min-width: 640px) {
  .titleVariantB {
    font-size: 18px;
  }
}

/* Button Touchable Styles */
.buttonBTouchable {
  padding: 0;
  cursor: pointer;
  flex: 1;
  width: 100%;
}
