import React from "react";
import dynamic from "next/dynamic";

const TypewriterText = dynamic(
  () => import("@/components/TypewriterText/TypewriterText"),
);

interface AnimatedHeadingProps {
  staticText: string;
  words: string[];
  typingSpeed?: number;
  deletingSpeed?: number;
  delayBetweenWords?: number;
}

export function AnimatedHeading({
  staticText,
  words,
  typingSpeed = 100,
  deletingSpeed = 50,
  delayBetweenWords = 2000,
}: AnimatedHeadingProps) {
  // Fallback obsah pro SSR
  const fallbackContent = (
    <div className="flex flex-col">
      <div className="text-3xl sm:text-4xl lg:text-5xl font-extrabold text-gray-900 leading-tight">
        {staticText}
      </div>
      <div className="text-3xl sm:text-4xl lg:text-5xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 via-blue-600 to-emerald-600 mt-2 min-h-[60px] sm:min-h-[60px] lg:min-h-[64px]">
        {words[0]}
      </div>
    </div>
  );

  return (
    <>
      {/* Fallback obsah pro SSR, který bude zobrazen při statickém generování */}
      <div className="sr-only sm:not-sr-only sm:hidden">{fallbackContent}</div>

      {/* Klientská komponenta s animací */}
      <TypewriterText
        staticText={staticText}
        words={words}
        typingSpeed={typingSpeed}
        deletingSpeed={deletingSpeed}
        delayBetweenWords={delayBetweenWords}
      />
    </>
  );
}

export default AnimatedHeading;
