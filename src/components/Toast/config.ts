import { toast } from "react-toastify";
import { ToastOptions } from "react-toastify";

interface ToastStyleConfig {
  className: string;
  progressClassName: string;
  style: {
    backgroundColor: string;
    borderColor: string;
    opacity: number;
  };
}

export const toastStyles: Record<string, ToastStyleConfig> = {
  success: {
    className: "!bg-emerald-100 !border !border-emerald-200 !rounded-lg",
    progressClassName: "!bg-emerald-500",
    style: {
      backgroundColor: "rgb(209 250 229) !important", // emerald-100
      borderColor: "rgb(167 243 208) !important", // emerald-200
      opacity: 1,
    },
  },
  error: {
    className: "!bg-red-100 !border !border-red-200 !rounded-lg",
    progressClassName: "!bg-red-500",
    style: {
      backgroundColor: "rgb(254 226 226) !important", // red-100
      borderColor: "rgb(254 202 202) !important", // red-200
      opacity: 1,
    },
  },
  warning: {
    className: "!bg-yellow-100 !border !border-yellow-200 !rounded-lg",
    progressClassName: "!bg-yellow-500",
    style: {
      backgroundColor: "rgb(254 249 195) !important", // yellow-100
      borderColor: "rgb(254 240 138) !important", // yellow-200
      opacity: 1,
    },
  },
  info: {
    className: "!bg-blue-100 !border !border-blue-200 !rounded-lg",
    progressClassName: "!bg-blue-500",
    style: {
      backgroundColor: "rgb(219 234 254) !important", // blue-100
      borderColor: "rgb(191 219 254) !important", // blue-200
      opacity: 1,
    },
  },
};

export const showToast = (
  message: string,
  type: "success" | "error" | "warning" | "info" = "success",
) => {
  const options: ToastOptions = {
    ...toastStyles[type],
    position: "top-right",
    autoClose: 4000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
  };

  toast[type](message, options);
};
