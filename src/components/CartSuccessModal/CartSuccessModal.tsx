import { X, ShoppingC<PERSON>, ArrowRight } from "lucide-react";

interface Props {
  isOpen: boolean;
  onClose: () => void;
  onGoToCart: () => void;
  productName: string;
}

export const CartSuccessModal = ({
  isOpen,
  onClose,
  onGoToCart,
  productName,
}: Props) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center px-6">
      <div
        className="fixed inset-0 bg-black/50"
        onClick={onClose}
        aria-hidden="true"
      />
      <div className="relative bg-white shadow-xl rounded-2xl p-8 max-w-md w-full">
        <button
          onClick={onClose}
          className="absolute right-4 top-4 text-gray-400 hover:text-gray-600"
        >
          <X className="w-5 h-5" />
        </button>

        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="bg-emerald-100 rounded-full p-3">
              <ShoppingCart className="w-6 h-6 text-emerald-600" />
            </div>
          </div>
          <h2 className="text-2xl font-bold text-gray-900">
            Přidáno do košíku
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            {productName} byl úspěšně přidán do košíku
          </p>
        </div>

        <div className="space-y-3">
          <button
            onClick={onGoToCart}
            className="flex items-center justify-center w-full gap-2 px-4 py-2.5 text-white bg-emerald-600 rounded-lg hover:bg-emerald-700 transition-colors"
          >
            <ShoppingCart className="w-5 h-5" />
            <span>Přejít do košíku</span>
            <ArrowRight className="w-4 h-4" />
          </button>

          <button
            onClick={onClose}
            className="flex items-center justify-center w-full gap-2 px-4 py-2.5 text-emerald-600 bg-white border border-emerald-600 rounded-lg hover:bg-emerald-50 transition-colors"
          >
            <span>Pokračovat v nákupu</span>
          </button>
        </div>
      </div>
    </div>
  );
};
