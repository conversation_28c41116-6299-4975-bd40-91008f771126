# Prepni na aktualni verzi node
ze souboru .nvmrc
```
Use nvm to Switch Node.js Version  
$ nvm use

Install Node.js Version: 
$ nvm install
```


# Vypsani session uzivatele

```
https://local-www.aktualnitesty.cz/api/auth/session
```

## TIP: ziskat session prihlaseneho studena na strane clienta
```
export default function LoginPage() {
    const { data, status } = useSession();
    const session = data as ExtendedDefaultSession;

    console.log("Session:", data);
    console.log("User:", data?.user);
    console.log("Status:", status);
    
    ...
}
```
## TIP: Jak ze strany klienta zavolat SSR mutaci, aby nebyla videt v console.
Vytvorit slozku api/auth/register/route.ts
```
import { NextResponse } from "next/server";
import { getClient } from "@/lib/client";
import { REGISTER_MUTATION } from "@/app/api/auth/register/constants/getRegistrationMutation";
import { ApolloError } from "@apollo/client";

export async function POST(request: Request) {
  let message = "";
  try {
    const { email, password } = await request.json();

    const client = getClient();
    const { data } = await client.mutate({
      mutation: REGISTER_MUTATION,
      variables: {
        email: email,
        password: password,
      },
    });

    message = data.register;
    console.log('message: ', message);
  } catch (e: unknown) {
    if (e instanceof ApolloError) {
      console.error('ApolloError: ', e.message);
      return NextResponse.json({ message: e.message }, { status: 400 });
    }
  }

  return NextResponse.json({ message: `success registration ${message}.` });
}

```
Na strane klienta zavolat
```
const response = await fetch("/api/auth/register", {
    method: "POST",
    headers: {
        "Content-Type": "application/json",
    },
    body: JSON.stringify({ email, password }),
});
```
## TIP: jak ziskat session prihlaseneho studenta na strane serveru (SSR)
```
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth.config";
import { ExtendedDefaultSession } from "@/types/extendedDefaultSession";

export default async function vopPage() {
  const session = (await getServerSession(
    authConfig,
  )) as ExtendedDefaultSession;
  console.log("Token", session?.user?.token);
  console.log("Email", session?.user?.email);
}
```
## Testovaci build stranek na lokalu
Na konci je vypis jakym zpusobem se zbuildi jednotlive stranky.

> $ next build
```
Route (app)                              Size     First Load JS
┌ ○ /                                    2.12 kB         116 kB
├ ○ /_not-found                          896 B           101 kB
├ ● /[application]/category              2.11 kB         116 kB
├   ├ /zbrojni-prukaz/category
├   ├ /autoskola/category
├   ├ /potapecsky-prukaz/category
├   └ /straznik/category
├   └ /kapitanske-zkousky/category
├ ƒ /api/auth/[...nextauth]              287 B           100 kB
...
○  (Static)   prerendered as static content
●  (SSG)      prerendered as static HTML (uses generateStaticParams)
ƒ  (Dynamic)  server-rendered on demand
```
## Spusteni produkcniho vyvoje na Lokalu
Pokud chcete testovat cachování obrázků, spustit aplikaci v produkčním módu pomocí příkazu next build a next start.
- bude potreeba mit zmenene v client.ts na `process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = "0";`
- pokud bezi local prvne `make dev-stop`
- Pred spustenim zase dev vymazat .next a make dev-rebuild

```
$ next build & next start
spusti se na http://localhost:3000
```
## SSR - Povine generovat stranky na serveru. 
> Pri buildu se tyto stranky negeneruji.

> Vynutí, aby byla stránka renderována na straně serveru při každém požadavku
```export const dynamic = "force-ssr";```

## Dynamic - Stranka je pro kazdeho studenta jina.
>  Vynutí, aby byla stránka dynamicky renderována (buď SSR nebo na straně klienta)

```export const dynamic = "force-dynamic";```

## Static - povine staticky generovat stranky uz pri buildu.
```export const dynamic = "force-static"```