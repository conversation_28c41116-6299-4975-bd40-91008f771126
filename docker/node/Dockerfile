# Stage 1: Build
FROM --platform=linux/arm64 arm64v8/node:23.3.0 AS builder

# Set the working directory inside the container
WORKDIR /usr/src

# Update npm to latest version
RUN npm install -g npm@latest

# Install Apollo CLI
RUN curl -sSL https://rover.apollo.dev/nix/latest | sh && \
    npm install -g apollo graphql@16

# Copy package.json and package-lock.json to the working directory
COPY package.json package-lock.json ./

ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_ENV production
ENV NEXT_PUBLIC_GRAPHQL_HOST https://admin.aktualnitesty.cz/graphql

# Copy the rest of the application code
COPY . .

COPY .env.production .env.production

# Install dependencies including devDependencies
RUN npm install --include=dev --legacy-peer-deps

# Build the Next.js application
RUN npm run build

# Stage 2: Production
FROM --platform=linux/arm64 arm64v8/node:23.3.0

# Set the working directory inside the container
WORKDIR /usr/src

# Copy only the necessary files from the build stage
COPY --from=builder /usr/src/package.json /usr/src/package-lock.json ./
COPY --from=builder /usr/src/.next ./.next
COPY --from=builder /usr/src/public ./public
COPY --from=builder /usr/src/.env.production .env.production
COPY --from=builder /usr/src/next.config.js ./

# Install only production dependencies
RUN npm install --production --legacy-peer-deps

# Expose the application port
EXPOSE 3000

# Define the command to run the application
CMD ["npm", "run", "start"]
