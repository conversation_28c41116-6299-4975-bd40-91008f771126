FROM --platform=linux/arm64 arm64v8/node:23.3.0

WORKDIR /usr/src

# Update npm to latest version
RUN npm install -g npm@latest

# Instalace Rover CLI s explicitním přidáním do PATH
RUN curl -sSL https://rover.apollo.dev/nix/latest | sh
ENV PATH="/root/.rover/bin:${PATH}"

# Install Apollo CLI
RUN npm install -g apollo graphql@16

# Copy package.json and package-lock.json to the working directory
COPY package.json package-lock.json ./

ENV NEXT_TELEMETRY_DISABLED 1
ENV NEXT_PUBLIC_GRAPHQL_HOST https://local-admin.aktualnitesty.cz/graphql

# Install dependencies
RUN npm install --legacy-peer-deps

# Expose the application port
EXPOSE 3000

# Define the command to run the application
CMD ["npm", "run", "dev"]
