networks:
  docker-ci-network:
    external: true
    name: docker-ci-network

services:
  docker-ci-web:
    platform: linux/arm64
    build:
      context: .
      dockerfile: docker/node/Dockerfile
    container_name: docker-ci-web
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**************************************/mydatabase
    networks:
      docker-ci-network:
        ipv4_address: **********
