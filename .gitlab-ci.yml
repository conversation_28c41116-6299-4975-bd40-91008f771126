stages:
    - build
    - image-test
    - deploy

default:
    image: docker:latest
    services:
        - name: docker:dind

variables:
    DOCKER_HOST: "tcp://docker:2375"
    DOCKER_TLS_CERTDIR: ""

.docker_template: &template
    tags:
        - aws-ec2-arm.t4g.medium

build_docker_image:
    <<: *template
    stage: build
    resource_group: production
    # only:
    #     - main
    before_script:
        - docker version
        - cat $next_env_file > .env.production
    variables:
        DOCKER_DEFAULT_PLATFORM: linux/arm64
    script:
        - echo "Building the project....."
        - docker login registry.gitlab.com -u hrdlickama -p $NEXT_TOKEN
        - docker compose -f docker-compose.yml build
        - docker tag webzkousky-docker-ci-web registry.gitlab.com/hrdlicka/webzkousky:next-$CI_PIPELINE_IID
        - docker push registry.gitlab.com/hrdlicka/webzkousky:next-$CI_PIPELINE_IID

    artifacts:
        paths:
            - $CI_PROJECT_DIR/

test_docker_image:
    <<: *template
    stage: image-test
    resource_group: production
    script:
        - docker login registry.gitlab.com -u hrdlickama -p $NEXT_TOKEN
        - docker pull registry.gitlab.com/hrdlicka/webzkousky:next-$CI_PIPELINE_IID
        - docker image ls
include:
    - local: 'deploy_docker_image_prod.yml'
