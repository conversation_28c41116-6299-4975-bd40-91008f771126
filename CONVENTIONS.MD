# Coding Conventions for the TypeScript/React Codebase                   
                                                                          
 This document outlines the coding conventions used throughout the projec 
 Adhering to these guidelines ensures consistency, readability, and       
 maintainability. This document is intended for Aider usage and future    
 contributors.                                                            
                                                                          
 ## General Guidelines                                                    
 - Use TypeScript with strict type checking enabled.                      
 - Follow modern ECMAScript/TypeScript syntax and use ES modules.         
 - Code formatting is enforced via <PERSON>SL<PERSON> and Prettier (if configured).   
 - Use `strict mode` by default.                                          
                                                                          
 ## File Structure and Naming Conventions                                 
 - **File Names:** Use kebab-case or PascalCase (e.g., `CourseDetail.tsx` 
 `useMicrolearning.ts`).                                                  
 - **Components:** React components use PascalCase (e.g., `CourseDetail`, 
 `PriceListIntro`).                                                       
 - **Custom Hooks:** Use the `use` prefix with camelCase (e.g.,           
 `useTranslate`, `useMediaPlayer`).                                       
 - **Functions and Variables:** Use camelCase.                            
 - **Constants:** Use UPPER_SNAKE_CASE (e.g.,                             
 `COLLECT_LEADS_FROM_COMPANIES`).                                         
                                                                          
 ## Import Style                                                          
 - **Grouping:** Import external dependencies first, followed by internal 
 modules.                                                                 
 - **Ordering:** Order import statements alphabetically within groups.    
 - **Type Imports:** Prefer `import type` when importing type definitions 
 - **Path Usage:** Use relative paths for local modules.                  
                                                                          
 ## Docstrings and Comments                                               
 - Use JSDoc style comments to document functions, components, and custom 
 types.                                                                   
 - Provide brief descriptions above functions and components, including   
 parameter and return type details.                                       
 - Use inline comments sparingly and only to clarify complex logic.       
                                                                          
 ## React and JSX Guidelines                                              
 - Use functional components and hooks exclusively.                       
 - Clearly type component props using TypeScript interfaces or types.     
 - Destructure props in function components where appropriate.            
 - Include explicit return types for components and hooks when needed.    
                                                                          
 ## State Management and Contexts                                         
 - Use React Contexts (e.g., MicrolearningProvider, QuizItemProvider) for 
 shared state.                                                            
 - Manage complex state with reducers and clearly defined action types.   
 - Ensure that hooks and state management functions are fully typed.      
                                                                          
 ## Testing Practices                                                     
 - Test files follow the `.test.tsx` naming convention.                   
 - Use accessible attributes (e.g., `data-testid`) to facilitate componen 
 testing with tools like React Testing Library.                           
 - We prefer placing test files in `src/tests` or in the same folder as the tested component if more convenient.
 - We mock external dependencies with Jest as needed (see examples in the test files).
 - `.stories.tsx` files are used for Storybook, typically placed alongside their components.
                                                                          
 ## Additional Guidelines                                                 
 - When using CSS/SCSS, prefer CSS modules for scoped styling.            
 - Follow the project's design system guidelines where applicable.        
 - For GraphQL interactions, import queries and mutations with proper typ 
 definitions.                                                             
 - Maintain clear, concise, and self-documenting code with meaningful     
 variable and function names.                                             
 - Use Next.js features (like useRouter) with caution to maintain a consistent architecture.
 - Place new UI components in `.stories.tsx` files for Storybook usage.
 - Prefer functional components with explicit typed props (using either `interface` or `type`).
 - Follow the project's approach of default exporting React components, or named exports where necessary.
